class AddUniqueIndexToPredictedCloudTransactions < ActiveRecord::Migration[7.0]
  def up
    # Add unique index for PredictedCloudTransaction upsert_all
    add_index :predicted_cloud_transactions,
              [:company_id, :date, :name, :company_integration_id],
              unique: true,
              name: 'unique_predicted_cloud_transactions'
  end

  def down
    # Remove the unique index
    remove_index :predicted_cloud_transactions, name: 'unique_predicted_cloud_transactions'
  end
end
