class MakeCloudUsageTransactionsIndexUnique < ActiveRecord::Migration[7.0]
  def up
    # Remove the existing non-unique index
    remove_index :cloud_usage_transactions, name: 'index_cloud_usage_transactions_multiple'
    
    # Add the same index but with unique constraint for upsert_all
    add_index :cloud_usage_transactions, 
              [:company_id, :transaction_date, :name, :company_integration_id, :is_manual], 
              unique: true, 
              name: 'index_cloud_usage_transactions_multiple'
  end

  def down
    # Remove the unique index
    remove_index :cloud_usage_transactions, name: 'index_cloud_usage_transactions_multiple'
    
    # Add back the non-unique index
    add_index :cloud_usage_transactions, 
              [:company_id, :transaction_date, :name, :company_integration_id, :is_manual], 
              name: 'index_cloud_usage_transactions_multiple'
  end
end
