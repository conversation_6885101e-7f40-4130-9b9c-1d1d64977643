# Benchmark script for Azure Worker
# Run in Rails console: load 'scripts/benchmark_azure_worker.rb'

require 'benchmark'

puts "Azure Worker Performance Benchmark"
puts "="*50

# Setup test data
company = Company.first || Company.create!(name: 'Test Company', subdomain: 'test')
vendor = company.vendors.find_or_create_by(name: 'Microsoft Azure')
product = vendor.products.find_or_create_by(name: 'Azure Services', company: company)
company_integration = company.company_integrations.first || 
                     company.company_integrations.create!(
                       integration: Integration.find_or_create_by(name: 'azure'),
                       status: true
                     )

# Create different sized datasets
test_sizes = [10, 50, 100, 500]

test_sizes.each do |size|
  puts "\n" + "-"*30
  puts "Testing with #{size} records"
  puts "-"*30
  
  # Generate mock data
  mock_data = size.times.map do |i|
    {
      "properties" => {
        "date" => (Date.current - rand(30).days).to_s,
        "consumedService" => "Service-#{i}",
        "costInBillingCurrency" => rand(10.0..100.0).round(2),
        "cost" => rand(10.0..100.0).round(2)
      }
    }
  end
  
  # Setup worker
  worker = Integrations::Azure::SaveUsageTransactionsWorker.new
  worker.instance_variable_set(:@company, company)
  worker.instance_variable_set(:@company_integration, company_integration)
  worker.instance_variable_set(:@company_integration_id, company_integration.id)
  worker.instance_variable_set(:@vendor_id, vendor.id)
  worker.instance_variable_set(:@product_id, product.id)
  
  # Clean up previous test data
  CloudUsageTransaction.where(company: company, name: /Service-/).delete_all
  
  # Benchmark
  result = Benchmark.measure do
    worker.send(:save_usage_transactions, mock_data)
  end
  
  puts "Time: #{(result.real * 1000).round(2)}ms"
  puts "Per record: #{((result.real * 1000) / size).round(2)}ms"
  puts "Records created: #{CloudUsageTransaction.where(company: company, name: /Service-/).count}"
end

puts "\n" + "="*50
puts "Benchmark completed!"
puts "Look for log messages starting with '[AZURE WORKER OPTIMIZED]'"
