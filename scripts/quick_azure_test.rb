# Quick Azure Worker Test
# Usage: rails runner scripts/quick_azure_test.rb
# Or in Rails console: load 'scripts/quick_azure_test.rb'

puts "⚡ Quick Azure Worker Test"
puts "=" * 40

# Configuration
COMPANY_EMAIL = "<EMAIL>"

# Find company
company = Company.find_by(email: COMPANY_EMAIL)
unless company
  puts "❌ Company with email '#{COMPANY_EMAIL}' not found"
  exit
end

puts "✅ Using company: #{company.name} (ID: #{company.id})"

# Setup test data
vendor = company.vendors.find_or_create_by(name: 'Quick Test Vendor') do |v|
  v.is_cloud_platform = true
end

product = vendor.products.find_or_create_by(name: 'Quick Test Product') do |p|
  p.company = company
end

integration = Integration.find_or_create_by(name: 'quick_test')
company_integration = company.company_integrations.find_or_create_by(integration: integration) do |ci|
  ci.status = true
  ci.sync_status = :successful
end

# Create simple mock data
mock_data = [
  {
    "properties" => {
      "date" => "2024-01-15",
      "consumedService" => "Virtual Machines",
      "costInBillingCurrency" => 45.67,
      "cost" => 45.67
    }
  },
  {
    "properties" => {
      "date" => "2024-01-15",
      "consumedService" => "Storage",
      "costInBillingCurrency" => 12.34,
      "cost" => 12.34
    }
  },
  {
    "properties" => {
      "date" => "2024-01-16",
      "consumedService" => "Networking",
      "costInBillingCurrency" => 23.45,
      "cost" => 23.45
    }
  }
]

# Clean up previous test data
CloudUsageTransaction.where(
  company: company,
  vendor: vendor,
  name: ["Virtual Machines", "Storage", "Networking"]
).delete_all

puts "\n🧪 Running quick test with #{mock_data.size} records..."

# Setup worker
worker = Integrations::Azure::SaveUsageTransactionsWorker.new
worker.instance_variable_set(:@company, company)
worker.instance_variable_set(:@company_integration, company_integration)
worker.instance_variable_set(:@company_integration_id, company_integration.id)
worker.instance_variable_set(:@vendor_id, vendor.id)
worker.instance_variable_set(:@product_id, product.id)

# Test execution
initial_count = CloudUsageTransaction.where(company: company, vendor: vendor).count

start_time = Time.current
worker.send(:save_usage_transactions, mock_data)
end_time = Time.current

final_count = CloudUsageTransaction.where(company: company, vendor: vendor).count
duration = ((end_time - start_time) * 1000).round(2)

# Results
puts "\n📊 Results:"
puts "  ⏱️  Processing time: #{duration}ms"
puts "  📝 Records processed: #{mock_data.size}"
puts "  ➕ Records created: #{final_count - initial_count}"
puts "  ⚡ Avg per record: #{(duration / mock_data.size).round(2)}ms"

if duration < 50
  puts "  ✅ EXCELLENT: Very fast processing!"
elsif duration < 200
  puts "  ✅ GOOD: Fast processing"
else
  puts "  ⚠️  SLOW: May need further optimization"
end

# Show created records (without the slow query from before)
puts "\n🔍 Created #{final_count - initial_count} transactions:"
CloudUsageTransaction.where(company: company, vendor: vendor)
                    .limit(5)
                    .pluck(:transaction_date, :name, :amount)
                    .each do |date, name, amount|
  puts "  - #{date} | #{name} | $#{amount}"
end

puts "\n✅ Quick test completed!"

# Performance check
if duration < 100
  puts "🎯 Performance: OPTIMIZED - No slow COUNT queries detected!"
else
  puts "⚠️  Performance: Check for potential issues"
end
