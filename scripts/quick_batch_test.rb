puts "Quick Batch Processing Test"
puts "=" * 40

COMPANY_EMAIL = "<EMAIL>"
TEST_RECORD_COUNT = 2000

company = Company.find_by(email: COMPANY_EMAIL)
unless company
  puts "Company with email '#{COMPANY_EMAIL}' not found"
  exit
end

puts "Using company: #{company.name} (ID: #{company.id})"

vendor = company.vendors.find_or_create_by(name: 'Quick Batch Vendor') do |v|
  v.is_cloud_platform = true
end

product = vendor.products.find_or_create_by(name: 'Quick Batch Product') do |p|
  p.company = company
end

integration = Integration.find_or_create_by(name: 'quick_batch_test')
company_integration = company.company_integrations.find_or_create_by(integration: integration) do |ci|
  ci.status = true
  ci.sync_status = :successful
end

mock_data = TEST_RECORD_COUNT.times.map do |i|
  {
    "properties" => {
      "date" => (Date.current - rand(5).days).to_s,
      "consumedService" => "BatchService_#{i}",
      "costInBillingCurrency" => rand(10.0..100.0).round(2),
      "cost" => rand(10.0..100.0).round(2)
    }
  }
end

CloudUsageTransaction.where(
  company: company,
  vendor: vendor
).where("name LIKE ?", "%BatchService_%").delete_all

puts "\nTesting batch processing with #{TEST_RECORD_COUNT} records..."

worker = Integrations::Azure::SaveUsageTransactionsWorker.new
worker.instance_variable_set(:@company, company)
worker.instance_variable_set(:@company_integration, company_integration)
worker.instance_variable_set(:@company_integration_id, company_integration.id)
worker.instance_variable_set(:@vendor_id, vendor.id)
worker.instance_variable_set(:@product_id, product.id)

query_count = 0
subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions')
    query_count += 1
    puts "  Query #{query_count}: #{sql.split(' ').first(6).join(' ')}..."
  end
end

initial_count = CloudUsageTransaction.where(company: company, vendor: vendor).count

start_time = Time.current
worker.send(:save_usage_transactions, mock_data)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(subscription)

final_count = CloudUsageTransaction.where(company: company, vendor: vendor).count
duration = ((end_time - start_time) * 1000).round(2)

puts "\n Batch Processing Results:"
puts "   Processing time: #{duration}ms"
puts "   Records processed: #{TEST_RECORD_COUNT}"
puts "   Records created: #{final_count - initial_count}"
puts "   Database queries: #{query_count}"
puts "   Queries per record: #{(query_count.to_f / TEST_RECORD_COUNT).round(2)}"

puts "\nPerformance Analysis:"
if query_count <= 5
  puts "  OUTSTANDING: Excellent batch processing!"
  puts "  Achieved ~#{((TEST_RECORD_COUNT * 2 - query_count).to_f / (TEST_RECORD_COUNT * 2) * 100).round(1)}% query reduction"
elsif query_count < TEST_RECORD_COUNT
  puts "  EXCELLENT: Batch optimization working!"
  puts "  Achieved ~#{((TEST_RECORD_COUNT * 2 - query_count).to_f / (TEST_RECORD_COUNT * 2) * 100).round(1)}% query reduction"
else
  puts "  NEEDS REVIEW: Expected fewer queries with batch processing"
end

puts "\nExpected vs Actual:"
puts "  Old approach would use: #{TEST_RECORD_COUNT * 2} queries (2 per record)"
puts "  Batch approach used: #{query_count} queries"
puts "  Improvement: #{TEST_RECORD_COUNT * 2 - query_count} fewer queries"

puts "\nTesting mixed operations (updates + new records)..."

update_data = mock_data.first(10).map do |record|
  record.merge(
    "properties" => record["properties"].merge(
      "costInBillingCurrency" => record["properties"]["costInBillingCurrency"] * 1.5
    )
  )
end

new_data = 5.times.map do |i|
  {
    "properties" => {
      "date" => Date.current.to_s,
      "consumedService" => "NewBatchService_#{i}",
      "costInBillingCurrency" => rand(50.0..150.0).round(2),
      "cost" => rand(50.0..150.0).round(2)
    }
  }
end

mixed_data = update_data + new_data

mixed_query_count = 0
mixed_subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions')
    mixed_query_count += 1
  end
end

start_time = Time.current
worker.send(:save_usage_transactions, mixed_data)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(mixed_subscription)

mixed_duration = ((end_time - start_time) * 1000).round(2)

puts "\nMixed Operations Results:"
puts "  Processing time: #{mixed_duration}ms"
puts "  Records processed: #{mixed_data.size}"
puts "  Database queries: #{mixed_query_count}"
puts "  Queries per record: #{(mixed_query_count.to_f / mixed_data.size).round(2)}"

puts "\nBatch processing test completed!"

if query_count <= 5 && mixed_query_count <= 10
  puts "RESULT: Batch optimization is working perfectly! 🚀"
else
  puts "RESULT: May need further optimization"
end
