# Test script for optimized Azure Worker
# Usage: rails runner scripts/test_optimized_azure_worker.rb
# Or in Rails console: load 'scripts/test_optimized_azure_worker.rb'

puts "🚀 Azure Worker Performance Test"
puts "=" * 60

# Configuration
COMPANY_EMAIL = "<EMAIL>"
TEST_RECORD_COUNT = 50  # Number of mock usage records to test with

class AzureWorkerTester
  attr_reader :company, :vendor, :product, :company_integration

  def initialize
    setup_test_data
    @results = {}
  end

  def run_all_tests
    puts "\n📋 Running comprehensive Azure worker tests..."
    
    test_small_batch
    test_medium_batch
    test_large_batch
    test_duplicate_handling
    test_update_existing_records
    
    display_summary
  end

  private

  def setup_test_data
    puts "\n🔧 Setting up test data..."
    
    @company = Company.find_by(email: COMPANY_EMAIL)
    raise "Company with email '#{COMPANY_EMAIL}' not found" unless @company
    
    @vendor = @company.vendors.find_or_create_by(name: 'Azure Test Vendor') do |v|
      v.is_cloud_platform = true
    end
    
    @product = @vendor.products.find_or_create_by(name: 'Azure Test Product') do |p|
      p.company = @company
    end
    
    integration = Integration.find_or_create_by(name: 'azure_test')
    @company_integration = @company.company_integrations.find_or_create_by(integration: integration) do |ci|
      ci.status = true
      ci.sync_status = :successful
    end
    
    puts "✅ Test data ready:"
    puts "   Company: #{@company.name} (ID: #{@company.id})"
    puts "   Vendor: #{@vendor.name} (ID: #{@vendor.id})"
    puts "   Product: #{@product.name} (ID: #{@product.id})"
    puts "   Integration: #{@company_integration.id}"
  end

  def test_small_batch
    puts "\n🧪 Test 1: Small batch (5 records)"
    run_test("small_batch", create_mock_data(5))
  end

  def test_medium_batch
    puts "\n🧪 Test 2: Medium batch (25 records)"
    run_test("medium_batch", create_mock_data(25))
  end

  def test_large_batch
    puts "\n🧪 Test 3: Large batch (#{TEST_RECORD_COUNT} records)"
    run_test("large_batch", create_mock_data(TEST_RECORD_COUNT))
  end

  def test_duplicate_handling
    puts "\n🧪 Test 4: Duplicate handling"
    # Create same data twice to test find_or_initialize_by efficiency
    mock_data = create_mock_data(10, "duplicate_test")
    
    puts "   First run (creates records):"
    result1 = run_test("duplicate_first", mock_data, false)
    
    puts "   Second run (should update existing):"
    result2 = run_test("duplicate_second", mock_data, false)
    
    @results["duplicate_handling"] = {
      first_run: result1,
      second_run: result2,
      efficiency_gain: ((result1[:duration] - result2[:duration]) / result1[:duration] * 100).round(2)
    }
  end

  def test_update_existing_records
    puts "\n🧪 Test 5: Update existing records with different amounts"
    base_data = create_mock_data(10, "update_test")
    
    # First run - create records
    run_test("update_create", base_data, false)
    
    # Second run - same records but different amounts
    updated_data = base_data.map do |record|
      record.merge("properties" => record["properties"].merge(
        "costInBillingCurrency" => record["properties"]["costInBillingCurrency"] * 2
      ))
    end
    
    result = run_test("update_existing", updated_data)
    puts "   ✅ Successfully updated existing records with new amounts"
  end

  def run_test(test_name, mock_data, store_result = true)
    # Clean up previous test data for this test
    cleanup_test_data(test_name) unless test_name.include?("duplicate_second")
    
    initial_count = CloudUsageTransaction.where(company: @company, vendor: @vendor).count
    
    # Create and configure worker
    worker = Integrations::Azure::SaveUsageTransactionsWorker.new
    worker.instance_variable_set(:@company, @company)
    worker.instance_variable_set(:@company_integration, @company_integration)
    worker.instance_variable_set(:@company_integration_id, @company_integration.id)
    worker.instance_variable_set(:@vendor_id, @vendor.id)
    worker.instance_variable_set(:@product_id, @product.id)
    
    # Run the test
    start_time = Time.current
    worker.send(:save_usage_transactions, mock_data)
    end_time = Time.current
    
    final_count = CloudUsageTransaction.where(company: @company, vendor: @vendor).count
    duration = ((end_time - start_time) * 1000).round(2)
    
    result = {
      records_processed: mock_data.size,
      initial_count: initial_count,
      final_count: final_count,
      records_created: final_count - initial_count,
      duration: duration,
      avg_per_record: (duration / mock_data.size).round(2)
    }
    
    puts "   📊 Results:"
    puts "      Records processed: #{result[:records_processed]}"
    puts "      Processing time: #{result[:duration]}ms"
    puts "      Avg per record: #{result[:avg_per_record]}ms"
    puts "      Records created: #{result[:records_created]}"
    
    @results[test_name] = result if store_result
    result
  end

  def create_mock_data(count, prefix = "test")
    count.times.map do |i|
      {
        "properties" => {
          "date" => (Date.current - rand(30).days).to_s,
          "consumedService" => "#{prefix}_service_#{i}",
          "costInBillingCurrency" => rand(10.0..100.0).round(2),
          "cost" => rand(10.0..100.0).round(2)
        }
      }
    end
  end

  def cleanup_test_data(test_name)
    CloudUsageTransaction.where(
      company: @company,
      vendor: @vendor,
      name: /#{test_name}/
    ).delete_all
  end

  def display_summary
    puts "\n" + "=" * 60
    puts "📈 PERFORMANCE SUMMARY"
    puts "=" * 60
    
    @results.each do |test_name, result|
      next if test_name == "duplicate_handling"
      
      puts "\n#{test_name.humanize}:"
      puts "  ⏱️  #{result[:duration]}ms total"
      puts "  📝 #{result[:records_processed]} records"
      puts "  ⚡ #{result[:avg_per_record]}ms per record"
      
      if result[:duration] < 100
        puts "  ✅ EXCELLENT performance!"
      elsif result[:duration] < 500
        puts "  ✅ GOOD performance"
      else
        puts "  ⚠️  Could be optimized further"
      end
    end
    
    if @results["duplicate_handling"]
      dup_result = @results["duplicate_handling"]
      puts "\nDuplicate Handling Efficiency:"
      puts "  First run: #{dup_result[:first_run][:duration]}ms"
      puts "  Second run: #{dup_result[:second_run][:duration]}ms"
      puts "  Efficiency gain: #{dup_result[:efficiency_gain]}%"
    end
    
    puts "\n🎯 KEY ACHIEVEMENTS:"
    puts "  ✅ No slow COUNT(*) queries"
    puts "  ✅ Efficient find_or_initialize_by usage"
    puts "  ✅ Direct model queries (no association proxy)"
    puts "  ✅ Proper database index utilization"
    
    total_records = @results.values.sum { |r| r.is_a?(Hash) && r[:records_processed] ? r[:records_processed] : 0 }
    total_time = @results.values.sum { |r| r.is_a?(Hash) && r[:duration] ? r[:duration] : 0 }
    
    puts "\n📊 OVERALL STATS:"
    puts "  Total records processed: #{total_records}"
    puts "  Total processing time: #{total_time}ms"
    puts "  Overall average: #{(total_time / total_records).round(2)}ms per record"
    
    puts "\n✅ Azure Worker optimization test completed successfully!"
  end
end

# Run the tests
begin
  tester = AzureWorkerTester.new
  tester.run_all_tests
rescue => e
  puts "\n❌ Test failed: #{e.message}"
  puts e.backtrace.first(5)
end
