puts " Clean UPSERT_ALL Test"
puts "=" * 40

COMPANY_EMAIL = "<EMAIL>"

company = Company.find_by(email: COMPANY_EMAIL)
unless company
  puts " Company with email '#{COMPANY_EMAIL}' not found"
  exit
end

puts " Using company: #{company.name} (ID: #{company.id})"

vendor = company.vendors.find_or_create_by(name: 'Clean Upsert Vendor') do |v|
  v.is_cloud_platform = true
end

product = vendor.products.find_or_create_by(name: 'Clean Upsert Product') do |p|
  p.company = company
end

integration = Integration.find_or_create_by(name: 'clean_upsert_test')
company_integration = company.company_integrations.find_or_create_by(integration: integration) do |ci|
  ci.status = true
  ci.sync_status = :successful
end
test_data = [
  {
    "properties" => {
      "date" => "2024-01-15",
      "consumedService" => "Virtual Machines",
      "costInBillingCurrency" => 45.67,
      "cost" => 45.67
    }
  },
  {
    "properties" => {
      "date" => "2024-01-15", 
      "consumedService" => "Storage",
      "costInBillingCurrency" => 12.34,
      "cost" => 12.34
    }
  },
  {
    "properties" => {
      "date" => "2024-01-16",
      "consumedService" => "Networking",
      "costInBillingCurrency" => 23.45,
      "cost" => 23.45
    }
  }
]

CloudUsageTransaction.where(
  company: company,
  vendor: vendor,
  name: ["Virtual Machines", "Storage", "Networking"]
).delete_all

puts "\n Testing clean upsert_all with #{test_data.size} records..."

worker = Integrations::Azure::SaveUsageTransactionsWorker.new
worker.instance_variable_set(:@company, company)
worker.instance_variable_set(:@company_integration, company_integration)
worker.instance_variable_set(:@company_integration_id, company_integration.id)
worker.instance_variable_set(:@vendor_id, vendor.id)
worker.instance_variable_set(:@product_id, product.id)

query_count = 0
subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions') && !sql.include?('DELETE')
    query_count += 1
    puts "  🔍 Query #{query_count}: #{sql.split(' ').first(4).join(' ')}..."
  end
end

initial_count = CloudUsageTransaction.where(company: company, vendor: vendor).count

start_time = Time.current
worker.send(:save_usage_transactions, test_data)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(subscription)

final_count = CloudUsageTransaction.where(company: company, vendor: vendor).count
duration = ((end_time - start_time) * 1000).round(2)

puts "\n Results:"
puts "   Processing time: #{duration}ms"
puts "  Records processed: #{test_data.size}"
puts "  Records created: #{final_count - initial_count}"
puts "  Database queries: #{query_count}"

if query_count == 1
  puts "   PERFECT: Single upsert_all query!"
elsif query_count <= 2
  puts "  EXCELLENT: Very efficient!"
else
  puts "   UNEXPECTED: More queries than expected"
end

puts "\n Testing updates with same records..."

update_data = test_data.map do |record|
  record.merge(
    "properties" => record["properties"].merge(
      "costInBillingCurrency" => record["properties"]["costInBillingCurrency"] * 2
    )
  )
end
update_query_count = 0
update_subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions') && !sql.include?('DELETE')
    update_query_count += 1
    puts "  🔍 Query #{update_query_count}: #{sql.split(' ').first(4).join(' ')}..."
  end
end

start_time = Time.current
worker.send(:save_usage_transactions, update_data)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(update_subscription)

update_duration = ((end_time - start_time) * 1000).round(2)

puts "\n Update Results:"
puts "    Processing time: #{update_duration}ms"
puts "   Records processed: #{test_data.size}"
puts "   Database queries: #{update_query_count}"

if update_query_count == 1
  puts "   PERFECT: Single upsert_all query for updates!"
elsif update_query_count <= 2
  puts "   EXCELLENT: Very efficient updates!"
else
  puts "    UNEXPECTED: More queries than expected for updates"
end

puts "\n🔍 Verifying created records:"
CloudUsageTransaction.where(company: company, vendor: vendor)
                    .order(:transaction_date, :name)
                    .pluck(:transaction_date, :name, :amount)
                    .each do |date, name, amount|
  puts "  - #{date} | #{name} | $#{amount}"
end

puts "\n" + "=" * 40
puts " CLEAN UPSERT_ALL SUMMARY"
puts "=" * 40

total_queries = query_count + update_query_count

puts "\n Performance:"
puts "  Insert queries: #{query_count}"
puts "  Update queries: #{update_query_count}"
puts "  Total queries: #{total_queries}"
puts "  Expected: 2 queries (1 insert + 1 update)"

if total_queries <= 2
  puts "\n OUTSTANDING: Optimal performance achieved!"
  puts " Clean upsert_all implementation working perfectly!"
else
  puts "\n  REVIEW: More queries than expected"
end

puts "\nClean upsert_all test completed!"
