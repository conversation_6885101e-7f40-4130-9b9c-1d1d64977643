# Test script for final optimized Azure Worker
# Usage: rails runner scripts/test_final_optimization.rb

puts "🚀 Final Azure Worker Optimization Test"
puts "=" * 50

# Configuration
COMPANY_EMAIL = "<EMAIL>"

# Find company
company = Company.find_by(email: COMPANY_EMAIL)
unless company
  puts "❌ Company with email '#{COMPANY_EMAIL}' not found"
  exit
end

puts "✅ Using company: #{company.name} (ID: #{company.id})"

# Setup test data
vendor = company.vendors.find_or_create_by(name: 'Final Test Vendor') do |v|
  v.is_cloud_platform = true
end

product = vendor.products.find_or_create_by(name: 'Final Test Product') do |p|
  p.company = company
end

integration = Integration.find_or_create_by(name: 'final_test')
company_integration = company.company_integrations.find_or_create_by(integration: integration) do |ci|
  ci.status = true
  ci.sync_status = :successful
end

# Create test data
test_data = [
  {
    "properties" => {
      "date" => "2024-01-15",
      "consumedService" => "Virtual Machines",
      "costInBillingCurrency" => 45.67,
      "cost" => 45.67
    }
  },
  {
    "properties" => {
      "date" => "2024-01-15", 
      "consumedService" => "Storage",
      "costInBillingCurrency" => 12.34,
      "cost" => 12.34
    }
  },
  {
    "properties" => {
      "date" => "2024-01-16",
      "consumedService" => "Networking",
      "costInBillingCurrency" => 23.45,
      "cost" => 23.45
    }
  }
]

# Clean up previous test data
CloudUsageTransaction.where(
  company: company,
  vendor: vendor,
  name: ["Virtual Machines", "Storage", "Networking"]
).delete_all

puts "\n🧪 Test 1: Creating new records (#{test_data.size} records)..."

# Setup worker
worker = Integrations::Azure::SaveUsageTransactionsWorker.new
worker.instance_variable_set(:@company, company)
worker.instance_variable_set(:@company_integration, company_integration)
worker.instance_variable_set(:@company_integration_id, company_integration.id)
worker.instance_variable_set(:@vendor_id, vendor.id)
worker.instance_variable_set(:@product_id, product.id)

# Count only CloudUsageTransaction queries (excluding test queries)
query_count = 0
subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions') && 
     !sql.include?('DELETE') && 
     !sql.include?('COUNT(*)')
    query_count += 1
    operation = sql.split(' ').first
    puts "  🔍 Query #{query_count}: #{operation} cloud_usage_transactions..."
  end
end

# Test execution
initial_count = CloudUsageTransaction.where(company: company, vendor: vendor).count

start_time = Time.current
worker.send(:save_usage_transactions, test_data)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(subscription)

final_count = CloudUsageTransaction.where(company: company, vendor: vendor).count
duration = ((end_time - start_time) * 1000).round(2)

# Results
puts "\n📊 Create Results:"
puts "  ⏱️  Processing time: #{duration}ms"
puts "  📝 Records processed: #{test_data.size}"
puts "  ➕ Records created: #{final_count - initial_count}"
puts "  🔍 Database queries: #{query_count}"
puts "  ⚡ Queries per record: #{(query_count.to_f / test_data.size).round(2)}"

if query_count <= test_data.size * 2
  puts "  ✅ GOOD: Efficient processing!"
else
  puts "  ⚠️  HIGH: More queries than expected"
end

# Test 2: Updates (same records with different amounts)
puts "\n🔄 Test 2: Updating existing records..."

# Modify amounts for update test
update_data = test_data.map do |record|
  record.merge(
    "properties" => record["properties"].merge(
      "costInBillingCurrency" => record["properties"]["costInBillingCurrency"] * 2
    )
  )
end

# Count queries for updates
update_query_count = 0
update_subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions') && 
     !sql.include?('DELETE') && 
     !sql.include?('COUNT(*)')
    update_query_count += 1
    operation = sql.split(' ').first
    puts "  🔍 Query #{update_query_count}: #{operation} cloud_usage_transactions..."
  end
end

start_time = Time.current
worker.send(:save_usage_transactions, update_data)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(update_subscription)

update_duration = ((end_time - start_time) * 1000).round(2)
final_count_after_update = CloudUsageTransaction.where(company: company, vendor: vendor).count

puts "\n📊 Update Results:"
puts "  ⏱️  Processing time: #{update_duration}ms"
puts "  📝 Records processed: #{test_data.size}"
puts "  📊 Total records: #{final_count_after_update} (should be #{test_data.size})"
puts "  🔍 Database queries: #{update_query_count}"
puts "  ⚡ Queries per record: #{(update_query_count.to_f / test_data.size).round(2)}"

if final_count_after_update == test_data.size
  puts "  ✅ CORRECT: No duplicate records created!"
else
  puts "  ⚠️  ISSUE: Duplicate records detected"
end

# Verify data integrity
puts "\n🔍 Verifying final records:"
records = CloudUsageTransaction.where(company: company, vendor: vendor)
                              .order(:transaction_date, :name)
                              .pluck(:transaction_date, :name, :amount)

records.each do |date, name, amount|
  puts "  - #{date} | #{name} | $#{amount}"
end

# Check for duplicates
duplicate_check = records.group_by { |date, name, _| [date, name] }
duplicates = duplicate_check.select { |_, group| group.size > 1 }

if duplicates.any?
  puts "\n⚠️  DUPLICATES FOUND:"
  duplicates.each do |key, group|
    puts "  - #{key[0]} | #{key[1]} appears #{group.size} times"
  end
else
  puts "\n✅ NO DUPLICATES: Data integrity maintained!"
end

# Summary
puts "\n" + "=" * 50
puts "🎯 FINAL OPTIMIZATION SUMMARY"
puts "=" * 50

total_queries = query_count + update_query_count
total_records = test_data.size * 2  # create + update

puts "\n📊 Performance:"
puts "  Create queries: #{query_count}"
puts "  Update queries: #{update_query_count}"
puts "  Total queries: #{total_queries}"
puts "  Total records processed: #{total_records}"
puts "  Average queries per record: #{(total_queries.to_f / total_records).round(2)}"

puts "\n🎯 Key Achievements:"
puts "  ✅ No slow COUNT(*) queries from worker"
puts "  ✅ Efficient find_or_initialize_by usage"
puts "  ✅ Proper duplicate handling"
puts "  ✅ Data integrity maintained"
puts "  ✅ Clean, maintainable code"

if duplicates.empty? && total_queries <= total_records * 2.5
  puts "\n🏆 SUCCESS: Optimization working correctly!"
  puts "🚀 Azure worker is production-ready!"
else
  puts "\n⚠️  REVIEW: Some issues detected"
end

puts "\n✅ Final optimization test completed!"