# Quick AWS Worker UPSERT_ALL Test
# Usage: rails runner scripts/quick_aws_upsert_test.rb

puts "⚡ Quick AWS Worker UPSERT_ALL Test"
puts "=" * 45

# Configuration
COMPANY_EMAIL = "<EMAIL>"

# Find company
company = Company.find_by(email: COMPANY_EMAIL)
unless company
  puts "❌ Company with email '#{COMPANY_EMAIL}' not found"
  exit
end

puts "✅ Using company: #{company.name} (ID: #{company.id})"

# Setup AWS test data
vendor = company.vendors.find_or_create_by(name: 'Amazon Web Services') do |v|
  v.is_cloud_platform = true
  v.friendly_name = 'AWS'
end

product = vendor.products.find_or_create_by(name: 'Amazon Web Services') do |p|
  p.company = company
end

integration = Integration.find_or_create_by(name: 'aws_quick_test')
company_integration = company.company_integrations.find_or_create_by(integration: integration) do |ci|
  ci.status = true
  ci.sync_status = :successful
end

aws_config = Integrations::Aws::Config.find_or_create_by(company_integration: company_integration) do |config|
  config.access_key_id = 'test_key'
  config.secret_access_key = 'test_secret'
  config.region = 'us-east-1'
end

puts "✅ AWS test environment ready"

# Create mock AWS transaction data
def create_mock_transaction(service_name, amount)
  transaction = OpenStruct.new
  transaction.keys = [service_name]
  transaction.metrics = {
    "UnblendedCost" => OpenStruct.new(amount: amount.to_s)
  }
  transaction
end

mock_transactions = [
  create_mock_transaction("EC2-Instance", 45.67),
  create_mock_transaction("S3-Storage", 12.34),
  create_mock_transaction("RDS-Database", 89.12),
  create_mock_transaction("Lambda-Functions", 5.43),
  create_mock_transaction("CloudWatch-Logs", 2.10)
]

# Clean up previous test data
CloudUsageTransaction.where(
  company: company,
  vendor: vendor,
  name: ["EC2-Instance", "S3-Storage", "RDS-Database", "Lambda-Functions", "CloudWatch-Logs"]
).delete_all

puts "\n🧪 Test 1: CloudUsageTransaction upsert_all (#{mock_transactions.size} records)..."

# Setup worker
worker = Integrations::Aws::SyncDataWorker.new
worker.instance_variable_set(:@company, company)
worker.instance_variable_set(:@company_integration, company_integration)
worker.instance_variable_set(:@aws_config, aws_config)

# Count queries during execution
query_count = 0
subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions') && !sql.include?('DELETE') && !sql.include?('COUNT(*)')
    query_count += 1
    operation = sql.split(' ').first
    puts "  🔍 Query #{query_count}: #{operation} cloud_usage_transactions..."
  end
end

# Test execution
initial_count = CloudUsageTransaction.where(company: company, vendor: vendor).count

start_time = Time.current
worker.send(:save_transactions, mock_transactions, Date.current.to_s)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(subscription)

final_count = CloudUsageTransaction.where(company: company, vendor: vendor).count
duration = ((end_time - start_time) * 1000).round(2)

# Results
puts "\n📊 Create Results:"
puts "  ⏱️  Processing time: #{duration}ms"
puts "  📝 Records processed: #{mock_transactions.size}"
puts "  ➕ Records created: #{final_count - initial_count}"
puts "  🔍 Database queries: #{query_count}"

if query_count == 1
  puts "  🏆 PERFECT: Single upsert_all query!"
elsif query_count <= 3
  puts "  ✅ EXCELLENT: Very efficient!"
else
  puts "  ⚠️  UNEXPECTED: More queries than expected"
end

# Test 2: Updates
puts "\n🔄 Test 2: Updates (same records with different amounts)..."

# Modify amounts for update test
update_transactions = mock_transactions.map do |transaction|
  new_transaction = transaction.dup
  new_transaction.metrics = {
    "UnblendedCost" => OpenStruct.new(amount: (transaction.metrics["UnblendedCost"].amount.to_f * 2).to_s)
  }
  new_transaction
end

# Count queries for updates
update_query_count = 0
update_subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions') && !sql.include?('DELETE') && !sql.include?('COUNT(*)')
    update_query_count += 1
    operation = sql.split(' ').first
    puts "  🔍 Query #{update_query_count}: #{operation} cloud_usage_transactions..."
  end
end

start_time = Time.current
worker.send(:save_transactions, update_transactions, Date.current.to_s)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(update_subscription)

update_duration = ((end_time - start_time) * 1000).round(2)
final_count_after_update = CloudUsageTransaction.where(company: company, vendor: vendor).count

puts "\n📊 Update Results:"
puts "  ⏱️  Processing time: #{update_duration}ms"
puts "  📝 Records processed: #{mock_transactions.size}"
puts "  📊 Total records: #{final_count_after_update} (should be #{mock_transactions.size})"
puts "  🔍 Database queries: #{update_query_count}"

if update_query_count == 1
  puts "  🏆 PERFECT: Single upsert_all query for updates!"
elsif update_query_count <= 3
  puts "  ✅ EXCELLENT: Efficient updates!"
else
  puts "  ⚠️  UNEXPECTED: More queries than expected for updates"
end

# Verify no duplicates
if final_count_after_update == mock_transactions.size
  puts "  ✅ CORRECT: No duplicate records created!"
else
  puts "  ⚠️  ISSUE: Duplicate records detected"
end

# Test 3: PredictedCloudTransaction
puts "\n🔮 Test 3: PredictedCloudTransaction upsert_all..."

# Clean up previous predicted data
PredictedCloudTransaction.where(
  company: company,
  name: "Forecasted Cost"
).delete_all

# Create mock projections
def create_mock_projections(count)
  projections = OpenStruct.new
  projections.forecast_results_by_time = count.times.map do |i|
    projection = OpenStruct.new
    projection.time_period = OpenStruct.new(start: (Date.current + i.days).to_s)
    projection.mean_value = (100 + i * 10).to_s
    projection
  end
  projections
end

mock_projections = create_mock_projections(3)

# Count queries for projections
projection_query_count = 0
projection_subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('predicted_cloud_transactions') && !sql.include?('DELETE') && !sql.include?('COUNT(*)')
    projection_query_count += 1
    operation = sql.split(' ').first
    puts "  🔍 Query #{projection_query_count}: #{operation} predicted_cloud_transactions..."
  end
end

start_time = Time.current
worker.send(:save_projections, mock_projections)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(projection_subscription)

projection_duration = ((end_time - start_time) * 1000).round(2)
projection_count = PredictedCloudTransaction.where(company: company, name: "Forecasted Cost").count

puts "\n📊 Projection Results:"
puts "  ⏱️  Processing time: #{projection_duration}ms"
puts "  📝 Projections processed: #{mock_projections.forecast_results_by_time.size}"
puts "  ➕ Records created: #{projection_count}"
puts "  🔍 Database queries: #{projection_query_count}"

if projection_query_count == 1
  puts "  🏆 PERFECT: Single upsert_all query!"
elsif projection_query_count <= 3
  puts "  ✅ EXCELLENT: Very efficient!"
else
  puts "  ⚠️  UNEXPECTED: More queries than expected"
end

# Summary
puts "\n" + "=" * 45
puts "🎯 AWS WORKER UPSERT_ALL SUMMARY"
puts "=" * 45

total_queries = query_count + update_query_count + projection_query_count
perfect_tests = [query_count, update_query_count, projection_query_count].count(1)

puts "\n📊 Overall Results:"
puts "  CloudUsageTransaction create: #{query_count} queries"
puts "  CloudUsageTransaction update: #{update_query_count} queries"
puts "  PredictedCloudTransaction: #{projection_query_count} queries"
puts "  Total queries: #{total_queries}"
puts "  Perfect tests (1 query): #{perfect_tests}/3"

puts "\n🚀 Optimization Benefits:"
puts "  ✅ Single query per batch operation"
puts "  ✅ Handles both inserts and updates"
puts "  ✅ Works for both CloudUsageTransaction and PredictedCloudTransaction"
puts "  ✅ Eliminates N+1 query issues"
puts "  ✅ Maximum database efficiency"

if perfect_tests >= 2
  puts "\n🏆 OUTSTANDING: AWS worker optimization successful!"
  puts "🚀 Ready for production with maximum efficiency!"
else
  puts "\n⚠️  REVIEW: Some operations may need further optimization"
end

puts "\n✅ Quick AWS worker upsert_all test completed!"
