# Test script for AWS Worker UPSERT_ALL optimization
# Usage: rails runner scripts/test_aws_upsert_optimization.rb

puts "🚀 AWS Worker UPSERT_ALL Optimization Test"
puts "=" * 60

# Configuration
COMPANY_EMAIL = "<EMAIL>"

class AwsUpsertTester
  attr_reader :company, :vendor, :product, :company_integration, :aws_config

  def initialize
    setup_test_data
  end

  def run_tests
    puts "\n📋 Testing AWS worker upsert_all optimization..."
    
    test_cloud_usage_transactions
    test_predicted_cloud_transactions
    
    display_summary
  end

  private

  def setup_test_data
    puts "\n🔧 Setting up AWS test environment..."
    
    @company = Company.find_by(email: COMPANY_EMAIL)
    raise "Company with email '#{COMPANY_EMAIL}' not found" unless @company
    
    # Setup AWS vendor
    @vendor = @company.vendors.find_or_create_by(name: 'Amazon Web Services') do |v|
      v.is_cloud_platform = true
      v.friendly_name = 'AWS'
    end
    
    # Setup AWS product
    @product = @vendor.products.find_or_create_by(name: 'Amazon Web Services') do |p|
      p.company = @company
    end
    
    # Setup integration
    integration = Integration.find_or_create_by(name: 'aws_test')
    @company_integration = @company.company_integrations.find_or_create_by(integration: integration) do |ci|
      ci.status = true
      ci.sync_status = :successful
    end
    
    # Setup AWS config
    @aws_config = Integrations::Aws::Config.find_or_create_by(company_integration: @company_integration) do |config|
      config.access_key_id = 'test_key'
      config.secret_access_key = 'test_secret'
      config.region = 'us-east-1'
    end
    
    puts "✅ AWS test environment ready:"
    puts "   Company: #{@company.name} (ID: #{@company.id})"
    puts "   Vendor: #{@vendor.name} (ID: #{@vendor.id})"
    puts "   Product: #{@product.name} (ID: #{@product.id})"
    puts "   Integration: #{@company_integration.integration.name} (ID: #{@company_integration.id})"
  end

  def test_cloud_usage_transactions
    puts "\n🧪 Test 1: CloudUsageTransaction upsert_all"
    
    # Clean up previous test data
    CloudUsageTransaction.where(
      company: @company,
      vendor: @vendor,
      name: ["EC2-Instance", "S3-Storage", "RDS-Database"]
    ).delete_all
    
    # Create mock AWS transaction data
    mock_transactions = [
      create_mock_transaction("EC2-Instance", 45.67),
      create_mock_transaction("S3-Storage", 12.34),
      create_mock_transaction("RDS-Database", 89.12)
    ]
    
    # Setup worker
    worker = Integrations::Aws::SyncDataWorker.new
    worker.instance_variable_set(:@company, @company)
    worker.instance_variable_set(:@company_integration, @company_integration)
    worker.instance_variable_set(:@aws_config, @aws_config)
    
    # Count queries
    query_count = count_cloud_usage_queries do
      worker.send(:save_transactions, mock_transactions, Date.current.to_s)
    end
    
    # Verify results
    created_count = CloudUsageTransaction.where(
      company: @company,
      vendor: @vendor,
      name: ["EC2-Instance", "S3-Storage", "RDS-Database"]
    ).count
    
    puts "   📊 Results:"
    puts "      Mock transactions: #{mock_transactions.size}"
    puts "      Records created: #{created_count}"
    puts "      Database queries: #{query_count}"
    
    if query_count == 1
      puts "      🏆 PERFECT: Single upsert_all query!"
    elsif query_count <= 3
      puts "      ✅ EXCELLENT: Very efficient!"
    else
      puts "      ⚠️  REVIEW: More queries than expected"
    end
    
    # Test updates
    puts "\n   🔄 Testing updates..."
    
    # Modify amounts for update test
    update_transactions = mock_transactions.map do |transaction|
      new_transaction = transaction.dup
      new_transaction.metrics["UnblendedCost"].amount = (transaction.metrics["UnblendedCost"].amount.to_f * 2).to_s
      new_transaction
    end
    
    update_query_count = count_cloud_usage_queries do
      worker.send(:save_transactions, update_transactions, Date.current.to_s)
    end
    
    puts "      Update queries: #{update_query_count}"
    
    if update_query_count == 1
      puts "      🏆 PERFECT: Single upsert_all query for updates!"
    elsif update_query_count <= 3
      puts "      ✅ EXCELLENT: Efficient updates!"
    else
      puts "      ⚠️  REVIEW: More queries than expected for updates"
    end
    
    @cloud_usage_results = {
      create_queries: query_count,
      update_queries: update_query_count,
      records_processed: mock_transactions.size * 2
    }
  end

  def test_predicted_cloud_transactions
    puts "\n🧪 Test 2: PredictedCloudTransaction upsert_all"
    
    # Clean up previous test data
    PredictedCloudTransaction.where(
      company: @company,
      name: "Forecasted Cost"
    ).delete_all
    
    # Create mock projection data
    mock_projections = create_mock_projections(3)
    
    # Setup worker
    worker = Integrations::Aws::SyncDataWorker.new
    worker.instance_variable_set(:@company, @company)
    worker.instance_variable_set(:@company_integration, @company_integration)
    worker.instance_variable_set(:@aws_config, @aws_config)
    
    # Count queries
    query_count = count_predicted_queries do
      worker.send(:save_projections, mock_projections)
    end
    
    # Verify results
    created_count = PredictedCloudTransaction.where(
      company: @company,
      name: "Forecasted Cost"
    ).count
    
    puts "   📊 Results:"
    puts "      Mock projections: #{mock_projections.forecast_results_by_time.size}"
    puts "      Records created: #{created_count}"
    puts "      Database queries: #{query_count}"
    
    if query_count == 1
      puts "      🏆 PERFECT: Single upsert_all query!"
    elsif query_count <= 3
      puts "      ✅ EXCELLENT: Very efficient!"
    else
      puts "      ⚠️  REVIEW: More queries than expected"
    end
    
    @predicted_results = {
      queries: query_count,
      records_processed: mock_projections.forecast_results_by_time.size
    }
  end

  def create_mock_transaction(service_name, amount)
    # Create a mock transaction object that mimics AWS API response
    transaction = OpenStruct.new
    transaction.keys = [service_name]
    transaction.metrics = {
      "UnblendedCost" => OpenStruct.new(amount: amount.to_s)
    }
    transaction
  end

  def create_mock_projections(count)
    # Create mock projections object that mimics AWS API response
    projections = OpenStruct.new
    projections.forecast_results_by_time = count.times.map do |i|
      projection = OpenStruct.new
      projection.time_period = OpenStruct.new(start: (Date.current + i.days).to_s)
      projection.mean_value = (100 + i * 10).to_s
      projection
    end
    projections
  end

  def count_cloud_usage_queries(&block)
    count_queries('cloud_usage_transactions', &block)
  end

  def count_predicted_queries(&block)
    count_queries('predicted_cloud_transactions', &block)
  end

  def count_queries(table_name, &block)
    query_count = 0
    
    subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
      sql = data[:sql]
      if sql.include?(table_name) && !sql.include?('DELETE') && !sql.include?('COUNT(*)')
        query_count += 1
        operation = sql.split(' ').first
        puts "      🔍 Query #{query_count}: #{operation} #{table_name}..."
      end
    end
    
    block.call
    
    ActiveSupport::Notifications.unsubscribe(subscription)
    query_count
  end

  def display_summary
    puts "\n" + "=" * 60
    puts "🎯 AWS WORKER UPSERT_ALL SUMMARY"
    puts "=" * 60
    
    puts "\n📊 CloudUsageTransaction Performance:"
    puts "  Create queries: #{@cloud_usage_results[:create_queries]}"
    puts "  Update queries: #{@cloud_usage_results[:update_queries]}"
    puts "  Records processed: #{@cloud_usage_results[:records_processed]}"
    
    puts "\n📊 PredictedCloudTransaction Performance:"
    puts "  Queries: #{@predicted_results[:queries]}"
    puts "  Records processed: #{@predicted_results[:records_processed]}"
    
    total_queries = @cloud_usage_results[:create_queries] + @cloud_usage_results[:update_queries] + @predicted_results[:queries]
    total_records = @cloud_usage_results[:records_processed] + @predicted_results[:records_processed]
    
    puts "\n🎯 Overall Performance:"
    puts "  Total queries: #{total_queries}"
    puts "  Total records: #{total_records}"
    puts "  Average queries per operation: #{(total_queries.to_f / 3).round(2)}"
    
    puts "\n🚀 UPSERT_ALL Benefits:"
    puts "  ✅ Single query per batch operation"
    puts "  ✅ Handles both inserts and updates"
    puts "  ✅ Eliminates N+1 query issues"
    puts "  ✅ Maximum database efficiency"
    puts "  ✅ Scalable performance"
    
    perfect_operations = [@cloud_usage_results[:create_queries], @cloud_usage_results[:update_queries], @predicted_results[:queries]].count(1)
    
    if perfect_operations >= 2
      puts "\n🏆 OUTSTANDING: AWS worker optimization successful!"
      puts "🚀 Ready for production with maximum efficiency!"
    else
      puts "\n⚠️  REVIEW: Some operations may need further optimization"
    end
    
    puts "\n✅ AWS worker upsert_all optimization test completed!"
  end
end

# Run the tests
begin
  tester = AwsUpsertTester.new
  tester.run_tests
rescue => e
  puts "\n❌ Test failed: #{e.message}"
  puts e.backtrace.first(5)
end
