puts " Azure Worker UPSERT_ALL Optimization Test"
puts "=" * 60

COMPANY_EMAIL = "<EMAIL>"
TEST_SIZES = [10, 25, 50, 1000]

class UpsertOptimizationTester
  attr_reader :company, :vendor, :product, :company_integration

  def initialize
    setup_test_data
    @results = {}
  end

  def run_all_tests
    puts "\n Testing upsert_all optimization..."
    
    TEST_SIZES.each do |size|
      puts "\n" + "=" * 50
      puts "🧪 Testing with #{size} records"
      puts "=" * 50
      
      test_pure_inserts(size)
      test_pure_updates(size)
      test_mixed_operations(size)
    end
    
    display_performance_summary
  end

  private

  def setup_test_data
    puts "\n🔧 Setting up test environment..."
    
    @company = Company.find_by(email: COMPANY_EMAIL)
    raise "Company with email '#{COMPANY_EMAIL}' not found" unless @company
    
    @vendor = @company.vendors.find_or_create_by(name: 'Upsert Test Vendor') do |v|
      v.is_cloud_platform = true
    end
    
    @product = @vendor.products.find_or_create_by(name: 'Upsert Test Product') do |p|
      p.company = @company
    end
    
    integration = Integration.find_or_create_by(name: 'upsert_test')
    @company_integration = @company.company_integrations.find_or_create_by(integration: integration) do |ci|
      ci.status = true
      ci.sync_status = :successful
    end
    
    puts "Test environment ready:"
    puts "   Company: #{@company.name} (ID: #{@company.id})"
    puts "   Vendor: #{@vendor.name} (ID: #{@vendor.id})"
    puts "   Product: #{@product.name} (ID: #{@product.id})"
  end

  def test_pure_inserts(record_count)
    puts "\n Test 1: Pure inserts (all new records)"
    
    cleanup_test_data("insert_#{record_count}")
    
    mock_data = create_mock_data(record_count, "insert_#{record_count}")
    
    query_count = count_queries do
      run_worker_test(mock_data)
    end
    
    puts "    Results:"
    puts "      Records processed: #{record_count}"
    puts "      Database queries: #{query_count}"
    puts "      Expected: 1 query (single upsert_all)"
    
    if query_count == 1
      puts "       PERFECT: Single upsert_all query!"
    elsif query_count <= 3
      puts "       EXCELLENT: Very efficient!"
    else
      puts "        UNEXPECTED: More queries than expected"
    end
    
    @results["insert_#{record_count}"] = {
      type: "pure_inserts",
      records: record_count,
      queries: query_count
    }
  end

  def test_pure_updates(record_count)
    puts "\n Test 2: Pure updates (all existing records)"
    
    existing_data = create_mock_data(record_count, "update_#{record_count}")
    run_worker_test(existing_data)
    
    update_data = existing_data.map do |record|
      record.merge(
        "properties" => record["properties"].merge(
          "costInBillingCurrency" => record["properties"]["costInBillingCurrency"] * 2
        )
      )
    end
    
    query_count = count_queries do
      run_worker_test(update_data)
    end
    
    puts "    Results:"
    puts "      Records processed: #{record_count}"
    puts "      Database queries: #{query_count}"
    puts "      Expected: 1 query (single upsert_all)"
    
    if query_count == 1
      puts "       PERFECT: Single upsert_all query!"
    elsif query_count <= 3
      puts "       EXCELLENT: Very efficient!"
    else
      puts "        UNEXPECTED: More queries than expected"
    end
    
    @results["update_#{record_count}"] = {
      type: "pure_updates",
      records: record_count,
      queries: query_count
    }
  end

  def test_mixed_operations(record_count)
    puts "\n Test 3: Mixed operations (50% updates, 50% inserts)"
    
    existing_count = record_count / 2
    existing_data = create_mock_data(existing_count, "mixed_#{record_count}_existing")
    run_worker_test(existing_data)
    
    mixed_data = []
    
    update_data = existing_data.map do |record|
      record.merge(
        "properties" => record["properties"].merge(
          "costInBillingCurrency" => record["properties"]["costInBillingCurrency"] * 1.5
        )
      )
    end
    mixed_data.concat(update_data)
    
    new_count = record_count - existing_count
    new_data = create_mock_data(new_count, "mixed_#{record_count}_new")
    mixed_data.concat(new_data)
    
    query_count = count_queries do
      run_worker_test(mixed_data)
    end
    
    puts "    Results:"
    puts "      Records processed: #{record_count}"
    puts "      Database queries: #{query_count}"
    puts "      Expected: 1 query (single upsert_all)"
    
    if query_count == 1
      puts "       PERFECT: Single upsert_all query!"
    elsif query_count <= 3
      puts "       EXCELLENT: Very efficient!"
    else
      puts "        UNEXPECTED: More queries than expected"
    end
    
    @results["mixed_#{record_count}"] = {
      type: "mixed_operations",
      records: record_count,
      queries: query_count
    }
  end

  def count_queries(&block)
    query_count = 0
    
    subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
      sql = data[:sql]
      if sql.include?('cloud_usage_transactions')
        query_count += 1
        puts "       Query #{query_count}: #{sql.split(' ').first(3).join(' ')}..."
      end
    end
    
    block.call
    
    ActiveSupport::Notifications.unsubscribe(subscription)
    query_count
  end

  def run_worker_test(mock_data)
    worker = Integrations::Azure::SaveUsageTransactionsWorker.new
    worker.instance_variable_set(:@company, @company)
    worker.instance_variable_set(:@company_integration, @company_integration)
    worker.instance_variable_set(:@company_integration_id, @company_integration.id)
    worker.instance_variable_set(:@vendor_id, @vendor.id)
    worker.instance_variable_set(:@product_id, @product.id)
    
    worker.send(:save_usage_transactions, mock_data)
  end

  def create_mock_data(count, prefix)
    count.times.map do |i|
      {
        "properties" => {
          "date" => (Date.current - rand(7).days).to_s,
          "consumedService" => "#{prefix}_service_#{i}",
          "costInBillingCurrency" => rand(10.0..100.0).round(2),
          "cost" => rand(10.0..100.0).round(2)
        }
      }
    end
  end

  def cleanup_test_data(prefix)
    CloudUsageTransaction.where(
      company: @company,
      vendor: @vendor
    ).where("name LIKE ?", "%#{prefix}%").delete_all
  end

  def display_performance_summary
    puts "\n" + "=" * 60
    puts " UPSERT_ALL OPTIMIZATION SUMMARY"
    puts "=" * 60
    
    puts "\n Query Efficiency Results:"
    
    @results.each do |test_name, result|
      puts "\n#{test_name.humanize}:"
      puts "   Records: #{result[:records]}"
      puts "   Queries: #{result[:queries]}"
      puts "   Type: #{result[:type].humanize}"
      
      if result[:queries] == 1
        puts "   PERFECT: Single upsert_all query!"
      elsif result[:queries] <= 3
        puts "   EXCELLENT: Very efficient!"
      else
        puts "    REVIEW: Expected fewer queries"
      end
    end
    total_records = @results.values.sum { |r| r[:records] }
    total_queries = @results.values.sum { |r| r[:queries] }
    
    puts "\n OVERALL PERFORMANCE:"
    puts "  Total records processed: #{total_records}"
    puts "  Total database queries: #{total_queries}"
    puts "  Average queries per test: #{(total_queries.to_f / @results.size).round(2)}"
    
    perfect_tests = @results.values.count { |r| r[:queries] == 1 }
    
    if perfect_tests == @results.size
      puts "\n OUTSTANDING: All tests achieved perfect single-query performance!"
    elsif perfect_tests >= @results.size * 0.8
      puts "\n EXCELLENT: Most tests achieved optimal performance!"
    else
      puts "\n  REVIEW: Some tests may need optimization"
    end
    
    puts "\n Upsert_all optimization test completed!"
  end
end

begin
  tester = UpsertOptimizationTester.new
  tester.run_all_tests
rescue => e
  puts "\n Test failed: #{e.message}"
  puts e.backtrace.first(5)
end
