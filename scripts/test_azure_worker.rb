# Quick fix - run this in Rails console to test the corrected worker

puts "🚀 Testing Azure Worker with Manual Fix"
puts "="*50

# Use your specific company
company = Company.find_by(email: "<EMAIL>")
puts "✅ Using company: #{company.name} (ID: #{company.id})"

# Create test vendor and product
vendor = company.vendors.find_or_create_by(name: 'Microsoft Azure Test') do |v|
  v.is_cloud_platform = true
end

product = vendor.products.find_or_create_by(name: 'Azure Test Services') do |p|
  p.company = company
end

# Create integration
integration = Integration.find_or_create_by(name: 'azure_test')
company_integration = company.company_integrations.find_or_create_by(integration: integration) do |ci|
  ci.status = true
  ci.sync_status = :successful
end

# Create mock Azure data
mock_data = [
  {
    "properties" => {
      "date" => "2024-01-15",
      "consumedService" => "Virtual Machines",
      "costInBillingCurrency" => 45.67,
      "cost" => 45.67
    }
  },
  {
    "properties" => {
      "date" => "2024-01-15", 
      "consumedService" => "Storage",
      "costInBillingCurrency" => 12.34,
      "cost" => 12.34
    }
  }
]

# Create a simple working version of the optimized method
class TestAzureWorker
  def initialize(company, company_integration_id, vendor_id, product_id)
    @company = company
    @company_integration_id = company_integration_id
    @vendor_id = vendor_id
    @product_id = product_id
  end

  def save_usage_transactions(response)
    return if response.blank?
    
    puts "[TEST WORKER] Processing #{response.size} usage records"
    
    # Filter valid usage records
    valid_usages = response.filter_map do |usage|
      next unless usage.present? && usage["properties"]["costInBillingCurrency"].to_f > 0
      
      {
        transaction_date: usage["properties"]["date"],
        name: usage["properties"]["consumedService"],
        amount: usage["properties"]["costInBillingCurrency"].to_f
      }
    end
    
    puts "[TEST WORKER] Found #{valid_usages.size} valid records"
    
    # Process each record (simplified for testing)
    valid_usages.each do |usage|
      transaction = CloudUsageTransaction.find_or_initialize_by(
        company_id: @company.id,
        transaction_date: usage[:transaction_date],
        name: usage[:name],
        company_integration_id: @company_integration_id,
        is_manual: false
      )
      
      transaction.assign_attributes(
        vendor_id: @vendor_id,
        product_id: @product_id,
        amount: usage[:amount]
      )
      
      transaction.save!
      puts "[TEST WORKER] Saved: #{usage[:name]} - $#{usage[:amount]}"
    end
  end
end

# Test the simplified worker
puts "\n🧪 Running simplified test..."
initial_count = CloudUsageTransaction.where(company: company).count

test_worker = TestAzureWorker.new(company, company_integration.id, vendor.id, product.id)

start_time = Time.current
test_worker.save_usage_transactions(mock_data)
end_time = Time.current

final_count = CloudUsageTransaction.where(company: company).count

puts "\n📊 Results:"
puts "- Processing time: #{((end_time - start_time) * 1000).round(2)}ms"
puts "- Initial count: #{initial_count}"
puts "- Final count: #{final_count}"
puts "- Records created: #{final_count - initial_count}"

puts "\n🔍 Created transactions:"
CloudUsageTransaction.where(company: company, vendor: vendor).order(:transaction_date, :name).each do |t|
  puts "- #{t.transaction_date} | #{t.name} | $#{t.amount}"
end

puts "\n✅ Test completed successfully!"