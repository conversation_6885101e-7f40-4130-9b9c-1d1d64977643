# Test script for Azure Worker optimization
# Run this in Rails console: load 'scripts/test_azure_worker.rb'

puts "Setting up test data for Azure Worker..."

# 1. Use the specific company
company = Company.find_by(email: "<EMAIL>")

if company.nil?
  puts "❌ Company with email '<EMAIL>' not found."
  exit
end

puts "✅ Using company: #{company.name} (ID: #{company.id})"

# 2. Create Azure config (simplified)
azure_config = Integrations::Azure::Config.find_or_create_by(company: company)

# 3. Create company integration
integration = Integration.find_or_create_by(name: 'azure_test')
company_integration = CompanyIntegration.find_or_create_by(
  company: company,
  integration: integration
) do |ci|
  ci.status = true
  ci.sync_status = :successful
end

# 4. Create vendor and product
vendor = Vendor.find_or_create_by(name: 'Microsoft Azure Test', company: company) do |v|
  v.is_cloud_platform = true
end

product = Product.find_or_create_by(name: 'Azure Test Services', vendor: vendor) do |p|
  p.company = company
end

# 5. Mock Azure response data
mock_response = [
  {
    "properties" => {
      "date" => "2024-01-15",
      "consumedService" => "Virtual Machines",
      "costInBillingCurrency" => 45.67,
      "cost" => 45.67
    }
  },
  {
    "properties" => {
      "date" => "2024-01-15", 
      "consumedService" => "Storage",
      "costInBillingCurrency" => 12.34,
      "cost" => 12.34
    }
  },
  {
    "properties" => {
      "date" => "2024-01-16",
      "consumedService" => "Virtual Machines", 
      "costInBillingCurrency" => 67.89,
      "cost" => 67.89
    }
  },
  {
    "properties" => {
      "date" => "2024-01-16",
      "consumedService" => "Networking",
      "costInBillingCurrency" => 23.45,
      "cost" => 23.45
    }
  }
]

# 6. Create some existing records to test updates
existing_transaction = CloudUsageTransaction.find_or_create_by(
  company: company,
  vendor: vendor,
  product: product,
  company_integration: company_integration,
  transaction_date: Date.parse("2024-01-15"),
  name: "Virtual Machines",
  is_manual: false
) do |t|
  t.amount = 30.00 # Different amount to test update
end

puts "\n✅ Test data ready:"
puts "- Company: #{company.name} (ID: #{company.id})"
puts "- Azure Config: #{azure_config.id}"
puts "- Company Integration: #{company_integration.id}"
puts "- Vendor: #{vendor.name} (ID: #{vendor.id})"
puts "- Product: #{product.name} (ID: #{product.id})"
puts "- Existing transaction: #{existing_transaction.id} (amount: $#{existing_transaction.amount})"
puts "- Mock response has #{mock_response.size} records"

# 7. Test the worker
puts "\n" + "="*50
puts "TESTING AZURE WORKER"
puts "="*50

# Enable SQL logging to see queries
ActiveRecord::Base.logger = Logger.new($stdout)
ActiveRecord::Base.logger.level = Logger::DEBUG

# Count existing records
initial_count = CloudUsageTransaction.where(company: company).count
puts "Initial CloudUsageTransaction count: #{initial_count}"

# Create worker instance and test
worker = Integrations::Azure::SaveUsageTransactionsWorker.new

# Set instance variables that would normally be set in perform method
worker.instance_variable_set(:@company, company)
worker.instance_variable_set(:@company_integration, company_integration)
worker.instance_variable_set(:@company_integration_id, company_integration.id)
worker.instance_variable_set(:@vendor_id, vendor.id)
worker.instance_variable_set(:@product_id, product.id)

# Measure performance
start_time = Time.current

# Call the optimized method
worker.send(:save_usage_transactions, mock_response)

end_time = Time.current

# Check results
final_count = CloudUsageTransaction.where(company: company).count
updated_transaction = CloudUsageTransaction.find(existing_transaction.id)

puts "\n" + "="*50
puts "RESULTS"
puts "="*50
puts "Execution time: #{((end_time - start_time) * 1000).round(2)}ms"
puts "Initial count: #{initial_count}"
puts "Final count: #{final_count}"
puts "Records created: #{final_count - initial_count}"
puts "Existing record updated: #{updated_transaction.amount} (was #{existing_transaction.amount})"

# Show all transactions for this company
puts "\nAll CloudUsageTransactions for company:"
CloudUsageTransaction.where(company: company).each do |t|
  puts "- #{t.transaction_date} | #{t.name} | $#{t.amount} | #{t.new_record? ? 'NEW' : 'EXISTING'}"
end

puts "\nTest completed!"
