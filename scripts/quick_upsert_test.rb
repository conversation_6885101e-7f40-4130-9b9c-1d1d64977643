#rails runner scripts/quick_upsert_test.rb

puts "⚡ Quick UPSERT_ALL Test"
puts "=" * 40

COMPANY_EMAIL = "<EMAIL>"
TEST_RECORD_COUNT = 2000

company = Company.find_by(email: COMPANY_EMAIL)
unless company
  puts "Company with email '#{COMPANY_EMAIL}' not found"
  exit
end

puts "Using company: #{company.name} (ID: #{company.id})"

vendor = company.vendors.find_or_create_by(name: 'Quick Upsert Vendor') do |v|
  v.is_cloud_platform = true
end

product = vendor.products.find_or_create_by(name: 'Quick Upsert Product') do |p|
  p.company = company
end

integration = Integration.find_or_create_by(name: 'quick_upsert_test')
company_integration = company.company_integrations.find_or_create_by(integration: integration) do |ci|
  ci.status = true
  ci.sync_status = :successful
end

mock_data = TEST_RECORD_COUNT.times.map do |i|
  {
    "properties" => {
      "date" => (Date.current - rand(5).days).to_s,
      "consumedService" => "UpsertService_#{i}",
      "costInBillingCurrency" => rand(10.0..100.0).round(2),
      "cost" => rand(10.0..100.0).round(2)
    }
  }
end
CloudUsageTransaction.where(
  company: company,
  vendor: vendor
).where("name LIKE ?", "%UpsertService_%").delete_all

puts "\nTest 1: Pure inserts with #{TEST_RECORD_COUNT} records..."

worker = Integrations::Azure::SaveUsageTransactionsWorker.new
worker.instance_variable_set(:@company, company)
worker.instance_variable_set(:@company_integration, company_integration)
worker.instance_variable_set(:@company_integration_id, company_integration.id)
worker.instance_variable_set(:@vendor_id, vendor.id)
worker.instance_variable_set(:@product_id, product.id)

query_count = 0
subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions')
    query_count += 1
    puts "  🔍 Query #{query_count}: #{sql.split(' ').first(3).join(' ')}..."
  end
end

initial_count = CloudUsageTransaction.where(company: company, vendor: vendor).count

start_time = Time.current
worker.send(:save_usage_transactions, mock_data)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(subscription)

final_count = CloudUsageTransaction.where(company: company, vendor: vendor).count
duration = ((end_time - start_time) * 1000).round(2)

puts "\n Insert Test Results:"
puts "    Processing time: #{duration}ms"
puts "   Records processed: #{TEST_RECORD_COUNT}"
puts "   Records created: #{final_count - initial_count}"
puts "   Database queries: #{query_count}"
puts "   Expected: 1 query (upsert_all)"

if query_count == 1
  puts "   PERFECT: Single upsert_all query achieved!"
elsif query_count <= 3
  puts "   EXCELLENT: Very efficient!"
else
  puts "    UNEXPECTED: More queries than expected with upsert_all"
end

puts "\nTest 2: Updates with same #{TEST_RECORD_COUNT} records..."

update_data = mock_data.map do |record|
  record.merge(
    "properties" => record["properties"].merge(
      "costInBillingCurrency" => record["properties"]["costInBillingCurrency"] * 2
    )
  )
end

update_query_count = 0
update_subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions')
    update_query_count += 1
    puts "  🔍 Query #{update_query_count}: #{sql.split(' ').first(3).join(' ')}..."
  end
end

start_time = Time.current
worker.send(:save_usage_transactions, update_data)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(update_subscription)

update_duration = ((end_time - start_time) * 1000).round(2)
final_count_after_update = CloudUsageTransaction.where(company: company, vendor: vendor).count

puts "\nUpdate Test Results:"
puts "    Processing time: #{update_duration}ms"
puts "   Records processed: #{TEST_RECORD_COUNT}"
puts "   Total records after update: #{final_count_after_update}"
puts "   Database queries: #{update_query_count}"
puts "  Expected: 1 query (upsert_all)"

if update_query_count == 1
  puts "  PERFECT: Single upsert_all query for updates!"
elsif update_query_count <= 3
  puts "   EXCELLENT: Very efficient updates!"
else
  puts "    UNEXPECTED: More queries than expected for updates"
end

puts "\n Test 3: Mixed operations (10 updates + 10 new records)..."

mixed_data = []

mixed_data.concat(update_data.first(10))

new_records = 10.times.map do |i|
  {
    "properties" => {
      "date" => Date.current.to_s,
      "consumedService" => "NewUpsertService_#{i}",
      "costInBillingCurrency" => rand(50.0..150.0).round(2),
      "cost" => rand(50.0..150.0).round(2)
    }
  }
end
mixed_data.concat(new_records)

mixed_query_count = 0
mixed_subscription = ActiveSupport::Notifications.subscribe('sql.active_record') do |name, started, finished, unique_id, data|
  sql = data[:sql]
  if sql.include?('cloud_usage_transactions')
    mixed_query_count += 1
    puts "  🔍 Query #{mixed_query_count}: #{sql.split(' ').first(3).join(' ')}..."
  end
end

start_time = Time.current
worker.send(:save_usage_transactions, mixed_data)
end_time = Time.current

ActiveSupport::Notifications.unsubscribe(mixed_subscription)

mixed_duration = ((end_time - start_time) * 1000).round(2)

puts "\n Mixed Operations Results:"
puts "    Processing time: #{mixed_duration}ms"
puts "   Records processed: #{mixed_data.size}"
puts "   Database queries: #{mixed_query_count}"
puts "   Expected: 1 query (upsert_all)"

if mixed_query_count == 1
  puts "   PERFECT: Single upsert_all query for mixed operations!"
elsif mixed_query_count <= 3
  puts "   EXCELLENT: Very efficient mixed operations!"
else
  puts "    UNEXPECTED: More queries than expected for mixed operations"
end

total_queries = query_count + update_query_count + mixed_query_count
perfect_tests = [query_count, update_query_count, mixed_query_count].count(1)

puts "\nOverall Results:"
puts "  Total tests: 3"
puts "  Perfect tests (1 query): #{perfect_tests}"
puts "  Total queries across all tests: #{total_queries}"
puts "  Expected total: 3 queries (1 per test)"

if perfect_tests == 3
  puts "\n OUTSTANDING: All tests achieved perfect single-query performance!"
  puts " UPSERT_ALL optimization is working flawlessly!"
elsif perfect_tests >= 2
  puts "\n EXCELLENT: Most tests achieved optimal performance!"
  puts " UPSERT_ALL optimization is working well!"
else
  puts "\n REVIEW: UPSERT_ALL may need investigation"
end

puts "\n Quick upsert_all test completed!"
