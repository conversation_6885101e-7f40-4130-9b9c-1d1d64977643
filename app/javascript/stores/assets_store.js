import http from 'common/http';
import Cookies from 'js-cookie';
import createPersistedState from 'vuex-persistedstate';
import router from '../routers/assets';
import customForms from "./mixins/custom_forms_mixin";
import GlobalStore from "./global_store";
import AutomatedTasksStore from "./automated_tasks_store";
import { companyUsersAndGroupsOptions } from './mixins/company_users_groups_options';
import defautStates from './mixins/defaultStates';

export default new Vuex.Store({
  strict: process.env.NODE_ENV === "development",
  modules: {
    companyUsersAndGroupsOptions: {
      ...companyUsersAndGroupsOptions,
    },
    customForms: {
      namespaced: true,
      ...customForms,
    },
    GlobalStore: {
      namespaced: true,
      ...GlobalStore,
    },
    AutomatedTasksStore,
  },

  state: {
    preventTableLoading: false,
    previousRoute: null,
    assetNameColumnWidth: "",
    // These are all of the open close flags
    moduleName: "ManagedAsset",
    showArchived: false, // By default show only active assets
    search: null,
    companyLogoUrl: '',
    currentLocation: null,
    discoveredAssetLocation: null,
    currentTag: null,
    searchTermLogs: '',
    searchLogs: null,
    searchTerm: '',
    searchView: '',
    viewType: null,
    assetsArr: [],
    archivedFiltered: [],
    pageCountLogs: 1,
    pageSizeLogs: 25,
    pageCount: 1,
    pageSize: 25,
    totalRecord: null,
    totalUnfilteredRecords: null,
    successMessage: null,
    pageIndex: 0,
    pageIndexLogs: 0,
    assets: [],
    loading: false,
    loadingLogs: false,
    assetsLoading: false,
    merakiInfo: null,
    discoveredAssets: [],
    discoveredAssetsLogs: [],
    checkedAssets: [],
    assetTypes: [],
    statusTypes: [],
    assetTags: [],

    softwareTypes: ['Operating System', 'Application', 'Utility'],

    usedBy: null,
    managedBy: null,
    activeTab: 'assigned',
    isPeople: false,

    totalIncomplete: null,
    totalUnrecognized: null,
    totalReadyForImport: null,
    totalImported: null,
    totalIgnored:null,
    lastCreatedIncomplete: null,
    lastCreatedReadyForImport: null,
    lastSeenCreatedIncomplete: null,
    lastSeenCreatedReadyForImport: null,
    companyIntegrations: null,
    integrationsWithAlerts: [],
    deletingIntegrations: [],
    loadingAccounts: true,
    integratedSources: [
      "agent",
      "selfonboarding",
      "probe",
      "meraki",
      "ubiquiti",
      "aws",
      "azure",
      "google",
      'kandji',
      'jamf_pro',
      'mosyle',
      'sophos',
    ],
    assetStatuses: [
      {
        id: 'active',
        name: 'Active',
      },
      {
        id: 'archived',
        name: 'Archived',
      },
    ],
    assetSources: [
      {
        id: 'manually_added',
        name: 'Manually Added',
      },
      {
        id: 'uploaded',
        name: 'Imported',
      },
      {
        id: 'selfonboarding',
        name: 'Self-Onboarding',
      },
      {
        id: 'agent',
        name: 'Agent',
      },
      {
        id: 'probe',
        name: 'Probe',
      },
      {
        id: 'meraki',
        name: 'Meraki',
      },
      {
        id: 'ubiquiti',
        name: 'Ubiquiti',
      },
      {
        id: 'aws',
        name: 'AWS',
      },
      {
        id: 'google',
        name: 'GCP',
      },
      {
        id: 'azure',
        name: 'Azure',
      },
      {
        id: 'ms_intune',
        name: 'MS Intune',
      },
      {
        id: 'azure_ad_devices',
        name: 'AzureAD',
      },
      {
        id: 'kaseya',
        name: 'Kaseya',
      },
      {
        id: 'kandji',
        name: 'Kandji',
      },
      {
        id: 'jamf_pro',
        name: 'Jamf Pro',
      },
      {
        id: 'mosyle',
        name: 'Mosyle',
      },
      {
        id: 'google_workspace',
        name: 'Google Workspace',
      },
      {
        id: 'sophos',
        name: 'Sophos',
      },
    ],
    warrantyStates: [
      {
        id: 'in_warranty',
        name: 'In Warranty',
      },
      {
        id: 'expiring_warranty',
        name: 'Expiring Soon',
      },
      {
        id: 'expired_warranty',
        name: 'Expired',
      },
      {
        id: 'no_warranty',
        name: 'No Warranty',
      },
    ],
    assetAvailabilityStatus: [],
    assetInsightsSelectedChartTypes: [
      {
        id: 'warranty',
        type: 'bar',
      },
      {
        id: 'application',
        type: 'bar',
      },
    ],
    currentAsset: null,
    associateTicketsHistory: null,
    currentDiscoveredAsset: {},
    channelKey: '',
    companyChannelKey: '',
    discoveredAssetStatus: null,
    assetDiscovery: null,

    // Warranty stats
    inWarranty: null,
    expiringWarranty: null,
    expiredWarranty: null,
    noWarranty: null,
    totalWarranty: null,

    // Active filters for Multi-select
    activeMultiFilters: {
      activeTypes: [],
      activeSources: [],
      activeTags: [],
      activeWarranties: [],
      activeAvailabilities: [],
      activeLocations: [],
      activeStatuses: [{id : 'active', name : 'Active'}],
      activeDepartments: [],
    },

    // Filter params
    assetType: null,
    assetActivities: [],
    assetActivitiesPage: 0,
    assetActivitiesPerPage: 25,
    assetActivitiesPageCount: 0,
    assetSource: null,
    assetAvailableStatus: null,
    discoveredAssetSource: null,
    discoveredAssetType: null,
    warrantyStatus: null,
    assetStatus: null,
    companyLocations: null,
    companyProducts: [],
    assetTypeData: null,
    topUsers: [],
    softwares: [],
    depreciations: [],
    selectedColumns: [],
    unselectedColumns: [],
    assetPreferences: [],
    discoveredAssetPreferences: [],
    qrCodeSettings: defautStates.qrCodeSettings(),
    currentAssetProduct: {},
    activeOrder: {},
    sortByOptions: {
      activeSort: "name",
      activeSortDirection: "asc",
    },
    reloadTypes: true,
    appsLocationSummary: {
      agentCount: 0,
      probeCount: 0,
    },
    activeImportFileName: "",
    hasUnseenDiscoveryTool: false,
    lastSeenDiscoveryToolTab: null,
    lastAgentVersionCreatedAt: null,
    lastProbeVersionCreatedAt: null,
    assetDepartments: [],
    assetDepartment: null,
    assetManufacturers: [],
    assetOperatingSystems: null,
    analyticsTypeFilter: [],
    analyticsManufacturerFilter: [],
    analyticsWarrantyFilter: [],
    analyticsDeviceEncryption: [],
    analyticsAvailabilityFilter: [],
    analyticsOperatingSystem: [],
    analyticsIntegrationSource: [],
    analyticsNonIntegrationSource: [],
    analyticsLocationFilter: [],
    analyticsMemoryFilter: [],
    analyticsStorageFilter: [],
    analyticsInstalledSoftware: [],
    analyticsNotInstalledSoftware: [],
    totalSoftwareCount: 0,
    totalStatusesCount: 0,
    totalTagsCount: 0,
    totalAssetTypesCount: 0,
    totalManufacturersCount: 0,
    totalOperatingSystemsCount: 0,
    filterOptionSelected: false,
    analyticsOptionsSearch: null,
    analyticsOptionsSearchTerm: '',
    optionsLoading: false,
    analyticsActiveFiltersCount: 0,
    fetchAllAssetTypesData: false,
    fetchAllManufacturersData: false,
    fetchAllInstalledSoftwareData: false,
    fetchAllNotInstalledSoftwareData: false,
    fetchAllMemoryData: false,
    fetchAllWarrantyData: false,
    fetchAllStorageData: false,
    fetchAllDeviceEncryptionData: false,
    fetchAllOSData: false,
    fetchAllSourcesData: false,
    fetchAllNonSourcesData: false,
    fetchAllLocationsData: false,
    fetchAllStatusData: false,
    operatingSystemCardData: null,
    applicationsCardData: null,
    availabilityCardData: null,
    locationCardData: null,
    warrantyCardData: null,
    modelCardData: null,
    assetTypeCardData: null,
    departmentCardData: null,
    costCardData: null,
    firmwareCardData: null,
    resetOSOptions: false,
    resetSoftwareOptions: false,
    resetSourcesOptions: false,
    riskCenterWidgetData: {},
    analyticsPreferences: [],
    analyticsSelectedColumns: [],
    analyticsUnselectedColumns: [],
    locationAndUsageModal: false,
    selectedCardDataItems: [],
    unSelectedCardDataItems: [],
    customLifecycles: [],
    automaticLifecycles: [],
    riskItemSummaries: [],
    metricCustomOptions: [],
    displayRiskCenterSection: false,
    hasAgentInstalled: false,
    totalFilteredAssetsCount: 0,
    auditHistoryItems: [],
    historyPageCount: 0,
    historyPerPage: 25,
    historyLoading: false,
  },

  mutations: {
    setPreventTableLoading(state, preventTableLoading) {
      state.preventTableLoading = preventTableLoading;
    },
    setPreviousRoute(state, previousRoute) {
      state.previousRoute = previousRoute;
    },
    setActiveTab(state, tab) {
      state.activeTab = tab;
    },
    setPeople(state, isPeople) {  
      state.isPeople = isPeople;
    },
    setAssetNameColumnWidth(state, width) {
      state.assetNameColumnWidth = width;
    },
    resetAsset: (state) => {
      state.assetTags = [];
      state.softwares = [];
      state.assetTypes = [];
      state.statusTypes = [];
      state.currentAsset = {};
      state.depreciations = [];
      state.cardDataItems = [];
      state.qRCodeSettings = [];
      state.companyProducts = [];
      state.selectedColumns = [];
      state.assetPreferences = [];
      state.unselectedColumns = [];
      state.companyLocations = null;
      state.selectedCardDataItems = [];
      state.assetAvailabilityStatus = [];
      state.associateTicketsHistory = null;
      state.companyUsersAndGroupsOptions.companyUsers = [];
      state.companyUsersAndGroupsOptions.companyUsersAndGroups = [];
      state.companyUsersAndGroupsOptions.auditedUsers = [];
    },
    setCustomLifecycles: (state, customLifecycles) => {
      state.customLifecycles = customLifecycles;
    },
    setAutomaticLifecycles: (state, automaticLifecycles) => {
      state.automaticLifecycles = automaticLifecycles;
    },
    setCompanyIntegrations: (state, integrations) => {
      state.companyIntegrations = integrations;
    },
    setIntegrationsWithAlerts: (state, integration) => {
      state.integrationsWithAlerts = integration;
    },
    setDeletingIntegrations: (state, integration) => {
      state.deletingIntegrations = integration;
    },
    setLoadingAccounts: (state, loadingAccounts) => {
      state.loadingAccounts = loadingAccounts;
    },
    updateAllCheckedAssets(state, params) {
      for (let idx = 0; idx < state.checkedAssets.length; idx += 1) {
        state.checkedAssets[idx][params.field] = params.value;
      }
    },
    clearCheckedAssets(state) {
      state.checkedAssets.splice(0, state.checkedAssets.length);
    },
    removeCheckedAsset(state, idx) {
      state.checkedAssets.splice(idx, 1);
    },
    addCheckedAsset(state, checkedAsset) {
      state.checkedAssets.push(checkedAsset);
    },
    setCompanyLogoUrl(state, logoUrl) {
      state.companyLogoUrl = logoUrl;
    },
    selectAllDiscoveredAssets(state)  {
      if (state.checkedAssets.length === state.discoveredAssets.length) {
        state.checkedAssets.splice(0, state.checkedAssets.length);
      } else {
        state.checkedAssets.splice(0,
          state.checkedAssets.length,
          ...state.discoveredAssets.map(asset => ({ ...asset}))
        );
      }
    },
    setUsedBy: (state, id) => {
      state.usedBy = id;
    },
    setManagedBy: (state, id) => {
      state.managedBy = id;
    },
    setAssetTypeData: (state, assetTypeData) => {
      state.assetTypeData = assetTypeData;
    },
    setTopUsers: (state, users) => {
      state.topUsers = users;
    },
    setSoftwareTypes: (state, softwareTypes) => {
      state.softwareTypes = softwareTypes;
    },
    setCompanyLocations: (state, locations) => {
      state.companyLocations = locations;
    },
    setCompanyProducts: (state, products) => {
      state.companyProducts = products;
    },
    setDiscoveredAssetStatus: (state, discoveredAssetStatus) => {
      state.discoveredAssetStatus = discoveredAssetStatus;
    },
    setAssetDiscovery: (state, assetDiscovery) => {
      state.assetDiscovery = assetDiscovery;
    },
    setDiscoveredAssetType: (state, discoveredAssetType) => {
      state.discoveredAssetType = discoveredAssetType;
    },
    setSuccessMessage: (state, successMessage) => {
      state.successMessage = successMessage;
    },
    setCurrentLocation: (state, location) => {
      state.currentLocation = location;
    },
    setDiscoveredAssetLocation: (state, location) => {
      state.discoveredAssetLocation = location;
    },
    setAssetTypes: (state, assetTypes) => {
      state.assetTypes = assetTypes;
    },
    setStatusTypes: (state, statusTypes) => {
      state.statusTypes = statusTypes;
    },
    setAssetAvailabilityStatus: (state, statuses) => {
      state.assetAvailabilityStatus = statuses;
    },
    setAssetPreferences: (state, preferences) => {
      state.assetPreferences = preferences;
    },
    setDiscoveredAssetPreferences: (state, preferences) => {
      state.discoveredAssetPreferences = preferences;
    },
    setQRCodeSettings: (state, qrCodeSettings) => {
      Object.assign(state.qrCodeSettings, qrCodeSettings);
    },
    setSearch: (state, search) => {
      state.search = search;
    },
    setSearchLogs: (state, search) => {
      state.searchLogs = search;
    },
    setShowArchived: (state, arc) => {
      state.showArchived = arc;
    },
    setPageCount: (state, count) => {
      state.pageCount = count;
    },
    setPageCountLogs: (state, count) => {
      state.pageCountLogs = count;
    },
    setTotalRecord: (state, total) => {
      state.totalRecord = total;
    },
    setTotalUnfilteredRecords: (state, total) => {
      state.totalUnfilteredRecords = total;
    },
    setCurrentAsset: (state, cAsset) => {
      state.currentAsset = cAsset;
    },
    setAssociatedTicketsHistory: (state, tHistory) => {
      state.associateTicketsHistory = tHistory;
    },
    setCurrentAssetProduct: (state, product) => {
      state.currentAssetProduct = product;
    },
    setCurrentDiscoveredAsset: (state, cAsset) => {
      state.currentDiscoveredAsset = cAsset;
    },
    setSearchView: (state, sView) => {
      state.searchView = sView;
    },
    setSearchTerm: (state, sTerm) => {
      state.searchTerm = sTerm;
    },
    setSearchTermLogs: (state, sTerm) => {
      state.searchTermLogs = sTerm;
    },
    setPageIndex: (state, pageIndex) => {
      state.pageIndex = pageIndex;
    },
    setPageIndexLogs: (state, pageIndexLogs) => {
      state.pageIndexLogs = pageIndexLogs;
    },
    setWarrantyStatus: (state, status) => {
      state.warrantyStatus = status;
    },
    setActiveTypes(state, types) {
      state.activeMultiFilters.activeTypes = types;
      state.assetType = null;

      if (router.currentRoute.query.type) {
        router.replace({
          query: { },
        });
      }
    },
    setActiveSources(state, sources) {
      state.activeMultiFilters.activeSources = sources;
    },
    setActiveTags(state, tags) {
      state.activeMultiFilters.activeTags = tags;
    },
    setActiveWarranties(state, warranties) {
      state.activeMultiFilters.activeWarranties = warranties;
    },
    setActiveAvailabilities(state, availabilities) {
      state.activeMultiFilters.activeAvailabilities = availabilities;
    },
    setActiveLocations(state, locations) {
      state.activeMultiFilters.activeLocations = locations;
    },
    setActiveStatuses(state, statuses) {
      state.activeMultiFilters.activeStatuses = statuses;
    },
    setActiveDepartments(state, departments) {
      state.activeMultiFilters.activeDepartments = departments;
    },
    setAssetType: (state, assetType) => {
      state.assetType = assetType;
    },
    setAssetSource: (state, assetSource) => {
      state.assetSource = assetSource;
    },
    setAssetAvailableStatus: (state, assetAvailableStatus) => {
      state.assetAvailableStatus = assetAvailableStatus;
    },
    setDiscoveredAssetSource: (state, assetSource) => {
      state.discoveredAssetSource = assetSource;
    },
    setAssetStatus: (state, assetStatus) => {
      state.assetStatus = assetStatus;
    },
    setAssetTags: (state, assetTags) => {
      state.assetTags = assetTags.map(tag => ({ ...tag, checked: tag.checked || false }));
    },
    setCurrentTag: (state, currentTag) => {
      state.currentTag = currentTag;
    },

    setViewType: (state, viewType) => {
      state.viewType = viewType;
    },

    addTag: (state, tag) => {
      state.assetTags.push(tag);
    },
    setDiscoveredAssetsLogs: (state, discoveredAssetsLogs) => {
      state.discoveredAssetsLogs = discoveredAssetsLogs;
    },
    setDiscoveredAssets: (state, discoveredAssets) => {
      state.discoveredAssets = discoveredAssets;
    },
    setCheckedAssets: (state, checkedAssets) => {
      state.checkedAssets = checkedAssets;
    },
    clearDiscoveredAssetsLogs: (state) => {
      state.discoveredAssetsLogs.splice(0);
    },
    clearDiscoveredAssets: (state) => {
      state.discoveredAssets.splice(0);
    },
    setAssets: (state, assets) => {
      state.assets = assets;
    },
    updateAssets: (state, assets) => {
      state.assets.splice(0);
      assets.forEach((a) => {
        state.assets.push(a);
      });
    },
    addNewAsset: (state, newAsset) => {
      state.assetsArr.push(newAsset);
      state.assets.push(newAsset);
    },
    updateAsset: (state, asset) => {
      const index = state.assets.findIndex(tmpAsset => tmpAsset.id === asset.id);
      state.assets.splice(index, 1, asset);
    },
    setChannelKey: (state, key) => {
      state.channelKey = key;
    },
    setCompanyChannelKey: (state, key) => {
      state.companyChannelKey = key;
    },
    updateArchivedFiltered: (state, assets) => {
      state.archivedFiltered.splice(0);
      assets.forEach((a) => {
        state.archivedFiltered.push(a);
      });
    },
    setPageSize: (state, pageSize) => {
      state.pageSize = pageSize;
    },
    setPageSizeLogs: (state, pageSize) => {
      state.pageSizeLogs = pageSize;
    },
    setLoading: (state, status) => {
      state.loading = status;
    },
    setLoadingLogs: (state, status) => {
      state.loadingLogs = status;
    },
    setMerakiInfo: (state, merakiInfo) => {
      state.merakiInfo = merakiInfo;
    },
    clearLoading: (state) => {
      state.loading = false;
    },
    setInWarranty: (state, inWarranty) => {
      state.inWarranty = inWarranty;
    },
    setExpiringWarranty: (state, expiredWarranty) => {
      state.expiringWarranty = expiredWarranty;
    },
    setExpiredWarranty: (state, expiredWarranty) => {
      state.expiredWarranty = expiredWarranty;
    },
    setNoWarranty: (state, noWarranty) => {
      state.noWarranty = noWarranty;
    },
    setTotalWarranty: (state, totalWarranty) => {
      state.totalWarranty = totalWarranty;
    },
    setTotalIncomplete: (state, totalIncomplete) => { state.totalIncomplete = totalIncomplete; },
    setTotalUnrecognized: (state, totalUnrecognized) => { state.totalUnrecognized = totalUnrecognized; },
    setTotalReadyForImport: (state, totalReadyForImport) => { state.totalReadyForImport = totalReadyForImport; },
    setTotalImported: (state, totalImported) => { state.totalImported = totalImported; },
    setTotalIgnored: (state, totalIgnored) => { state.totalIgnored = totalIgnored; },
    setLastCreatedIncomplete: (state, lastCreatedIncomplete) => { state.lastCreatedIncomplete = lastCreatedIncomplete; },
    setLastCreatedReadyForImport: (state, lastCreatedReadyForImport) => { state.lastCreatedReadyForImport = lastCreatedReadyForImport; },
    setLastSeenCreatedIncomplete: (state, lastSeenCreatedIncomplete) => { state.lastSeenCreatedIncomplete = lastSeenCreatedIncomplete; },
    setLastSeenCreatedReadyForImport: (state, lastSeenCreatedReadyForImport) => { state.lastSeenCreatedReadyForImport = lastSeenCreatedReadyForImport; },
    setSoftwares: (state, softwares) => { state.softwares = softwares; },
    setDepreciations: (state, depreciations) => { state.depreciations = depreciations; },
    setDefaultSelectedColumns(state,selected) {
      state.selectedColumns = selected;
    },
    setCardDataItems(state, payload) {
      state.selectedCardDataItems = payload.selectedItems;
      state.unSelectedCardDataItems = payload.unselectedItems;
    },
    setDefaultUnSelectedColumns(state, unselected) {
      state.unselectedColumns = unselected;
    },
    setUnselectedColumns(state) {
      state.unselectedColumns = [...state.selectedColumns.filter(d => d.active), ...state.unselectedColumns];
      state.selectedColumns = state.selectedColumns.filter(d => !d.active);
    },
    setSelectedColumn(state, column) {
      state.selectedColumns = column;
    },
    setUnselectedColumn(state, column) {
      state.unselectedColumns = column;
    },
    setSelectedColumns(state) {
      state.selectedColumns = [...state.selectedColumns, ...state.unselectedColumns.filter(d => d.active)];
      state.unselectedColumns = state.unselectedColumns.filter(d => !d.active);
    },
    clearActive(state) {
      state.selectedColumns = state.selectedColumns.map(d => ({
        ...d,
        active: false,
      }));
      state.unselectedColumns = state.unselectedColumns.map(d => ({
        ...d,
        active: false,
      }));
    },
    setActiveOrder: (state, orderObject) => { state.activeOrder = orderObject; },
    setAssetsLoading: (state, loading) => { state.assetsLoading = loading; },
    clearAssetFilter: (state, filter) => { state[filter.filterName] = null; },
    setSortByOptions: (state, { activeSort, activeSortDirection }) => {
      state.sortByOptions = {
        activeSort,
        activeSortDirection,
      };
    },
    setReloadTypes: (state, value) => {
      state.reloadTypes = value;
    },
    setAppsLocationSummary: (state, value) => {
      state.appsLocationSummary = value;
    },
    setActiveImportFileName: (state, value) => {
      state.activeImportFileName = value;
    },
    setLastSeenDiscoveryToolTab: (state, date) => {
      state.lastSeenDiscoveryToolTab = date;
    },
    setHasUnseenDiscoveryTool: (state, boolean) => {
      state.hasUnseenDiscoveryTool = boolean;
    },
    setLastAgentVersionCreatedAt: (state, value) => {
      state.lastAgentVersionCreatedAt = value;
    },
    setLastProbeVersionCreatedAt: (state, value) => {
      state.lastProbeVersionCreatedAt = value;
    },
    setAssetInsightsSelectedChartTypes: (state, chartType) => {
      state.assetInsightsSelectedChartTypes = [...state.assetInsightsSelectedChartTypes.filter(v => v.id !== chartType.id), chartType];
    },
    setAssetDepartments: (state, departments) => {
      state.assetDepartments = departments;
    },
    setAssetDepartment: (state, department) => {
      state.assetDepartment = department;
    },
    setAssetManufacturers: (state, manufacturers) => {
      state.assetManufacturers = manufacturers;
    },
    setAssetOperatingSystems: (state, operatingSystems) => {
      state.assetOperatingSystems = operatingSystems;
    },
    setAnalyticsTypeFilter: (state, typeFilter) => {
      state.analyticsTypeFilter = typeFilter;
    },
    setAnalyticsManufacturerFilter: (state, manufacturerFilter) => {
      state.analyticsManufacturerFilter = manufacturerFilter;
    },
    setAnalyticsWarrantyFilter: (state, warrantyFilter) => {
      state.analyticsWarrantyFilter = warrantyFilter;
    },
    setAnalyticsDeviceEncryption: (state, deviceEncryption) => {
      state.analyticsDeviceEncryption = deviceEncryption;
    },
    setAnalyticsAvailabilityFilter: (state, availabilityFilter) => {
      state.analyticsAvailabilityFilter = availabilityFilter;
    },
    setAnalyticsOperatingSystem: (state, operatingSystem) => {
      state.analyticsOperatingSystem = operatingSystem;
    },
    setAnalyticsIntegrationSource: (state, integratedSource) => {
      state.analyticsIntegrationSource = integratedSource;
    },
    setAnalyticsNonIntegrationSource: (state, nonIntegratedSource) => {
      state.analyticsNonIntegrationSource = nonIntegratedSource;
    },
    setAnalyticsLocationFilter: (state, locationFilter) => {
      state.analyticsLocationFilter = locationFilter;
    },
    setAnalyticsMemoryFilter: (state, memoryFilter) => {
      state.analyticsMemoryFilter = memoryFilter;
    },
    setAnalyticsStorageFilter: (state, storageFilter) => {
      state.analyticsStorageFilter = storageFilter;
    },
    setAnalyticsInstalledSoftware: (state, installedSoftwateFilter) => {
      state.analyticsInstalledSoftware = installedSoftwateFilter;
    },
    setAnalyticsNotInstalledSoftware: (state, notInstalledSoftwateFilter) => {
      state.analyticsNotInstalledSoftware = notInstalledSoftwateFilter;
    },
    setTotalSoftwareCount: (state, softwareCount) => {
      state.totalSoftwareCount = softwareCount;
    },
    setTotalStatusesCount: (state, statusesCount) => {
      state.totalStatusesCount = statusesCount;
    },
    setTotalTagsCount: (state, tagsCount) => {
      state.totalTagsCount = tagsCount;
    },
    setTotalAssetTypesCount: (state, typesCount) => {
      state.totalAssetTypesCount = typesCount;
    },
    setTotalManufacturersCount: (state, manufacturerCount) => {
      state.totalManufacturersCount = manufacturerCount;
    },
    setTotalOperatingSystemsCount: (state, osCount) => {
      state.totalOperatingSystemsCount = osCount;
    },
    setFilterOptionSelected: (state, optionSelected) => {
      state.filterOptionSelected = optionSelected;
    },
    setAnalyticsOptionsSearch: (state, optionsSearch) => {
      state.analyticsOptionsSearch = optionsSearch;
    },
    setAnalyticsOptionsSearchTerm: (state, optionsSearchTerm) => {
      state.analyticsOptionsSearchTerm = optionsSearchTerm;
    },
    setOptionsLoading: (state, opLoad) => {
      state.optionsLoading = opLoad;
    },
    setAnalyticsActiveFiltersCount: (state, count) => {
      state.analyticsActiveFiltersCount = count;
    },
    setFetchAllAssetTypesData: (state, value) => {
      state.fetchAllAssetTypesData = value;
    },
    setFetchAllManufacturersData: (state, value) => {
      state.fetchAllManufacturersData = value;
    },
    setFetchAllInstalledSoftwareData: (state, value) => {
      state.fetchAllInstalledSoftwareData = value;
    },
    setFetchAllNotInstalledSoftwareData: (state, value) => {
      state.fetchAllNotInstalledSoftwareData = value;
    },
    setFetchAllWarrantyData: (state, value) => {
      state.fetchAllWarrantyData = value;
    },
    setFetchAllMemoryData: (state, value) => {
      state.fetchAllMemoryData = value;
    },
    setFetchAllStorageData: (state, value) => {
      state.fetchAllStorageData = value;
    },
    setFetchAllDeviceEncryptionData: (state, value) => {
      state.fetchAllDeviceEncryptionData = value;
    },
    setFetchAllOSData: (state, value) => {
      state.fetchAllOSData = value;
    },
    setFetchAllSourcesData: (state, value) => {
      state.fetchAllSourcesData = value;
    },
    setFetchAllNonSourcesData: (state, value) => {
      state.fetchAllNonSourcesData = value;
    },
    setFetchAllLocationsData: (state, value) => {
      state.fetchAllLocationsData = value;
    },
    setFetchAllStatusData: (state, value) => {
      state.fetchAllStatusData = value;
    },
    setOperatingSystemCardData: (state, operatingSystemData) => { 
      state.operatingSystemCardData = operatingSystemData;
    },
    setApplicationsCardData: (state, applicationsCardData) => { 
      state.applicationsCardData = applicationsCardData;
    },
    setAvailabilityCardData: (state, availabilityCardData) => {
      state.availabilityCardData = availabilityCardData;
    },
    setLocationCardData: (state, locationCardData) => { 
      state.locationCardData = locationCardData;
    },
    setWarrantyCardData: (state, warrantyCardData) => { 
      state.warrantyCardData = warrantyCardData;
    },
    setModelCardData: (state, modelCardData) => { 
      state.modelCardData = modelCardData;
    },
    setAssetTypeCardData: (state, assetTypeCardData) => { 
      state.assetTypeCardData = assetTypeCardData;
    },
    setDepartmentCardData: (state, departmentCardData) => { 
      state.departmentCardData = departmentCardData;
    },
    setCostCardData: (state, costCardData) => { 
      state.costCardData = costCardData;
    },
    setFirmwareCardData: (state, firmwareCardData) => { 
      state.firmwareCardData = firmwareCardData;
    }, 
    setResetOSOptions: (state, value) => {
      state.resetOSOptions = value;
    },
    setResetSoftwareOptions: (state, value) => {
      state.resetSoftwareOptions = value;
    },
    setResetSourcesOptions: (state, value) => {
      state.resetSourcesOptions = value;
    },
    setAnalyticsPreferences: (state, preferences) => {
      state.analyticsPreferences = preferences;
    },
    setAnalyticsSelectedColumn(state, column) {
      state.analyticsSelectedColumns = column;
    },
    setAnalyticsUnselectedColumn(state, column) {
      state.analyticsUnselectedColumns = column;
    },
    setAnalyticsSelectedColumns(state) {
      state.analyticsSelectedColumns = [...state.analyticsSelectedColumns, ...state.analyticsUnselectedColumns.filter(column => column.active)];
      state.analyticsUnselectedColumns = state.analyticsUnselectedColumns.filter(column => !column.active);
    },
    setAnalyticsUnselectedColumns(state) {
      state.analyticsUnselectedColumns = [...state.analyticsSelectedColumns.filter(column => column.active), ...state.analyticsUnselectedColumns];
      state.analyticsSelectedColumns = state.analyticsSelectedColumns.filter(column => !column.active);
    },
    clearAnalyticsActive(state) {
      state.analyticsSelectedColumns = state.analyticsSelectedColumns.map(column => ({
        ...column,
        active: false,
      }));
      state.analyticsUnselectedColumns = state.analyticsUnselectedColumns.map(column => ({
        ...column,
        active: false,
      }));
    },
    setLocationAndUsageModal(state) {
      state.locationAndUsageModal = !state.locationAndUsageModal;
    },
    setDisplayRiskCenterSection: (state, value) => {
      state.displayRiskCenterSection = value;
    },
    setHasAgentInstalled: (state, value) => {
      state.hasAgentInstalled = value;
    },
    setRiskCenterWidgetData(state, { widgetId, data }) {
      state.riskCenterWidgetData = {
        ...state.riskCenterWidgetData,
        [widgetId]: data,
      };
    },
    setRiskItemSummaries(state, riskItemSummaries) {
      state.riskItemSummaries = riskItemSummaries;
    },
    setMetricCustomOptions(state, metricCustomOptions) {
      state.metricCustomOptions = metricCustomOptions;
    },
    setTotalFilteredAssetsCount(state, totalFilteredAssetsCount) {
      state.totalFilteredAssetsCount = totalFilteredAssetsCount;
    },
    setAuditHistoryItems(state, items) {
      state.auditHistoryItems = items;
    },
    setHistoryPageCount(state, items) {
      state.historyPageCount = items;
    },
    setHistoryPerPage(state, items) {
      state.historyPerPage = items;
    },
    setAssetActivities(state, activities) {
      state.assetActivities = activities;
    },
    setAssetActivitiesPage(state, page) {
      state.assetActivitiesPage = page;
    },
    setAssetActivitiesPerPage(state, perPage) {
      state.assetActivitiesPerPage = perPage;
    },
    setAssetActivitiesPageCount(state, pageCount) {
      state.assetActivitiesPageCount = pageCount;
    },
    setHistoryLoading: (state, status) => {
      state.historyLoading = status;
    },
  },
  getters: {
    preventTableLoading: (state) => state.preventTableLoading,
    previousRoute: (state) => state.previousRoute,
    activeTab: (state) => state.activeTab,
    isPeople: (state) => state.isPeople,
    assetNameColumnWidth: state => state.assetNameColumnWidth,
    customLifecycles: state => state.customLifecycles,
    automaticLifecycles: state => state.automaticLifecycles,
    companyIntegrations: state => state.companyIntegrations,
    deletingIntegrations: state => state.deletingIntegrations,
    integrationsWithAlerts: state => state.integrationsWithAlerts,
    loadingAccounts: state => state.loadingAccounts,
    usedBy: state => state.usedBy,
    managedBy: state => state.managedBy,
    companyLogoUrl: state => state.companyLogoUrl,
    successMessage: state => state.successMessage,
    search: state => state.search,
    searchLogs: state => state.searchLogs,
    activeTypes: (state) => state.activeMultiFilters.activeTypes,
    activeSources: (state) => state.activeMultiFilters.activeSources,
    activeTags: (state) => state.activeMultiFilters.activeTags,
    activeWarranties: (state) => state.activeMultiFilters.activeWarranties,
    activeAvailabilities: (state) => state.activeMultiFilters.activeAvailabilities,
    activeLocations: (state) => state.activeMultiFilters.activeLocations,
    activeStatuses: (state) => state.activeMultiFilters.activeStatuses,
    activeDepartments: (state) => state.activeMultiFilters.activeDepartments,
    assetTypes: state => state.assetTypes,
    statusTypes: state => state.statusTypes,
    assetTags: state => state.assetTags,
    assetPreferences: state => state.assetPreferences,
    discoveredAssetPreferences: state => state.discoveredAssetPreferences,
    qrCodeSettings: state => state.qrCodeSettings,
    assetSources: state => state.assetSources,
    assetAvailabilityStatus: state => state.assetAvailabilityStatus,
    assetInsightsSelectedChartTypes: state => state.assetInsightsSelectedChartTypes,
    warrantyStates: state => state.warrantyStates,
    assetStatuses: state => state.assetStatuses,
    integratedSources: state => state.integratedSources,
    pageSize: state => state.pageSize,
    pageSizeLogs: state => state.pageSizeLogs,
    pageIndex: state => state.pageIndex,
    pageIndexLogs: state => state.pageIndexLogs,
    currentLocation: state => state.currentLocation,
    discoveredAssetLocation: state => state.discoveredAssetLocation,
    currentTag: state => state.currentTag,
    assets: state => state.assets,
    merakiInfo: state => state.merakiInfo,
    loadingStatus: state => state.loadingStatus,
    loading: state => state.loading,
    loadingLogs: state => state.loadingLogs,
    discoveredAssets: state => state.discoveredAssets,
    discoveredAssetsLogs: state => state.discoveredAssetsLogs,
    checkedAssets: state => state.checkedAssets,
    currentAsset: state => state.currentAsset,
    associateTicketsHistory: state => state.associateTicketsHistory,
    currentAssetProduct: state => state.currentAssetProduct,
    currentDiscoveredAsset: state => state.currentDiscoveredAsset,
    showArchived: state => state.showArchived,
    searchView: state => state.searchView,
    searchTerm: state => state.searchTerm,
    searchTermLogs: state => state.searchTermLogs,
    archivedFiltered: state => state.archivedFiltered,
    pageCount: state => state.pageCount,
    pageCountLogs: state => state.pageCountLogs,
    totalRecord: state => state.totalRecord,
    totalUnfilteredRecords: state => state.totalUnfilteredRecords,
    channelKey: state => state.channelKey,
    companyChannelKey: state => state.companyChannelKey,
    warrantyStatus: state => state.warrantyStatus,
    assetStatus: state => state.assetStatus,
    assetType: state => state.assetType,
    assetSource: state => state.assetSource,
    assetAvailableStatus: state => state.assetAvailableStatus,
    discoveredAssetSource: state => state.discoveredAssetSource,
    discoveredAssetStatus: state => state.discoveredAssetStatus,
    assetDiscovery: state => state.assetDiscovery,
    discoveredAssetType: state => state.discoveredAssetType,
    companyLocations: state => state.companyLocations,
    companyProducts: state => state.companyProducts,
    assetTypeData: state => state.assetTypeData,
    softwareTypes: state => state.softwareTypes,
    activeMultiFiltersCount: state =>  Object.values(state.activeMultiFilters).reduce((sum, arr) => sum + arr.length, 0),

    inWarranty: state => state.inWarranty,
    expiringWarranty: state => state.expiringWarranty,
    expiredWarranty: state => state.expiredWarranty,
    noWarranty: state => state.noWarranty,
    totalWarranty: state => state.totalWarranty,
    totalIncomplete: state => state.totalIncomplete,
    totalUnrecognized: state => state.totalUnrecognized,
    totalReadyForImport: state => state.totalReadyForImport,
    totalImported: state => state.totalImported,
    totalIgnored: state => state.totalIgnored,
    discoveredAssetsCount: state => state.totalIncomplete + state.totalReadyForImport,
    lastCreatedIncomplete: state => state.lastCreatedIncomplete,
    lastCreatedReadyForImport: state => state.lastCreatedReadyForImport,
    lastSeenCreatedIncomplete: state => state.lastSeenCreatedIncomplete,
    lastSeenCreatedReadyForImport: state => state.lastSeenCreatedReadyForImport,
    topUsers: state => state.topUsers,
    tags: state => state.tags,
    softwares: state => state.softwares,
    depreciations: state => state.depreciations,
    assetsLoading: state => state.assetsLoading,
    viewType: state => state.viewType,
    baseFilterParams: (state) => {
      const params = {};
      if (state.activeMultiFilters.activeTypes.length > 0) {
        params.company_asset_type_ids = state.activeMultiFilters.activeTypes.map(type => type.id);
      }
      if (state.activeMultiFilters.activeTags.length > 0) {
        params.active_tags = state.activeMultiFilters.activeTags.map(tag => tag.id);
      }
      if (state.activeMultiFilters.activeWarranties.length > 0) {
        params.warranty_statuses = state.activeMultiFilters.activeWarranties.map(warranty => warranty.id);
      }
      if (state.activeMultiFilters.activeAvailabilities.length > 0) {
        params.statuses = state.activeMultiFilters.activeAvailabilities.map(availability => availability.id);
      }
      if (state.activeMultiFilters.activeLocations.length > 0) {
        params.location_ids = state.activeMultiFilters.activeLocations.map(location => location.id);
      }
      if (state.activeMultiFilters.activeStatuses.length > 0) {
        params.archives = state.activeMultiFilters.activeStatuses.map(status => status.id === 'archived');
      }
      if (state.activeMultiFilters.activeDepartments.length > 0) {
        params.department_ids = state.activeMultiFilters.activeDepartments.map(asset => asset.department.id);
      }
      if (state.activeMultiFilters.activeSources.length > 0) {
        params.sources = state.activeMultiFilters.activeSources.map(source => source.id);
      }
      if (state.assetType) {
        params.company_asset_type_id = state.assetType.id;
      }
      if (state.assetSource) {
        params.source = state.assetSource.id;
      }
      if (state.assetAvailableStatus) {
        params.status = state.assetAvailableStatus.id;
      }
      if (state.currentLocation) {
        params.location_id = state.currentLocation.id;
      }
      if (state.currentTag) {
        params.tag = state.currentTag.id;
      }
      if (state.assetStatus && state.assetStatus.id === 'archived') {
        params.archived = 'true';
      } else if (state.assetStatus && state.assetStatus.id === 'active') {
        params.archived = 'false';
      } else {
        params.archived = 'all';
      }
      if (state.activeTab && state.activeTab === 'archived') {
        params.archived = 'true';
      }
      if (state.warrantyStatus) {
        params.warranty_status = state.warrantyStatus.id;
      }
      if (state.usedBy) {
        params.used_by = state.usedBy;
      }
      if (state.managedBy) {
        params.managed_by = state.managedBy;
      }
      if (router.currentRoute.query.type) {
        if (router.currentRoute.query.multi) {
          const queryTypeId = Number(router.currentRoute.query.type);
          state.activeMultiFilters.activeTypes = state.activeMultiFilters.activeTypes.filter(
            (type) => type.id === queryTypeId
          );

          params.company_asset_type_ids = [router.currentRoute.query.type];
        } else {
          params.company_asset_type_id = [router.currentRoute.query.type];
        }
      }
      if (router.currentRoute.query.location_id) {
        params.location_id = router.currentRoute.query.location_id;
      }
      if (router.currentRoute.query.warranty_filter) {
        params.warranty_status = router.currentRoute.query.warranty_filter;
      }
      if (state.assetDepartment) {
        params.asset_department_id = state.assetDepartment.department.id;
      }
      if (state.companyFilter) {
        params.companies_ids = state.companyFilter.map((company) => company.id);
      }
      return params;
    },
    analyticsFilterParams: (state) => {
      const analyticsParams = {};
      if (state.analyticsTypeFilter.length) {
        analyticsParams.analytics_asset_types = state.analyticsTypeFilter.map(type => type.id);
      }
      if (state.analyticsManufacturerFilter.length) {
        analyticsParams.analytics_asset_manufacturers = state.analyticsManufacturerFilter.map(manufacturer => manufacturer.name);
      }
      if (state.analyticsWarrantyFilter.length) {
        analyticsParams.analytics_asset_warranty = state.analyticsWarrantyFilter.map(warranty => warranty.id);
      }
      if (state.analyticsDeviceEncryption.length) {
        analyticsParams.analytics_device_encryption = state.analyticsDeviceEncryption.map(encryption => encryption.id);
      }
      if (state.analyticsAvailabilityFilter.length) {
        analyticsParams.analytics_availability_status = state.analyticsAvailabilityFilter.map(status => status.id);
      }
      if (state.analyticsOperatingSystem.length) {
        analyticsParams.analytics_operating_system = state.analyticsOperatingSystem.map(operatingSystem => operatingSystem.name);
      }
      if (state.analyticsIntegrationSource.length) {
        analyticsParams.analytics_integration_source = state.analyticsIntegrationSource.map(integrationSource => integrationSource.id);
      }
      if (state.analyticsNonIntegrationSource.length) {
        analyticsParams.analytics_non_integration_source = state.analyticsNonIntegrationSource.map(nonIntegrationSource => nonIntegrationSource.id);
      }
      if (state.analyticsLocationFilter.length) {
        analyticsParams.analytics_location_filter = state.analyticsLocationFilter.map(location => location.id);
      }
      if (state.analyticsMemoryFilter.length) {
        analyticsParams.analytics_memory_filter = state.analyticsMemoryFilter.map(memory => memory.id);
      }
      if (state.analyticsStorageFilter.length) {
        analyticsParams.analytics_storage_filter = state.analyticsStorageFilter.map(storage => storage.id);
      }
      if (state.analyticsInstalledSoftware.length) {
        analyticsParams.analytics_installed_software = state.analyticsInstalledSoftware.map(installedSoftware => installedSoftware.name);
      }
      if (state.analyticsNotInstalledSoftware.length) {
        analyticsParams.analytics_not_installed_software = state.analyticsNotInstalledSoftware.map(notInstalledSoftware => notInstalledSoftware.name);
      }
      const stateToAnalyticsParams = {
        fetchAllAssetTypesData: 'fetch_all_asset_type_data',
        fetchAllManufacturersData: 'fetch_all_manufacturer_data',
        fetchAllInstalledSoftwareData: 'fetch_all_installed_software_data',
        fetchAllNotInstalledSoftwareData: 'fetch_all_not_installed_software_data',
        fetchAllMemoryData: 'fetch_all_memory_data',
        fetchAllWarrantyData: 'fetch_all_warranty_data',
        fetchAllStorageData: 'fetch_all_storage_data',
        fetchAllDeviceEncryptionData: 'fetch_all_device_encryption_data',
        fetchAllOSData: 'fetch_all_os_data',
        fetchAllSourcesData: 'fetch_all_sources_data',
        fetchAllNonSourcesData: 'fetch_all_non_sources_data',
        fetchAllLocationsData: 'fetch_all_locations_data',
        fetchAllStatusData: 'fetch_all_status_data',
      };
      Object.entries(stateToAnalyticsParams).forEach(([stateProperty, analyticsParam]) => {
        if (state[stateProperty]) {
          analyticsParams[analyticsParam] = true;
        }
      });
      return analyticsParams;
    },
    selectedColumns: state  => state.selectedColumns,
    unselectedColumns: state  => state.unselectedColumns,
    activeOrder: state => state.activeOrder,
    assetFiltersArray: (state) => [
      { filter: state.assetType, filterName: 'assetType' },
      { filter: state.assetSource, filterName: 'assetSource' },
      { filter: state.activeMultiFilters.activeStatuses, filterName: 'assetStatus' },
      { filter: state.warrantyStatus, filterName: 'warrantyStatus' },
      { filter: state.currentTag, filterName: 'currentTag' },
      { filter: state.currentLocation, filterName: 'currentLocation' },
      { filter: state.assetAvailableStatus, filterName: 'assetAvailableStatus' },
      { filter: state.assetDepartment, filterName: 'assetDepartment' },
    ],
    activeFilters: (state, getters) => getters.assetFiltersArray.filter(f => f.filter !== null),
    activeFiltersCount: (state, getters) => getters.activeFilters.length,
    sortByOptions: state => state.sortByOptions,
    reloadTypes: state => state.reloadTypes,
    appsLocationSummary: state => state.appsLocationSummary,
    activeImportFileName: state => state.activeImportFileName,
    hasUnseenDiscoveryTool: state => state.hasUnseenDiscoveryTool,
    lastSeenDiscoveryToolTab: state => state.lastSeenDiscoveryToolTab,
    lastAgentVersionCreatedAt: state => state.lastAgentVersionCreatedAt,
    lastProbeVersionCreatedAt: state => state.lastProbeVersionCreatedAt,
    assetDepartments: state => state.assetDepartments,
    assetDepartment: state => state.assetDepartment,
    assetManufacturers: state => state.assetManufacturers,
    assetOperatingSystems: state => state.assetOperatingSystems,
    analyticsTypeFilter: state => state.analyticsTypeFilter,
    analyticsManufacturerFilter: state => state.analyticsManufacturerFilter,
    analyticsWarrantyFilter: state => state.analyticsWarrantyFilter,
    analyticsDeviceEncryption: state => state.analyticsDeviceEncryption,
    analyticsAvailabilityFilter: state => state.analyticsAvailabilityFilter,
    analyticsIntegrationSource: state => state.analyticsIntegrationSource,
    analyticsNonIntegrationSource: state => state.analyticsNonIntegrationSource,
    analyticsOperatingSystem: state => state.analyticsOperatingSystem,
    analyticsLocationFilter: state => state.analyticsLocationFilter,
    analyticsMemoryFilter: state => state.analyticsMemoryFilter,
    analyticsStorageFilter: state => state.analyticsStorageFilter,
    analyticsInstalledSoftware: state => state.analyticsInstalledSoftware,
    analyticsNotInstalledSoftware: state => state.analyticsNotInstalledSoftware,
    totalSoftwareCount: state => state.totalSoftwareCount,
    totalStatusesCount: state => state.totalStatusesCount,
    totalTagsCount: state => state.totalTagsCount,
    totalAssetTypesCount: state => state.totalAssetTypesCount,
    totalManufacturersCount: state => state.totalManufacturersCount,
    totalOperatingSystemsCount: state => state.totalOperatingSystemsCount,
    filterOptionSelected: state => state.filterOptionSelected,
    analyticsOptionsSearch: state => state.analyticsOptionsSearch,
    analyticsOptionsSearchTerm: state => state.analyticsOptionsSearchTerm,
    optionsLoading: state => state.optionsLoading,
    analyticsActiveFiltersCount: state => state.analyticsActiveFiltersCount,
    fetchAllAssetTypesData: state => state.fetchAllAssetTypesData,
    fetchAllManufacturersData: state => state.fetchAllManufacturersData,
    fetchAllInstalledSoftwareData: state => state.fetchAllInstalledSoftwareData,
    fetchAllNotInstalledSoftwareData: state => state.fetchAllNotInstalledSoftwareData,
    fetchAllMemoryData: state => state.fetchAllMemoryData,
    fetchAllWarrantyData: state => state.fetchAllWarrantyData,
    fetchAllStorageData: state => state.fetchAllStorageData,
    fetchAllDeviceEncryptionData: state => state.fetchAllDeviceEncryptionData,
    fetchAllOSData: state => state.fetchAllOSData,
    fetchAllSourcesData: state => state.fetchAllSourcesData,
    fetchAllNonSourcesData: state => state.fetchAllNonSourcesData,
    fetchAllLocationsData: state => state.fetchAllLocationsData,
    fetchAllStatusData: state => state.fetchAllStatusData,
    operatingSystemCardData: state => state.operatingSystemCardData,
    applicationsCardData: state => state.applicationsCardData,
    availabilityCardData: state => state.availabilityCardData,
    locationCardData: state => state.locationCardData,
    warrantyCardData: state => state.warrantyCardData,
    modelCardData: state => state.modelCardData,
    assetTypeCardData: state => state.assetTypeCardData,
    departmentCardData: state => state.departmentCardData,
    costCardData: state => state.costCardData,
    firmwareCardData: state => state.firmwareCardData,
    resetOSOptions: state => state.resetOSOptions,
    resetSoftwareOptions: state => state.resetSoftwareOptions,
    resetSourcesOptions: state => state.resetSourcesOptions,
    analyticsPreferences: state => state.analyticsPreferences,
    analyticsSelectedColumns: state => state.analyticsSelectedColumns,
    displayRiskCenterSection: state => state.displayRiskCenterSection,
    hasAgentInstalled: state => state.hasAgentInstalled,
    analyticsUnselectedColumns: state => state.analyticsUnselectedColumns,
    locationAndUsageModal: state => state.locationAndUsageModal,
    selectedCardDataItems: state => state.selectedCardDataItems,
    unSelectedCardDataItems: state => state.unSelectedCardDataItems,
    riskCenterWidgetData: (state) => state.riskCenterWidgetData,
    riskItemSummaries: (state) => state.riskItemSummaries,
    metricCustomOptions: (state) => state.metricCustomOptions,
    totalFilteredAssetsCount: (state) => state.totalFilteredAssetsCount,
    auditHistoryItems: state => state.auditHistoryItems,
    historyPageCount: state => state.historyPageCount,
    historyPerPage: state => state.historyPerPage,
    assetActivities: state => state.assetActivities,
    assetActivitiesPage: (state) => state.assetActivitiesPage,
    assetActivitiesPerPage: (state) => state.assetActivitiesPerPage,
    assetActivitiesPageCount: (state) => state.assetActivitiesPageCount,
    historyLoading: state => state.historyLoading,
  },
  actions: {
    fetchLifecycles({ commit }) {
      commit('setLoading',true);
      http
        .get('/asset_lifecycles.json')
        .then((res) => {
          commit('setCustomLifecycles', res.data.custom);
          commit('setAutomaticLifecycles', res.data.automatic);
          commit('setLoading', false);
        })
        .catch(() => {
          commit('setLoading', false);
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error loading asset lifecycles.`);
        });
    },
    fetchCompanyIntegrations({commit}) {
      commit('setLoading',true);
      http
        .get('/company_integrations.json')
        .then((res) => {
          commit('setCompanyIntegrations', res.data.integrations);
          commit('setLoadingAccounts', false);
          commit('setLoading', false);
         })
        .catch(() => {
          commit('setLoading', false);
          commit('setLoadingAccounts', false);
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error while fetching integrations account.`);
        });
    },
    fetchCompanyAlertsInfo({commit}) {
      commit('setLoading',true);
      http
        .get('/discovered_assets/discovered_asset_updates_all.json')
        .then((res) => {
          commit('setIntegrationsWithAlerts', res.data.completeInfo);
          commit('setLoading', false);
         })
        .catch(() => {
          commit('setLoading', false);
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error while fetching integrations alerts.`);
        });
    },
    fetchAssociateTicketsHistory({ commit }, ids) {
      const params = { 
        helpTicketIds: ids,
      };
      http
        .get('/managed_assets/get_associated_tickets_history.json', { params })
        .then((res) => {
          commit('setAssociatedTicketsHistory', res.data);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error while fetching associate tickets history.`);
        });
    },

    fetchAssetInfo({ commit, getters }, specificParams = {}) {
      const params = { ...getters.baseFilterParams, ...specificParams };

      http
        .get('/asset_type_summaries.json', { params })
        .then((res) => {
          commit('setAssetTypeData', res.data);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error retrieving the warranty info (${error.message}).`);
        });
    },

    fetchWarrantyInfo({ commit, getters }, specificParams = {}) {
      const params = { ...getters.baseFilterParams, ...specificParams };

      http.get('/warranty_summaries.json', { params }).then((res) => {
        commit('setInWarranty', res.data.inWarranty);
        commit('setExpiringWarranty', res.data.expiringWarranty);
        commit('setExpiredWarranty', res.data.expiredWarranty);
        commit('setNoWarranty', res.data.noWarranty);
        commit('setTotalWarranty', res.data.totalWarranty);
      }).catch((error) => {
        commit('GlobalStore/setErrorMessage', `Sorry, there was an error retrieving the warranty info (${error.message}).`);
      });
    },

    fetchCompanyLogoUrl({ commit }) {
      http
        .get('/company/logo_url.json', { params: { privilege_name: 'ManagedAsset' } })
        .then(res => {
          commit('setCompanyLogoUrl', res.data.companyLogoUrl);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage',`Sorry, there was an error fetching company logo.`);
        });
    },

    getInsightsSummary({ commit }, widgetType) {
      const widgetName = widgetType.widgetType;
      const params = { widgetType: widgetName };
      return http
        .get('/managed_assets/assets/insights.json', { params })
        .then((res) => {
          const { selectedWidget } = res.data;
          switch(widgetName) {
            case 'operating_system':
              commit('setOperatingSystemCardData', selectedWidget.operatingSystem);
              break;
            case 'applications':
              commit('setApplicationsCardData', selectedWidget.applications);
              break;
            case 'availability': 
              commit('setAvailabilityCardData', selectedWidget.availability);
              break;
            case 'location':
              commit('setLocationCardData', selectedWidget.location);
              break;
            case 'warranty':
              commit('setWarrantyCardData', selectedWidget.warranty);
              break;
            case 'model':
              commit('setModelCardData', selectedWidget.model);
              break;
            case 'asset_type': 
              commit('setAssetTypeCardData', selectedWidget.assetType);
              break;
            case 'department':
              commit('setDepartmentCardData', selectedWidget.department);
              break;
            case 'cost':
              commit('setCostCardData', selectedWidget.cost);
              break;
            case 'firmware':  
              commit('setFirmwareCardData', selectedWidget.firmware);
              break;
            default:
              return null;
          };
          return null;
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', `Please reload page and try again.`, { root: true });
        });
    },
    fetchRiskCenterWidgetData({ commit, getters }, params) {
      const mergedParams = { 
        ...getters.baseFilterParams, 
        ...params,
      };
      http
        .get('/managed_assets/risk_center_widget.json',{ params: mergedParams } )
        .then((res) => {
          commit('setRiskCenterWidgetData', { widgetId: params.id, data: res.data.widgetData });
        })
        .catch(() => {
          this.emitError('Sorry, there was an error gathering risk center data. Please refresh the page and try again.');
        });
    },
    getRiskCenterSummaries({ commit, getters }) {
      const filteredParams = { ...getters.baseFilterParams };
      http
        .get('/managed_assets/risk_center_summaries.json', { params: filteredParams })
        .then((res) => {
          commit('setRiskItemSummaries', res.data.assetRiskSummaries);
        })
        .catch(() => {
          this.emitError('Sorry, there was an error gathering risk center summary. Please refresh the page and try again.');
        });
    },
    getCustomMetricOptions({ commit }, params) {
      http
          .get('/managed_assets/custom_widget_options.json', { params })
          .then((res) => {
            commit('setMetricCustomOptions', res.data.options);
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error fetching data (${error.response.data.message}).`);
          });
    },
    archiveAsset({ commit, dispatch }, attrs) {
      http
        .post(`/managed_assets/${attrs.asset.id}/archive.json`)
        .then(() => {
          commit('setPageIndex', 0);
          commit('setSuccessMessage', 'Successfully archived this asset.');
          dispatch('fetchAssets');
          if (!(attrs.router.history && attrs.router.history.current && attrs.router.history.current.path === '/assets')) {
            attrs.router.push({ path: '/assets' });
          }
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error archiving this asset (${error.response.data.message})`);
        });
    },

    unarchiveAsset({ commit }, attrs) {
      http
        .post(`/managed_assets/${attrs.asset.id}/unarchive.json`)
        .then(() => {
          commit('setSuccessMessage', 'Successfully unarchived this asset.');
          if (!(attrs.router.history && attrs.router.history.current && attrs.router.history.current.path === '/assets')) {
            attrs.router.push({ path: '/assets' });
          }
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error unarchiving this asset (${error.response.data.message})`);
        });
    },

    deleteAsset({ commit }, attrs) {
      http
        .delete(`/managed_assets/${attrs.asset.id}.json`)
        .then(() => {
          commit('setPageIndex', 0);
          commit('setSuccessMessage', 'Successfully deleted this asset.');
          if (!(attrs.router.history && attrs.router.history.current && attrs.router.history.current.path === '/assets')) {
            attrs.router.push({ path: '/assets' });
          }
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error deleting this asset (${error.response.data.message})`);
        });
    },

    archiveSoftware({ getters, commit, dispatch}, attrs) {
      return http
        .put(`/managed_assets/${getters.currentAsset.id}/archived_asset_softwares/${attrs.software.id}.json`)
        .then(() => {
          commit('setSuccessMessage', 'Successfully archived this software.');
          dispatch('fetchAsset', getters.currentAsset.id);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error archiving this asset (${error.response.data.message})`);
        });
    },

    unarchiveSoftware({ getters, commit, dispatch}, attrs) {
      return http
        .delete(`/managed_assets/${getters.currentAsset.id}/archived_asset_softwares/${attrs.software.id}.json`)
        .then(() => {
          commit('setSuccessMessage', 'Successfully unarchived this software.');
          dispatch('fetchAsset', getters.currentAsset.id);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error unarchiving this asset (${error.response.data.message})`);
        });
    },

    deleteSoftware({ getters, commit, dispatch}, attrs) {
      return http
        .delete(`/managed_assets/${getters.currentAsset.id}/asset_softwares/${attrs.software.id}.json`)
        .then(() => {
          commit('setSuccessMessage', 'Successfully deleted this software.');
          dispatch('fetchAsset', getters.currentAsset.id);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error deleting this software (${error.response.data.message})`);
        });
    },

    fetchDiscoveredAssets({ commit, getters }) {
      commit('setLoading', true);
      const params = {
        page: getters.pageIndex + 1,
        page_size: getters.pageSize,
        status: getters.discoveredAssetStatus,
        discovery: getters.assetDiscovery,
      };
      if (getters.search) {
        params.search = getters.search;
      }
      if (getters.discoveredAssetSource) {
        params.source = getters.discoveredAssetSource.id;
      }
      if (getters.discoveredAssetType) {        
        params.asset_type = getters.discoveredAssetType?.name;
      }
      if (getters.discoveredAssetLocation) {
        params.location_id = getters.discoveredAssetLocation.id;
      }
      params.active_order = {
        activeSort: getters.sortByOptions.activeSort || 'asset_type',
        activeSortDirection: getters.sortByOptions.activeSortDirection || 'desc',
      };
      
      http.get('/discovered_assets.json', { params }).then((res) => {
        commit('setDiscoveredAssets', res.data.discoveredAssets);
        commit('setTotalFilteredAssetsCount', res.data.filteredAssetsCount);
        commit('setLoading', false);
        commit('setPageCount', res.data.pageCount);
      });
    },

    fetchDiscoveredAssetsLogs({ commit, getters }) {
      commit('setLoadingLogs', true);
      const params = {
        page: getters.pageIndexLogs + 1,
        page_size: getters.pageSizeLogs,
        source: 'kaseya',
        discovery: getters.assetDiscovery,
      };
      if (getters.searchLogs) {
        params.search = getters.searchLogs;
      }
      http.get('/discovered_assets.json', { params }).then((res) => {
        commit('setDiscoveredAssetsLogs', res.data.discoveredAssets);
        commit('setLoadingLogs', false);
        commit('setPageCountLogs', res.data.pageCount);
      });
    },

    usedByContributorId({ commit }, id) {
      commit('setUsedBy', id);
    },
    managedByContributorId({ commit }, id) {
      commit('setManagedBy', id);
    },
    fetchAuditHistory({ commit, state }, params) {
      commit('setHistoryLoading', true);
      const requestParams = {
        ...params,
        per_page: state.historyPerPage,
      };
      return http
        .get('/managed_asset_history', { params: requestParams } )
        .then(res => {
          commit('setAuditHistoryItems', res.data.auditHistoryItems);
          commit('setHistoryPageCount', res.data.pageCount);
          commit('setHistoryLoading', false);
        })
        .catch(error => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error fetching asset history (${error.message}).`);
          commit('setHistoryLoading', false);
        });
    },
    
    fetchAsset({ commit }, id) {
      commit('setLoading',true);
      http
        .get(`/managed_assets/${id}.json`)
        .then((res) => {
          commit('setCurrentAsset', res.data);
          commit('setCompanyChannelKey', res.data.companyChannelKey);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error loading this asset (${error.response.data.message}).`);
        })
        .finally(() => commit('setLoading', false));
    },

    fetchAssetActivities({ commit, getters }, id) {
      const params = {
        page: getters.assetActivitiesPage + 1,
        per_page: getters.assetActivitiesPerPage,
      };

      return http
        .get(`/managed_assets/${id}/managed_asset_activities.json`, { params })
        .then((res) => {
          commit("setAssetActivities", res.data.managedAssetActivities);
          commit("setAssetActivitiesPageCount", res.data.pageCount);
        })
        .catch((error) => {
          const msg = error?.response?.data?.message || '';
          commit(
            "GlobalStore/setErrorMessage",
            `Sorry, there was an error loading assets activity info. ${msg}.`
          );
        });
    },

    fetchAssetLinkables({ commit }, id) {
      commit('setLoading',true);
      http
        .get(`/managed_assets/${id}/linkables.json`)
        .then((res) => {
          commit('setCurrentAssetLinkables', res.data);
          commit('setCompanyChannelKey', res.data.companyChannelKey);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error loading this asset (${error.response.data.message}).`);
        })
        .finally(() => commit('setLoading', false));
    },

    updateAssetListColumns({ commit }, value) {
      commit('setAssetPreferences', value);
      const payload = { selected_columns: value };
      http
        .put('/asset_preferences.json', payload)
        .then((res) => {
          commit('setSuccessMessage', res.data.message);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage',`Sorry, there was an error saving data (${error.response.data.message}).`);
        });
    },

    updateDiscoveredAssetListColumns({ commit }, value) {
      commit('setDiscoveredAssetPreferences', value);
      const payload = { selected_columns: value };
      http
        .put('/discovered_asset_preferences.json', payload)
        .then((res) => {
          commit('setSuccessMessage', res.data.message);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage',`Sorry, there was an error saving data (${error.response.data.message}).`);
        });
    },

    fetchAssets({ commit, getters }, specificParams) {
      let isBulkUpdating = false;
      let mspFlag = false;

      if (specificParams) {
        if (typeof specificParams === 'object') {
          isBulkUpdating = specificParams.isBulkUpdating;
          mspFlag = specificParams.mspFlag;
        };

        if (typeof specificParams === 'boolean') {
          isBulkUpdating = specificParams;
        }
      }

      if (!getters.preventTableLoading) {
        commit('setAssetsLoading', true);
      } 
            
      let params = {};
      let analyticsFilters = {};
      if (specificParams?.isAssetsAnalytics) {
        analyticsFilters = { ...getters.analyticsFilterParams };
        params.selected_columns = getters.analyticsSelectedColumns.map(column => column.name);
      } else {
        params = { ...getters.baseFilterParams };

        if (router.currentRoute.query.type) {
          if (router.currentRoute.query.multi) {
            const queryTypeId = Number(router.currentRoute.query.type);
            this.state.activeMultiFilters.activeTypes = this.state.activeMultiFilters.activeTypes.filter(
              (type) => type.id === queryTypeId
            );
  
            params.company_asset_type_ids = [router.currentRoute.query.type];
          } else {
            params.company_asset_type_id = [router.currentRoute.query.type];
          }
        }

        params.selected_columns = getters.selectedColumns.map(column => column.name);
        params.is_people = getters.isPeople;
      }
      commit('setAnalyticsActiveFiltersCount', Object.keys(analyticsFilters).length);
      params.search_term = getters.searchTerm;
      params.search = getters.search;
      params.page = getters.pageIndex;
      params.page_size = getters.pageSize;
      params.view_type = getters.viewType;
      params.is_bulk_updating = isBulkUpdating;
      params.tab = getters.activeTab;
      if (params.selected_columns.includes(getters.sortByOptions.activeSort) || getters.isPeople) {
        params.sort_by = getters.sortByOptions.activeSort;
        params.sort_order = getters.sortByOptions.activeSortDirection;
      } else {
        params.sort_by = 'name'; 
        params.sort_order = 'asc';
      }
      params.msp_flag = mspFlag;
      params.analytics_filters = analyticsFilters;

      return http.get('/managed_assets.json', { params }).then((res) => {
        commit('setAssets', res.data.assetsInfo.assets);
        commit('setLoading', false);
        commit('setTotalRecord', res.data.assetsInfo.totalRecord);
        commit('setTotalUnfilteredRecords', res.data.assetsInfo.totalUnfilteredRecords);
        commit('setPageCount', res.data.assetsInfo.pageCount);
        commit('setChannelKey', res.data.assetsInfo.channelKey);
        commit('setCompanyChannelKey', res.data.assetsInfo.companyChannelKey);
        commit('setSearchUser', null);
        commit('setSearchTerm', this.search);
        commit('setSearchView', 'default-search');
        if (!getters.preventTableLoading) {
          commit('setAssetsLoading', false);
        } else {
          commit('setPreventTableLoading', false);
        }
      });
    },

    fetchAssetTypes({ commit, getters }, options={}) {
      commit('setOptionsLoading', true);

      const params = { search_asset_type: getters.analyticsOptionsSearch };
      params.limit = options.limit;
      params.offset = options.offset;

      http
        .get('/company_asset_types.json', { params })
        .then((res) => {
          commit('setAssetTypes', res.data.assetTypes);
          commit('setTotalAssetTypesCount', res.data.assetTypesCount);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error loading asset types (${error.message}).`);
        })
        .finally(() => commit('setOptionsLoading', false));
    },

    fetchCompanyAssetStatuses({ commit, getters }, options={}) {
      commit('setOptionsLoading', true);

      const params = { search_asset_status: getters.analyticsOptionsSearch };
      params.limit = options.limit;
      params.offset = options.offset;

      http
        .get('/company_asset_statuses', { params })
        .then((res) => {
          commit('setStatusTypes', res.data.assetStatuses);
          commit('setAssetAvailabilityStatus', res.data.assetStatuses);
          commit('setTotalStatusesCount', res.data.assetStatusesCount);
        })
        .catch(() => {
          this.emitError('Sorry, there was an error fetching the statuses. Please refresh the page and try again.');
        })
        .finally(() => commit('setOptionsLoading', false));
    },

    fetchCompanyAssetTags({ commit }, options={}) {
      commit('setOptionsLoading', true);

      const params = {
        limit: options.limit,
        offset: options.offset,
      };

      http
        .get('/company_asset_tags', { params })
        .then((res) => {
          commit('setAssetTags', res.data.assetTags);
          commit('setTotalTagsCount', res.data.assetTagsCount);
        })
        .catch(() => {
          this.emitError('Sorry, there was an error fetching the statuses. Please refresh the page and try again.');
        })
        .finally(() => commit('setOptionsLoading', false));
    },

    async fetchAssetPreferences({ commit }) {
      await http
        .get('/asset_preferences.json')
        .then((res) => {
          const { tableData, cardData } = res.data;
          commit('setCardDataItems', cardData);
          commit('setDefaultSelectedColumns', tableData.defaultSelectedCols);
          commit('setDefaultUnSelectedColumns', tableData.defaultUnselectedCols);
          commit('setAssetPreferences', tableData.assetPreference.preference);
          if (tableData.assetPreference.qrCodeSettings) {
            commit('setQRCodeSettings', tableData.assetPreference.qrCodeSettings);
          }
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error fetching asset preferences (${error.message}).`);
        });
    },

    async fetchDiscoveredAssetPreferences({ commit }) {
      await http
        .get('/discovered_asset_preferences.json')
        .then((res) => {
          const { tableData } = res.data;
          commit('setDefaultSelectedColumns', tableData.defaultSelectedCols);
          commit('setDefaultUnSelectedColumns', tableData.defaultUnselectedCols);
          commit('setDiscoveredAssetPreferences', tableData.assetPreference.preference);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error fetching asset preferences (${error.message}).`);
        });
    },
    updateAssetPreferences({ commit }, payload) {
      http
        .put('/asset_preferences.json', payload)
        .then((res) => {
          commit('setSuccessMessage', res.data.message);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage',`Sorry, there was an error saving data (${error.response.data.message}).`);
        });
    },
    updateDiscoveredAssetPreferences({ commit }, payload) {
      http
        .put('/discovered_asset_preferences.json', payload)
        .then((res) => {
          commit('setSuccessMessage', res.data.message);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage',`Sorry, there was an error saving data (${error.response.data.message}).`);
        });
    },
    fetchCompanyLocations({ commit }) {
      commit('setLoading', true);
      http
        .get('/locations.json')
        .then((res) => {
          commit('setCompanyLocations', res.data.locations);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage',`Sorry, there was an error gathering company location data. Please refresh the page and try again: ${error.message}`);
        })
        .finally(() => commit('setLoading', false));
    },
    fetchCompanyProducts({ commit }) {
      http
        .get('/products.json', { params: { company_module: 'asset' } })
        .then((res) => {
          commit('setCompanyProducts', res.data);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage',`Sorry, there was an error loading company products ${error.message}`);
        });
    },
    fetchCurrentAssetProduct({ commit }, productId) {
      return http
        .get(`/products/${productId}.json`)
        .then(res => {
          commit('setCurrentAssetProduct', res.data.product);
        })
        .catch(() => {
          commit('setError', 'Sorry, there was an error fetching products data. Please refresh the page and try again.');
        });
    },
    fetchDiscoveredAssetsSummary({ commit }) {
      http
        .get(`/discovered_assets_summary.json`)
        .then((res) => {
          commit("setTotalUnrecognized", res.data.totalUnrecognized);
          commit("setTotalIncomplete", res.data.totalIncomplete);
          commit("setTotalReadyForImport", res.data.totalReadyForImport);
          commit("setTotalImported", res.data.totalImported);
          commit("setTotalIgnored", res.data.totalIgnored);
          commit("setLastCreatedIncomplete", res.data.lastCreatedIncomplete);
          commit("setLastCreatedReadyForImport", res.data.lastCreatedReadyForImport);
        }).catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error loading discovered asset information (${error.message}).`);
        });
    },
    fetchDepreciations({ commit }) {
      commit('setLoading', true);
      http
        .get(`/depreciations.json`)
        .then(res => {
          commit('setDepreciations', res.data);
          commit('setLoading', false);
        })
        .catch(error => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error getting depreciations (${error.message}).`);
          commit('setLoading', false);
        });
    },
    fetchCompanyChannelKey({ commit, getters }) {
      const params = { ...getters.baseFilterParams };
      params.search_term = getters.searchTerm;
      params.search = getters.search;
      params.page = getters.pageIndex + 1;
      params.page_size = getters.pageSize;
      http.get('/managed_assets.json', { params }).then((res) => {
        commit('setCompanyChannelKey', res.data.assetsInfo.companyChannelKey);
      });
    },
    fetchAppsLocationSummary({ commit }) {
      http
        .get("/apps_location_summary")
        .then((res) => {
          commit('setAppsLocationSummary', res.data);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', 'Sorry, there was an error while fetching the application location summary.');
        });
    },
    fetchLatestAgentVersion({ commit }) {
      http
        .get("/window_scripts/agent.json")
        .then((res) => {
          commit('setLastAgentVersionCreatedAt', res.data.createdAt);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', 'Sorry, there was an error while fetching the latest agent version.');
        });
    },
    fetchLatestProbeVersion({ commit }) {
      http
        .get("/window_scripts/network_discovery.json")
        .then((res) => {
          commit('setLastProbeVersionCreatedAt', res.data.createdAt);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', 'Sorry, there was an error while fetching the latest probe version.');
        });
    },
    checkIfUnseenDiscoveryLocations({ getters, commit }) {
      const isAgentUpdated = new Date(getters.lastSeenDiscoveryToolTab) < new Date(getters.lastAgentVersionCreatedAt);
      const isProbeUpdates = new Date(getters.lastSeenDiscoveryToolTab) < new Date(getters.lastProbeVersionCreatedAt);
      commit('setHasUnseenDiscoveryTool', isAgentUpdated || isProbeUpdates);
    },
    fetchDepartments({ commit }) {
      http
        .get(`/managed_asset_departments.json`)
        .then((res) => {
          commit('setAssetDepartments', res.data);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', 'Sorry, there was an error loading departments.');
        });
    },
    fetchAssetManufacturers({ commit }, options={}) {
      commit('setOptionsLoading', true);

      const params = { limit: options.limit };
      params.offset = options.offset;

      http
        .get(`/managed_asset_manufacturers.json`, { params })
        .then((res) => {
          commit('setTotalManufacturersCount', res.data.count);
          commit('setAssetManufacturers', res.data.manufacturers);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', 'Sorry, there was an error loading manufacturers.');
        })
        .finally(() => commit('setOptionsLoading', false));
    },
    fetchAssetOperatingSystems({ commit }, options={}) {
      commit('setOptionsLoading', true);

      const params = { limit: options.limit };
      params.offset = options.offset;

      http
        .get(`/managed_asset_operating_systems.json`, { params })
        .then((res) => {
          commit('setAssetOperatingSystems', res.data.operatingSystems);
          commit('setTotalOperatingSystemsCount', res.data.count);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', 'Sorry, there was an error loading operating systems.');
        })
        .finally(() => commit('setOptionsLoading', false));
    },
    fetchAssetSoftwares({ commit }, options={}) {
      commit('setOptionsLoading', true);

      const params = { limit: options.limit };
      params.offset = options.offset;

      http
        .get(`/managed_asset_softwares.json`, { params })
        .then((res) => {
          commit('setTotalSoftwareCount', res.data.count);
          commit('setSoftwares', res.data.softwares);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', 'Sorry, there was an error loading softwares.');
        })
        .finally(() => commit('setOptionsLoading', false));
    },
    searchFilterOptions({ commit, getters }, options) {
      commit('setOptionsLoading', true);

      const params = {
        search_query: getters.analyticsOptionsSearch,
        limit: options.limit,
        offset: options.offset,
        filter_type: options.filter_type,
      };

      const endpointMapping = {
        'manufacturer': ['setTotalManufacturersCount', 'setAssetManufacturers'],
        'operating_system': ['setTotalOperatingSystemsCount', 'setAssetOperatingSystems'],
        'software': ['setTotalSoftwareCount', 'setSoftwares'],
        'location': ['GlobalStore/setTotalLocationsCount', 'GlobalStore/setLocations'],
      };

      const [totalCountCommit, optionsCommit] = endpointMapping[options.filter_type];

      http
      .get('/managed_assets/assets/insights/search_field_filter_options', { params })
        .then((res) => {
          commit(totalCountCommit, res.data.count);
          commit(optionsCommit, res.data.options);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', 'Sorry, there was an error searching options.');
        })
        .finally(() => commit('setOptionsLoading', false));
    },
    async fetchAnalyticsPreferences({ commit }) {
      await http
        .get('/asset_analytics_preferences.json')
        .then((res) => {
          commit('setAnalyticsSelectedColumn', res.data.defaultSelectedCols);
          commit('setAnalyticsUnselectedColumn', res.data.defaultUnselectedCols);
          commit('setAnalyticsPreferences', res.data.analyticsPreference);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error fetching analytics preferences (${error.message}).`);
        });
    },
    shouldDisplayRiskCenter({ commit }) {
      http
        .get('/managed_assets/should_display_asset_risk_center.json')
        .then((res) => {
          commit('setDisplayRiskCenterSection', res.data.displayRiskCenter);
        });
    },
    fectchAgentStatus({ commit }) {
      http
        .get('/managed_assets/has_agent_installed.json')
        .then((res) => {
          commit('setHasAgentInstalled', res.data.agentInstalled);
        });
    },
  },

  plugins: [
    createPersistedState({
      localStorage: {
        getState: key => Cookies.getJSON(key),
        setState: (key, state) => Cookies.set(key, state, { expires: 3 }),
        removeItem: key => Cookies.remove(key),
      },
      paths: [
        'lastSeenDiscoveryToolTab',
        'assetInsightsSelectedChartTypes',
      ],
    }),
  ],
});
