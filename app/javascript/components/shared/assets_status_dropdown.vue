<template>
  <div class="dropdown d-inline-block">
    <div
      v-click-outside="onCloseDropdown"
      @click.prevent="toggleStatusDropdown"
    >
      <asset-status-pill
        clickable
        size="large"
        pill-style="btn-text py-2 px-3"
        :name="currentStatus.name"
        :icon="currentStatus.icon"
        :show-arrow="true"
        data-tc-dropdown="asset statuses"
      />
    </div>
    <div
      class="dropdown-menu not-as-small"
      :class="{ hidden: !showStatusDropdown }"
    >
      <a
        v-for="status in statusOptions"
        :key="status.id"
        class="dropdown-item clickable pl-3"
        href="#"
        @click.prevent="changeStatus(status)"
      >
        <asset-status-icon
          :icon="status.icon"
          :name="status.name"
        />
        <span
          :class="getTextColor(status.icon)"
          :data-tc-status-option="toTitle(status.name)"
        >{{ toTitle(status.name) }}
        </span>
      </a>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import string from 'mixins/string';
  import permissionsHelper from 'mixins/permissions_helper';
  import assetStatus from 'mixins/assets/asset_status';
  import vClickOutside from 'v-click-outside';
  import AssetStatusPill from 'components/shared/asset_status_pill.vue';
  import AssetStatusIcon from 'components/shared/asset_status_icon.vue';

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      AssetStatusPill,
      AssetStatusIcon,
    },
    mixins: [string, permissionsHelper, assetStatus],
    props: {
      currentAsset: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        showStatusDropdown: false,
        statusOptions: [],
      };
    },
    computed: {
      currentStatus() {
        return this.currentAsset.status;
      },
      mergedAsset() {
        return this.currentAsset.merged;
      },
    },
    methods: {
      onWorkspaceChange() {
        this.getStatusOptions();
      },
      toggleStatusDropdown() {
        if (this.isWrite && !this.mergedAsset) {
          this.showStatusDropdown = !this.showStatusDropdown;
        } else {
          this.emitError(`Sorry, you do not have permissions to set status.`);
        }
      },
      onCloseDropdown() {
        this.showStatusDropdown = false;
      },
      getStatusOptions() {
        http
          .get('/managed_asset_statuses.json')
          .then(res => {
            this.statusOptions = res.data;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error getting asset status options. Please refresh the page and try again`);
          });
      },
      changeStatus(status) {
        this.$emit('change-status', status);
        this.showStatusDropdown = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dropdown-menu {
    display: block;
    left: 0;
    right: auto;
  }

  .dropdown-item {
    min-width: 25rem;
    white-space: wrap;
  }

  a > .nulodgicon-android-close::before {
    color: $themed-fair !important;
  }
</style>
