<template>
  <div v-if="managedAsset">
    <div
      v-if="isWrite && !managedAsset.merged"
      class="text-center text-md-left"
    >
      <div
        v-if="!isEdit"
        class="btn d-flex align-items-center btn-text text-dark"
        @click.stop.prevent="openAlertDatesModal"
      >
        <div
          class="btn-icon-circle btn-icon-circle-xs icon--line-height"
          :class="alertClass"
        >
          <i class="genuicon-alerts align-middle text-secondary h6" />
        </div>
        <div
          v-if="nextAlertPresent"
          class="ml-2"
        >
          <span class="true-small text-secondary mr-2">Next Alert:</span>
          <span class="base-font-size">{{ nextAlertDate }}</span>
        </div>
        <span
          v-else
          class="true-small text-secondary ml-2"
          data-tc-btn="+ Add alerts"
        >
          + Add alerts
        </span>
      </div>
    </div>
    <Teleport to="body">
      <sweet-modal
        ref="alertDateModal"
        v-sweet-esc
        modal-theme="dark-header theme-right"
        title="Asset Alerts"
        class="alert-modal"
        data-tc-title="asset alerts"
        @close="handleClose"
      >
        <template slot="default">
          <alert-dates
            ref="alertDates"
            :key="alertDatesKey"
            :managed-asset.sync="managedAsset"
            @close="handleClose"
          />
        </template>
        <div
          slot="button"
          data-tc-section="mention days"
        >
          <div class="float-left ml-3">
            <span class="d-inline-flex">
              <span class="btn-icon-circle btn-icon-circle-xxs bg-danger-light mr-1 mt-0.5" />
              <span class="not-as-small text-secondary">&#8804; 5 days</span>
            </span>
            <span class="d-inline-flex ml-2">
              <span class="btn-icon-circle btn-icon-circle-xxs bg-warning-light mr-1 mt-0.5" />
              <span class="not-as-small text-secondary">&#8804; 10 days</span>
            </span>
            <span class="d-inline-flex ml-2">
              <span class="btn-icon-circle btn-icon-circle-xxs bg-safe mr-1 mt-0.5" />
              <span class="not-as-small text-secondary"> > 10 days</span>
            </span>
          </div>
          <button
            class="btn btn-link text-secondary mr-2"
            data-tc-btn="cancel"
            @click.stop="handleClose"
          >
            Cancel
          </button>
          <submit-button
            :is-saving="disabled"
            btn-content="Save"
            saving-content="Saving"
            @submit="saveAlerts"
          />
        </div>
      </sweet-modal>
    </Teleport>
  </div>
</template>
<script>
  import { SweetModal } from 'sweet-modal-vue';
  import _cloneDeep from 'lodash/cloneDeep';
  import dates from 'mixins/dates';
  import permissionsHelper from 'mixins/permissions_helper';
  import assetAlertUpdateHelper from 'mixins/assets/asset_alert_update_helper';
  import assetAlerts from 'mixins/assets/asset_alerts';
  import SubmitButton from '../shared/submit_button.vue';
  import AlertDates from './show_page_elements/alert_dates.vue';

  export default {
    components: {
      AlertDates,
      SweetModal,
      SubmitButton,
    },
    mixins: [permissionsHelper, assetAlertUpdateHelper, dates, assetAlerts],
    props: {
      managedAsset: {
        type: Object,
        default: () => ({}),
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      isEdit: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        asset: _cloneDeep(this.managedAsset),
        alertDatesKey: false,
        isModalOpen: false,
      };
    },
    computed: {
      nextAlertPresent() {
        return this.managedAsset?.alertDates?.some(alert => moment.utc(alert.date) > moment.utc());
      },
      nextAlertDate() {
        if (this.nextAlertPresent) {
          const futureAlerts = this.managedAsset.alertDates.filter(alert => moment.utc(alert.date) > moment.utc());
          const closestAlert = futureAlerts.reduce((prev, current) => moment.utc(prev.date).isBefore(moment.utc(current.date)) ? prev : current);
          return moment.parseZone(closestAlert.date).format('MMMM DD, YYYY');
        }
        return "";
      },
      alertClass() {
        if (this.nextAlertPresent) {
          const nextAlertDate = moment(this.nextAlertDate, 'MMMM DD, YYYY');
          const daysUntilNextAlert = this.daysUntilToday(nextAlertDate);
          return this.alertClassColor(daysUntilNextAlert);
        }
        return 'bg-safe';
      },
    },
    watch: {
      managedAsset() {
        this.asset = _cloneDeep(this.managedAsset);
        if (this.isEdit) {
          this.openAlertDatesModal();
        }
      },
      isEdit(newVal) {
        if (newVal) {
          this.openAlertDatesModal();
        } else {
          this.handleCancel();
        }
      },
    },
    mounted() {
      if (this.isEdit) {
        this.openAlertDatesModal();
      } else {
        this.resetData();
      }
    },
    methods: {
      openAlertDatesModal() {
        if (!this.isModalOpen) {
          this.isModalOpen = true;
          this.alertDatesKey = !this.alertDatesKey;
          this.$nextTick(() => {
            this.$refs.alertDateModal.open();
          });
          this.updateSeenAlert();
          this.asset = _cloneDeep(this.managedAsset);
        }
      },
      handleClose() {
        if (this.isModalOpen) {
          this.isModalOpen = false;
          this.$refs.alertDateModal.close();
          this.$emit('cancel-edit');
        }
      },
      saveAlerts() {
        this.$refs.alertDates.saveAlerts();
      },
      resetData() {
        this.alertDatesKey = !this.alertDatesKey;
        this.handleClose();
      },
    },
  };
</script>
<style scoped lang="scss">
  .alert-modal :deep(.sweet-modal) {
    max-width: 50vw;
    width: 50vw;
  }

  .icon--line-height {
    line-height: 1.8125rem;
  }
</style>
