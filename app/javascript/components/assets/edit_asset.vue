<template>
  <div>
    <div class="pt-5 d-flex justify-content-between flexbox-column">
      <div class="col-8">
        <onboarding-title
          :image-src="onboardingTitle.imageSrc"
          :header="onboardingTitle.header"
          :sub-header="onboardingTitle.subHeader"
        />
      </div>
      <div class="col-4 text-right">
        <router-link
          v-if="!currentAsset"
          to="/"
          class="text-secondary mr-4"
        >
          <i class="nulodgicon-arrow-left-c white mr-2" />
          <span>Back to <strong>all assets</strong></span>
        </router-link>

        <router-link
          v-else
          :to="`/${currentAsset.id}`"
          class="text-secondary mr-4"
        >
          <i class="nulodgicon-arrow-left-c white mr-2" />
          <span>Back to <strong>{{ currentAsset.name }}</strong></span>
        </router-link>
      </div>
    </div>

    <asset-form
      v-if="currentAsset"
      :temp-asset="currentAsset"
      :asset-type-name="assetTypeName"
      :submitted.sync="submitted"
      :is-new="false"
      @submit-asset="submitAsset"
    />
  </div>
</template>

<script>
import http from 'common/http';
import { mapMutations, mapGetters } from 'vuex';
import permissionsHelper from 'mixins/permissions_helper';
import OnboardingTitle from  'components/shared/module_onboarding/onboarding_title.vue';
import assetAlertUpdateHelper from 'mixins/assets/asset_alert_update_helper';
import AssetForm from '../asset_form/asset_form.vue';

export default {
  components: {
    AssetForm,
    OnboardingTitle,
  },
  mixins: [permissionsHelper, assetAlertUpdateHelper],
  data() {
    return {
      assetTypeName: '',
      submitted: false,
      onboardingTitle: {
        header: "Edit Asset",
        subHeader: "Tell us about this asset. Many of the fields are optional, although we highly recommend filling out as many as you can for a more accurate snapshot!",
        imageSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/asset_manual_entry_icon.svg",
      },
      alerts: null,
    };
  },
  computed: {
    ...mapGetters([
      'currentAsset',
      'loading',
    ]),
  },
  methods: {
    ...mapMutations([
      'setCurrentAsset',
      'updateAsset',
      'clearLoading',
    ]),

    onWorkspaceChange() {
      if (this.$route.params.id) {
        this.$store.dispatch('fetchAsset', this.$route.params.id);
      } else {
        this.$store.commit('clearLoading');
      }
    },
    submitAsset(formAttachments, alertDates) {
      if (alertDates.length) {
        this.alerts = alertDates;
      }
      this.submitted = true;
      formAttachments.append('managed_asset[source]', "manually_added");
      http
        .put(`/managed_assets/${formAttachments.get("managed_asset[id]")}.json`, formAttachments)
        .then((res) => {
          this.emitSuccess("Asset was successfully updated");
          const updatedAsset = res.data.asset;
          if (this.alerts) {
            updatedAsset.alertDates = this.alerts;
            this.saveAlertDates(updatedAsset);
          }
          this.setCurrentAsset(updatedAsset);
          this.updateAsset(updatedAsset);
          this.$router.push({ path: `/${updatedAsset.id}` });
        })
        .catch((error) => {
          this.emitError(`Sorry, there was an error saving this asset. ${error.response.data.message}`);
        })
        .finally(() => {
          this.submitted = false;
        });
    },
  },
};
</script>
