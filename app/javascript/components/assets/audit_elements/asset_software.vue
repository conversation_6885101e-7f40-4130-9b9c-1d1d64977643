<template>
  <div class="row">
    <template v-for="(change, item) in historyItem.auditedChanges">
      <div
        v-if="item != 'source'"
        :key="item"
        class="col-12 p-3 not-as-small"
      >
        <span class="action-icon mr-2">
          <i class="nulodgicon-social-windows" />
        </span>

        <span v-tooltip="`Source: ${toTitle(source)}`">
          <img
            v-if="source"
            class="history-source-icon mb-2 mr-2"
            :src="getSourceIcon(source)"
          >
        </span>
        <span v-if="currentAsset">
          <a
            :href="`/company/users/${historyItem.userId}`"
            target="_blank"
            class="text-secondary font-weight-bold"
          >
            {{ userName(historyItem.userId) }}
          </a>
          <span v-if="specifyAction == 'update'">
            changed the {{ setItem(item) }}
            <span v-if="setItem(item) != 'name'">
              of <strong>{{ getSoftwareName(historyItem.auditableId) }}</strong>
            </span>
            from <strong>{{ fromValue(item) }}</strong> to <strong>{{ toValue(item) }}</strong>.
          </span>
          <span v-else-if="specifyAction == 'added'">
            added the software 
            <strong
              v-tooltip="{
                content: toValue(item),
                boundariesElement: 'body',
                show: (toValue(item) && toValue(item).length > 92 && hoveredIndex == toValue(item)),
                trigger: 'manual',
              }"
              @mouseover="hoveredIndex = toValue(item)"
              @mouseleave="hoveredIndex = null"
            >
              <span>{{ truncate(toValue(item), 92) }}</span>
            </strong>.
          </span>
          <span v-else>
            {{ specifyAction }}
            <strong>{{ getSoftwareName(historyItem.auditableId) }}</strong>
          </span>
        </span>
        <span class="float-right small text-muted">{{ actionDate }}</span>
      </div>
    </template>
  </div>
</template>

<script>
  import audits from 'mixins/audits.js';
  import strings from 'mixins/string';
  import http from 'common/http';
  import { mapGetters } from 'vuex';

  export default {
    mixins: [audits, strings],
    props: ['historyItem'],
     data() {
      return {
        hoveredIndex: null,
      }
    },
    computed: {
      ...mapGetters([
        'currentAsset',
        'companyUsersAndGroups'
      ]),
      specifyAction() {
        if (this.historyItem.action == "create") {
          return "added";
        } else if (this.historyItem.action == "destroy") {
          return "removed";
        } else if (this.historyItem.action == "update") {
          if (this.historyItem.auditedChanges.archivedAt && this.historyItem.auditedChanges.archivedAt[0]) {
            return "unarchived";
          } else if (this.historyItem.auditedChanges.archivedAt && this.historyItem.auditedChanges.archivedAt[1]) {
            return "archived";
          } else {
            return "update";
          }
        }
      },
    },
    methods: {
      getSoftwareName(id) {
        const software = this.currentAsset.assetSoftwares.filter(software => software.id == id)[0];
        if (software) {
          let name = software.name;
          return name ? name : "Unnamed software"
        } else if (this.historyItem.auditedChanges.name) {
          return this.historyItem.auditedChanges.name;
        }
        return "(Software Deleted)";
      }
    }
  }
</script>

<style lang="scss" scoped>
  .action-icon {
    background-color: $red;
  }
</style>