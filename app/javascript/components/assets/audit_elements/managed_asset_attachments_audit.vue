<template>
  <div class="row">
    <template v-for="(change, item) in filteredAuditedChanges">
      <div
        v-if="item != 'source'"
        :key="item"
        class="col-12 p-3 not-as-small"
      >
        <span
          :class="{'add-attachment-color': historyItem.action == 'create', 'remove-attachment-color': historyItem.action == 'destroy'}"
          class="action-icon mr-2"
        >
          <i
            v-if="historyItem.action == 'create'"
            class="nulodgicon-link"
          />
          <i
            v-else-if="historyItem.action == 'destroy'"
            class="nulodgicon-trash-b"
          />
          <i
            v-else
            class="nulodgicon-edit"
          />
        </span>

        <span v-tooltip="`Source: ${toTitle(source)}`">
          <img
            v-if="source"
            class="history-source-icon mb-2 mr-2"
            :src="getSourceIcon(source)"
          >
        </span>
        <span>
          <a
            :href="`/company/users/${historyItem.userId}`"
            target="_blank"
            class="text-secondary font-weight-bold"
          >
            {{ userName(historyItem.userId) }}
          </a>
        </span>
        <span>
          {{ specifyAction }}
          <strong class="px-1 ml-1">{{ change }}</strong>
        </span>
        <span class="float-right small text-muted">{{ actionDate }}</span>
      </div>
    </template>
  </div>
</template>

<script>
  import _ from 'lodash';
  import audits from 'mixins/audits.js';
  import { mapGetters } from 'vuex';


  export default {
    mixins: [audits],
    props: ['historyItem'],
    computed: {
      ...mapGetters(['companyUsersAndGroups']),
      specifyAction() {
        if (this.historyItem.action == "create") {
          return "added an attachment";
        } else if (this.historyItem.action == "destroy") {
          return "removed an attachment";
        } else if (this.historyItem.action == "update") {
          return "changed the attachment";
        }
      },
      filteredAuditedChanges() {
        return _.pick(this.historyItem.auditedChanges, "attachedFileFileName");
      }
    }
  }
</script>

<style lang="scss" scoped>
  .action-icon {
    background-color: $blue;
  }
  .remove-attachment-color {
    background-color: $orange;
  }
  .add-attachment-color {
    background-color: #26C281;
  }
</style>
