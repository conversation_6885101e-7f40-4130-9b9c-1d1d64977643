<template>
  <div class="row">
    <template v-for="(change, item) in historyItem.auditedChanges">
      <div
        v-if="item != 'source'"
        :key="item"
        class="col-12 p-3 not-as-small"
      >
        <span class="action-icon mr-2">
          <i class="nulodgicon-android-call" />
        </span>

        <span v-tooltip="`Source: ${toTitle(source)}`">
          <img
            v-if="source"
            class="history-source-icon mb-2 mr-2"
            :src="getSourceIcon(source)"
          >
        </span>

        <a
          :href="`/company/users/${historyItem.userId}`"
          target="_blank"
          class="text-secondary font-weight-bold"
        >
          {{ userName(historyItem.userId) }}
        </a> 
        <span v-html="historyItemBody(item)" />
        <span class="float-right small text-muted">{{ actionDate }}</span>
      </div>
    </template>
  </div>
</template>

<script>
  import audits from 'mixins/audits.js';
  import { mapGetters } from 'vuex';

  export default {
    mixins: [audits],
    props: ['historyItem'],
    computed: {
      ...mapGetters(['companyUsersAndGroups']),
    }
  }
</script>

<style lang="scss" scoped>
  .action-icon {
    background-color: $cyan;
  }
</style>