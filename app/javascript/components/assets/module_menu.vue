<template>
  <div class="sub-menu mt-3 clearfix">
    <div class="module-sub-tabs float-left my-sm-2 my-md-0">
      <router-link
        v-if="isWrite || isRead"
        id="dashboardBtn"
        class="sub-menu-item"
        to="/"
        @click.native="onModuleLinkClick"
      >
        Dashboard
      </router-link>
      <router-link
        v-if="isWrite || isRead"
        id="assets_btn"
        class="sub-menu-item"
        :class="{'router-link-exact-active': subIsActive(['/assets'])}"
        to="/assets"
        data-tc-assets="Assets menu"
        @click.native="onModuleLinkClick"
      >
        Managed Assets
      </router-link>
      <span class="sub-menu-separator" />
      <span v-tooltip.bottom="tooltipMessage">
        <span
          class="module-sub-tabs float-left my-sm-2 my-md-0"
          :class="disableLinks"
        >
          <router-link
            v-if="isWrite && displayPeopleTab"
            class="sub-menu-item"
            to="/people_assets/assigned"
            :class="{'router-link-exact-active': subIsActive(peopleRoutes)}"
            @click.native="onModuleLinkClick"
          >
            People
          </router-link>
          <router-link
            v-if="isWrite"
            class="sub-menu-item"
            :to="getDefaultPath()"
            :class="{'router-link-exact-active': subIsActive(subRoutes)}"
            @click.native="handleDiscoveryClick"
          >
            Discovery Tools
            <span
              v-if="hasUnseenDiscoveryTool && outdatedApplicationsCount > 0"
              class="data-badge sub-menu-badge"
            >{{ outdatedApplicationsCount }}</span>
          </router-link>
          <router-link
            v-if="isWrite"
            class="sub-menu-item"
            to="/discovered_assets/ready_for_import"
            :class="discoveredAssetsCss"
            @click.native="onModuleLinkClick"
          >
            Discovered Assets
            <span
              v-if="discoveredAssetsCount > 0"
              class="data-badge sub-menu-badge"
            >{{ discoveredAssetsCount }}</span>
          </router-link>
          <router-link
            id="insights_btn"
            class="sub-menu-item"
            :class="{'router-link-exact-active': subIsActive(reportingRoutes)}"
            to="/insights"
            @click.native="onModuleLinkClick"
          >
            Reporting
          </router-link>
          <router-link
            v-if="displayRiskCenterSection"
            id="risk_center_btn"
            class="sub-menu-item"
            :class="{'router-link-exact-active': subIsActive('/risk_center')}"
            to="/risk_center"
            @click.native="onModuleLinkClick"
          >
            Risk Center
          </router-link>
          <router-link
            class="sub-menu-item"
            to="/automated_tasks"
            :class="{ 'router-link-exact-active': subIsActive('/automated_tasks') }"
            @click.native="onModuleLinkClick"
          >
            Automation
          </router-link>
          <router-link
            v-if="isWrite && isImportPage"
            class="sub-menu-item"
            to="/import_assets"
            @click.native="onModuleLinkClick"
          >
            Import
          </router-link>
          <router-link
            v-if="isWrite"
            class="sub-menu-item"
            to="/settings/asset_types"
            :class="settingsAssetsCss"
            data-tc-asset="settings menu"
            @click.native="onModuleLinkClick"
          >
            <i class="nulodgicon-cog text-fair" />
            Settings
          </router-link>
          <a
            v-tooltip="'Alerts'"
            href="#"
            class="sub-menu-item position-relative"
            @click="openAlertsModal"
          >
            <i class="genuicon-alerts sub-menu-item__icon"/>
            Alerts
            <span
              v-if="actionableAlertsCount > 0"
              class="data-badge sub-menu-badge"
            >
              {{ actionableAlertsCount }}
            </span>
          </a>
          <sweet-dropdown
            class="sub-menu-item"
            open-outer-style="router-btn-active"
            dropdown-position="bottom-left"
            :dropdown-top="-2"
            :dropdown-right="45"
            :options="commonActions"
          >
            <template #targetEle>
              <i class="nulodgicon-dot-3 h5 dropdown-pill bg-light px-2" />
            </template>
          </sweet-dropdown>
        </span>
      </span>
    </div>
    <alerts-modal
      ref="alertsModal"
      :actionable-alerts="actionableAlerts"
      @update-alerts="updateAlerts"
    />
  </div>
</template>

<script>
  import { mapMutations, mapGetters } from 'vuex';
  import SweetDropdown from 'components/shared/sweet_dropdown.vue';
  import AlertsModal from 'components/shared/alerts_modal.vue';
  import navbarHelper from 'mixins/assets/navbar_helper';
  import http from 'common/http';

  export default {
    components: {
      SweetDropdown,
      AlertsModal,
    },
    mixins: [navbarHelper],
    data() {
      return {
        displayPeopleTab: false,
        showActionsDropdown: false,
        actionableAlerts: [],
        actionableAlertsCount: 0,
      };
    },
    computed: {
      ...mapGetters(['displayRiskCenterSection', 'companyIntegrations']),
      discoveredAssetsCss() {
        const flag = (this.$route.path.indexOf("/discovered_assets") === 0);
        return {
          'router-link-exact-active': flag,
        };
      },
      settingsAssetsCss() {
        const flag = (this.$route.path.indexOf("/settings") === 0);
        return {
          'router-link-exact-active': flag,
        };
      },
      subRoutes() {
        return [
          '/discovery_tools',
          '/discovery_tools/',
          '/discovery_tools/agents',
          '/discovery_tools/probes',
          '/discovery_tools/connectors',
          '/discovery_tools/connections',
          '/discovery_tools/connectors/network_probe',
          '/discovery_tools/connectors/agent',
          '/discovery_tools/connectors/self_onboarding',
        ];
      },
      reportingRoutes() {
        return ['insights', '/analytics'];
      },
      peopleRoutes() {
        return [
          '/people_assets/assigned', 
          '/people_assets/unassigned', 
          '/people_assets/archived',
        ];
      },
    },
    mounted() {
      this.shouldDisplayPeopleTab();
      this.$store.dispatch('fetchCompanyIntegrations');
    },
    methods: {
      ...mapMutations('GlobalStore', ['setVerticalNav']),
      handleDiscoveryClick() {
        this.onDiscoveryToolsTabClick();
        this.onModuleLinkClick();
      },
      shouldDisplayPeopleTab() {
        http
          .get('/managed_assets/should_display_people_tab.json')
          .then((res) => {
            this.displayPeopleTab = res.data.displayPeopleTab;
          });
      },
      onModuleLinkClick() {
        const navTheme = document.cookie.split('; ').find(row => row.startsWith('nav-theme='));
        if (navTheme && navTheme.split('=')[1] === 'vertical') {
          this.setVerticalNav(true); 
        }
      },
      goToAssetsPermissions() {
        this.$emit('on-permissions-click');
      },
      subIsActive(input) {
        const paths = Array.isArray(input) ? input : [input];
        return paths.includes(this.$route.path);
      },
      openMoreActionsDropdown() {
        this.showActionsDropdown = !this.showActionsDropdown;
      },
      onBlur() {
        this.showActionsDropdown = false;
      },
      openAlertsModal() {
        this.$nextTick().then(() => {
          if (this.$refs.alertsModal) {
            document.body.appendChild(this.$refs.alertsModal.$el);
            this.$refs.alertsModal.fetchActionableAlerts();
            this.$refs.alertsModal.open();
          }
        });
      },
      updateAlerts(params) {
        this.actionableAlerts = params.alerts;
        this.actionableAlertsCount = params.totalCount;
      },
      getDefaultPath() {
        return this.companyIntegrations && this.companyIntegrations.length > 0 ? '/discovery_tools/connections' : '/discovery_tools/connectors';
      },
    },
  };
</script>

<style lang="scss" scoped>
  .sub-menu-separator {
    width: 0.375rem;
    height: 0.375rem;
  }

  .sub-menu {
    .sub-menu-item {
      &:after {
        bottom: 0px !important;
      }
    }
  }

  .dropdown-pill {
    border-radius: 50rem;
    position: relative;
    top: 0.125rem;
    &:before {
      top: 0.25rem;
    }
  }
</style>
