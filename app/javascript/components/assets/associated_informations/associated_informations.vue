<template>
  <sweet-modal
    ref="modal"
    v-sweet-esc
    title="Before we proceed..."
    @close="$emit('close')"
  >
    <template slot="default">
      <h6
        v-show="!readyToAssign"
        class="my-3 text-left"
      >
        Are you sure you want to {{ moduleName }} this assets?
      </h6>

      <div>
        <p v-if="readyToAssign">
          Select an asset to assign <strong>{{ currentAsset.name }}'s</strong> asscociated information.
        </p>
        <div
          v-else
          class="bg-lighter p-4 rounded"
        >
          <h6 class="text-cyan not-as-big mb-1">
            Note
          </h6>
          <p class="not-as-small text-muted">
            <span class="d-block"><strong>{{ currentAsset.name }}</strong> is associated with the following item(s).</span>
            <span class="d-block">You can associate <strong>{{ currentAsset.name }}</strong> information with any other asset.</span>
          </p>
        </div>

        <associated-help-tickets
          v-if="showComponent('Help Ticket')"
          associated-type="Help Ticket"
          :items-array="helpTickets"
          :ready-to-assign="readyToAssign"
          :opened-accordion.sync="openedAccordion"
          :associated-merged-assets="associatedMergedAssets"
        />
        <associated-related-items
          v-if="showComponent('Related Items')"
          associated-type="Related Items"
          :items-array="relatedItems"
          :ready-to-assign="readyToAssign"
          :opened-accordion.sync="openedAccordion"
          :associated-merged-assets="associatedMergedAssets"
        />
        <associated-assignments
          v-if="showComponent('Assignments')"
          associated-type="Assignments"
          :items-object="assignmentInformation"
          :ready-to-assign="readyToAssign"
          :opened-accordion.sync="openedAccordion"
        />
      </div>
    </template>
    <div class="sweet-buttons sweet-custom-footer pb-0 pt-3 border-top">
      <button
        slot="button"
        class="btn btn-link text-danger mr-2"
        @click.stop="closeModal"
      >
        Cancel
      </button>
      <submit-button
        :is-saving="isSubmitting || skippingAssignments"
        :btn-content="`${toTitle(moduleName)}`"
        :saving-content="`${toTitle(moduleName)}`"
        @submit="skipAssignment"
      />
      <submit-button
        class="ml-2"
        :is-saving="isSubmitting || skippingAssignments"
        :btn-content="assignMerge"
        saving-content="Assigning Info"
        @submit="assignInfo"
      />
    </div>
  </sweet-modal>
</template>

<script>
  import http from "common/http";
  import strings from "mixins/string";
  import { mapGetters } from "vuex";
  import { SweetModal } from "sweet-modal-vue";
  import AssociatedHelpTickets from "./associated_help_tickets.vue";
  import AssociatedRelatedItems from "./associated_related_items.vue";
  import AssociatedAssignments from "./associated_assignments.vue";
  import SubmitButton from '../../shared/submit_button.vue';

  export default {
    components: {
      SweetModal,
      AssociatedHelpTickets,
      AssociatedRelatedItems,
      AssociatedAssignments,
      SubmitButton
    },
    mixins: [strings],
    props: {
      moduleName: {
        type: String,
        default: "",
        required: true
      },
      associatedMergedAssets: {
        type: Boolean,
        default: null,
      },
      allowedItemList: {
        type: Array,
        default: () => []
      },
    },
    data() {
      return {
        isSubmitting: false,
        helpTickets: [],
        relatedItems: [],
        assignmentInformation: {},
        readyToAssign: false,
        skippingAssignments: false,
        openedAccordion: "",
      };
    },
    computed: {
      ...mapGetters(["currentAsset", "associateTicketsHistory"]),
      assignMerge() {
        if (this.readyToAssign) {
          return `Assign & ${this.toTitle(this.moduleName)}`
        }
        return `Assign Info`
      }
    },
    methods: {
      updateAssociateDetails() {
        this.isSubmitting = true;
        let params = {
          helpTickets: this.helpTickets,
          relatedItems: this.relatedItems,
          assignmentInformation: this.assignmentInformation,
        };
        http
          .put(`/managed_assets/associated_information.json`, params )
          .then(() => {
            this.$emit("info-assigned");
          })
          .catch(error => {
            this.isSubmitting = false;
            this.emitError(`Sorry, there was an error in updating these asset's information: ${error.response.data.message}.`);
          });
      },
      openModal() {
        this.$refs.modal.open();
      },
      closeModal() {
        this.readyToAssign = false;
        this.$refs.modal.close();
      },
      assignInfo() {
        if (this.readyToAssign) {
          this.updateAssociateDetails();
        }
        this.readyToAssign = true;
      },
      skipAssignment() {
        this.skippingAssignments = true;
        this.$emit('skip-assignments');
      },
      showComponent(item) {
        return this.allowedItemList.includes(item);
      },
    },
  };
</script>
