<template>
  <div v-if="associatedItems && associatedItems.length">
    <div
      class="mb-1 mt-3 not-as-big clickable"
      @click="openAssociatedItems(`${associatedType}`)"
    >
      Associated Related Item(s)
      <small v-if="associatedItems.length > 0">
        ({{ associatedItems.length }})&nbsp;
        <i
          class="clickable small"
          :class="arrowDirection(openedAccordion)"
        />
      </small>
    </div>

    <div
      v-if="openedAccordion == associatedType"
      :class="scrollableTable"
    >
      <p class="text-muted">
        This asset is associated with the following related items.
      </p>
      <table class="table table-striped">
        <thead>
          <tr>
            <th class="nav-dark-bg-text">
              Type
            </th>
            <th class="nav-dark-bg-text">
              Name
            </th>
            <th
              v-show="readyToAssign"
              class="nav-dark-bg-text"
            >
              Assign to asset
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(universalLink, index) in associatedItems"
            :key="universalLink.id"
          >
            <td class="truncate">
              {{ universalLink.target.linkableType }}
            </td>
            <td
              v-tooltip="{
                content: universalLink.target.name,
                show: (universalLink.target.name && universalLink.target.name.length > 30 && hoveredIndex == universalLink.id),
                trigger: 'manual'
              }"
              class="truncate"
              @mouseover="hoveredIndex = universalLink.id"
              @mouseleave="hoveredIndex = null"
            >
              {{ truncate(universalLink.target.name, 25) }}
            </td>
            <td
              v-show="readyToAssign"
              class="not-as-small text-muted w-50"
            >
              <asset-options
                compact
                :asset-index="index"
                :value="itemValues[index]"
                :associated-merged-assets="associatedMergedAssets"
                @select="handleItemChange"
                @remove="handleItemRemove"
              >
                <template slot="label">
                  <slot name="label" />
                </template>

                <template #image="{ option }">
                  <img
                    :src="`/managed_asset_images/${option.id}`"
                    :style="imageStyle"
                  >
                </template>
              </asset-options>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  import strings from "mixins/string";
  import associatedInformation from "mixins/assets/associated_information";
  import AssetOptions from "../asset_options.vue";

  export default {
    components: {
      AssetOptions
    },
    mixins: [strings, associatedInformation],
    computed: {
      ...mapGetters(["currentAsset"]),
      associatedItems() {
        return this.currentAsset ? this.currentAsset.universalLinks : [];
      },
      imageStyle() {
        return 'height: 20px; width: 30px; object-fit: contain;';
      }
    },
  };
</script>
