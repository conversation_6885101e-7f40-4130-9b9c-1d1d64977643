<template>
  <div>
    <div class="row mb-3 mx-0">
      <slot name="title"/>
      <div class="col-md d-flex justify-content-end align-items-start">

        <div class="d-flex flex-row-reverse flex-wrap justify-content-start">
          <span
            v-for="filter in visibleAssetFilters" 
            :key="filter.label"
            class="pb-2"
          >
            <active-filter-multiple-asset
              id-label="id"
              :label="filter.label"
              name="name"
              :all-options="filter.options"
              :filters="filter.active"
              @apply-selected="filtersUpdated"
            />
          </span>
        </div>

        <span
          v-for="filter in activeFilters"
          :key="`${filter.filterName}_${filter.filter.id}`"
        >
          <active-filter
            :filter="filter"
            @clear-filter="clearFilter"
          />
        </span>
        <div
          v-if="dashboardView == 'management'"
          class="mb-0"
        >
          <active-filter-multiple
            v-if="companyFilter && companyFilter.length > 0"
            :filters="companyFilter"
            :filter-mutation="`setCompanyFilter`"
            @clear-filter="updateSelectedCompanies"
          />
        </div>

        <filter-button
          :active-filters-count="activeMultiFiltersCount"
          :class="disableLinks"
          @toggle-filter="toggleFilterMenu"
        />
      </div>
    </div>

    <div :class="isRiskCenterModule ? 'filters-margin-left' : 'row justify-content-end mx-0'">
      <dismissible-container
        ref="dismissibleContainer"
        :container-classes="'dismissible-container--right mt-0 mb-3'"
        :persist-state="false"
        show-pointer
        @show-hide-container="toggleFilterMenu"
        @close-container="toggleFilterMenu"
      >
        <asset-filters @filters-updated="filtersUpdated" />
      </dismissible-container>
    </div>
  </div>
</template>

<script>
  import dropdownClose from 'mixins/dropdown_close_outside_click';
  import DismissibleContainer from 'components/shared/dismissible_container.vue';
  import ActiveFilterMultiple from 'components/shared/active_filter_multiple.vue';
  import FilterButton from 'components/shared/filter_button.vue';
  import ActiveFilter from 'components/shared/active_filter.vue';
  import mspHelper from 'mixins/msp_helper';
  import { mapGetters, mapMutations } from 'vuex';
  import AssetFilters from './asset_filters.vue';
  import activeFilterMultipleAsset from '../shared/active_filter_multiple_asset.vue';
  import assetFiltersHelper from '../../mixins/assets/asset_filters_helper';

  export default {
    components: {
      DismissibleContainer,
      ActiveFilterMultiple,
      AssetFilters,
      FilterButton,
      ActiveFilter,
      activeFilterMultipleAsset,
    },
    mixins: [mspHelper,assetFiltersHelper, dropdownClose],
    props: {
      isRiskCenterModule: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      ...mapGetters([
        'activeFilters',
        'activeMultiFiltersCount',
      ]),
      ...mapGetters('GlobalStore', ['dashboardView', 'companyFilter']),
      visibleAssetFilters() {
        return Object.entries(this.assetFilters)
          .filter(([key, filter]) => key && Array.isArray(filter.active) && filter.active.length && !this.mspFlag)
          .map(([label, filter]) => ({ label, ...filter }));
      },
    },
    methods: {
      ...mapMutations([
        'clearAssetFilter',
      ]),
      filtersUpdated() {
        this.$emit('filters-updated');
      },
      toggleFilterMenu() {
        this.hideAllOtherMenus();
        this.$refs.dismissibleContainer.toggleOpen();
      },
      clearFilter(filter) {
        this.clearAssetFilter(filter);
        this.filtersUpdated();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .filters-margin-left {
    margin-left: -15rem;
  }
</style>
