<template>
  <tr
    class="box--with-hover"
    :class="{'row-selected': isSelected}"
    :data-tc-asset="asset.name"
    @click.stop.prevent="routeOrSelect"
  >
    <td
      v-for="(preference, index) in assetPreferences"
      :key="`asset-row-data-${index}`"
      :class="{
        'asset-tags': preference.name == 'tags',
        'table-data--sticky': isSticky(preference.friendlyName)
      }"
    >
      <asset-row-data
        :asset="asset"
        :preference="preference"
        :show-select-checkbox="showSelectCheckbox"
        :is-discovered-asset="isDiscoveredAsset"
        :is-selected="isSelected"
        @select-asset="selectAsset"
      />
    </td>
    <td v-if="isIcon">
      <i class="nulodgicon-external-link" />
    </td>
  </tr>
</template>

<script>
import { mapMutations } from 'vuex';
import dates from 'mixins/dates';
import strings from 'mixins/string';
import assetImages from 'mixins/asset_images';
import tableLayoutStyle from 'mixins/table_layout_style';
import { managementToCompany } from 'mixins/msp_helper';
import AssetRowData from './asset_row_data.vue';

export default {
  components: {
    AssetRowData,
  },
  mixins: [dates, strings, assetImages, tableLayoutStyle],
  props: {
    asset: {
      type: Object,
      default: () => {},
    },
    isTagging: {
      type: Boolean,
    },
    selectedAssetIdsArr: {
      type: Array,
      default: () => [],
    },
    isIcon: {
      type: Boolean,
    },
    assetPreferences: {
      type: Array,
      default: () => [],
    },
    showSelectCheckbox: {
      type: Boolean,
      required: false,
      default: false,
    },
    isInsights: {
      type: Boolean,
      default: false,
    },
    isBulkSelected: {
      type: Boolean,
      default: false,
    },
    isDiscoveredAsset: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      listTableLayoutModuleStyle: "managed_assets",
    };
  },
  computed: {
    isSelected() {
      if (this.selectedAssetIdsArr.includes(this.asset.id)) {
        return true;
      } 
      return false;
    },
  },
  methods: {
    ...mapMutations([
      'setCurrentAsset',
      'updateAsset',
      'setPreviousRoute',
    ]),
    routeOrSelect(e) {
      this.handleSwitchToCompanyView();
      if (this.isBulkSelected) {
        this.selectAsset();
      } else {
        this.setPreviousRoute(`${this.$route.path}`);
        const assetUrl = `/managed_assets/${this.asset.id}`;
        if (e.shiftKey && this.showSelectCheckbox) {
          this.selectAsset();
        } else if (this.asset.companyId !== getCompanyFromStorage().id) {
          this.$emit('child-asset-opener', this.asset);
        } else if (e.ctrlKey || e.metaKey) {
          window.open(assetUrl, '_blank');
        } else if (this.$router.options.base === '/vendors') {
          const url =`/managed_assets/${this.asset.id}`;
          window.open(url, "_blank");
        } else {
          const { id } = this.asset;
          if (this.isInsights) {
            window.open(assetUrl, '_blank');
          } else {
            const url = this.isDiscoveredAsset
              ? `/discovered_assets/${id}`
              : `/${id}`;
            this.$router.push({ path: url });
          }
        }
      }
    },
    selectAsset() {
      this.$emit('select-asset', this.asset.id);
    },
    handleSwitchToCompanyView() {
      managementToCompany();
    },
  },
};
</script>

<style scoped lang="scss">
[type='checkbox'] {
  display: none;
}

.asset-icon {
  color: $color-assets;
}

.asset-tags {
  max-width: 150px;
}

:checked ~ .checkbox {
  background-color: $themed-link;
  border-color: $themed-link;
}
</style>
