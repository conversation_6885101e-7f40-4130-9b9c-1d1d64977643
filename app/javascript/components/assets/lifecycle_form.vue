<template>
  <div>
    <div>
      <div class="form-group row">
        <div
          v-if="isCustomLifecycles && !isAssetForm"
          class="col-sm-12 col-md-6"
        >
          <label for="name">Lifecycle Name <span class="required"/></label>
          <input
            id="name"
            v-model="lifecycleData.name"
            v-validate="'required'"
            placeholder="Enter lifecycle name"
            data-vv-as="lifecycle name"
            name="name"
            class="form-control"
            :class="{ 'is-invalid': errors.has('name') }"
            data-tc-field="life cycle name"
          >
          <div
            v-show="errors.has('name')"
            class="form-text text-danger small"
          >
            {{ errors.first("name") }}
          </div>
        </div>
        <div
          v-if="isLifecyclesByType && !isAssetForm"
          class="col-sm-12 col-lg-6"
        >
          <div class="form-group">
            <label for="asset_type">Asset Type <span class="required"/></label>
            <multi-select
              id="asset_type"
              v-validate="'required'"
              placeholder="Select an asset type"
              data-vv-as="asset type"
              track-by="id"
              name="asset_type"
              label="name"
              class="asset-type"
              deselect-label="Can't remove selected asset type"
              :class="{ 'is-invalid': errors.has('asset_type') }"
              :allow-empty="false"
              :multiple="false"
              :options="assetTypes"
              :value="assetType"
              @input="assignAssetType"
              @open="getAssetTypes"
            />
            <div
              v-show="errors.has('asset_type')"
              class="form-text text-danger small"
            >
              {{ errors.first("asset_type") }}
            </div>
          </div>
        </div>
        <div
          v-if="isLifecyclesByTag && !isAssetForm"
          class="col-sm-12 col-lg-6"
        >
          <div class="form-group">
            <label for="asset_tag">Asset Tag <span class="required"/></label>
            <multi-select
              id="asset_tag"
              v-validate="'required'"
              placeholder="Select an asset tag"
              data-vv-as="asset tag"
              track-by="id"
              name="asset_tag"
              label="name"
              class="asset-type"
              deselect-label="Can't remove selected asset tag"
              :class="{ 'is-invalid': errors.has('asset_tag') }"
              :allow-empty="false"
              :multiple="false"
              :options="assetTags"
              :value="assetTag"
              @input="assignAssetTag"
              @open="fetchCompanyAssetTags"
            />
            <div
              v-show="errors.has('asset_tag')"
              class="form-text text-danger small"
            >
              {{ errors.first("asset_tag") }}
            </div>
          </div>
        </div>
        <div
          v-if="isAssetForm"
          class="col-sm-12 col-lg-6"
        >
          <div class="form-group">
            <label for="custom_lifecycle">Custom Lifecycle <span class="small text-muted ml-1">(optional)</span></label>
            <multi-select
              id="custom_lifecycle"
              class="lifecycle"
              placeholder="Select an custom lifecycle"
              track-by="id"
              name="custom_lifecycle"
              label="name"
              deselect-label="Remove selected custom lifecycle"
              :allow-empty="true"
              :multiple="false"
              :options="customLifecycles"
              :value="customLifecycle"
              data-tc-label="custom life cycle"
              @remove="unAssignCustomLifecycles"
              @input="assignCustomLifecycle"
              @open="fetchCustomLifecycles"
            />
          </div>
        </div>
        <div class="col-md-6 align-content-center text-center">
          <span
            v-if="isAutomaticLifecycles || isAssetForm"
            class="btn btn-text text-muted"
            @click.stop="redirectToPage"
          >
            <i class="nulodgicon-external-link text-muted mr-1 clickable external-link-size" />
            {{ customizeBtnText }}
          </span>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-4">
          <label for="purchase_price">Purchase Price <span class="small text-muted ml-1">(optional)</span></label>
          <currency-input
            id="purchase_price"
            v-model="lifecycleData.purchasePrice"
            v-validate="costValidation"
            data-vv-as="purchase price"
            name="purchase_price"
            :input-class="{ 'is-invalid': errors.has('purchase_price') }"
            data-tc-field="purchase price"
          />
          <small
            v-show="errors.has('purchase_price')"
            class="form-text text-danger"
          >
            {{ errors.first('purchase_price') }}
          </small>
        </div>
        <div class="col-4">
          <label for="replacement_cost">Replacement Cost <span class="small text-muted ml-1">(optional)</span></label>
          <currency-input
            id="replacement_cost"
            v-model="lifecycleData.replacementCost"
            v-validate="costValidation"
            data-vv-as="replacement cost"
            name="replacement_cost"
            :input-class="{ 'is-invalid': errors.has('replacement_cost') }"
            data-tc-field="replacement cost"
          />
          <small
            v-show="errors.has('replacement_cost')"
            class="form-text text-danger"
          >
            {{ errors.first('replacement_cost') }}
          </small>
        </div>
        <div class="col-4">
          <label for="salvage">Salvage Value <span class="small text-muted ml-1">(optional)</span></label>
          <currency-input
            id="salvage"
            v-model="lifecycleData.salvage"
            v-validate="costValidation"
            data-vv-as="salvage value"
            name="salvage"
            :input-class="{ 'is-invalid': errors.has('salvage') }"
            data-tc-field="salvage value"
          />
          <small
            v-show="errors.has('salvage')"
            class="form-text text-danger"
          >
            {{ errors.first('salvage') }}
          </small>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-4">
          <label for="useful_life">Useful Life <span class="small text-muted ml-1">(optional)</span></label>
          <div class="d-flex">
            <input
              id="useful_life"
              v-model="lifecycleData.usefulLife"
              type="number"
              class="form-control w-50"
              data-tc-field="useful life"
            >
            <span class="ml-2 align-self-center h6">Years</span>
          </div>
        </div>
        <div class="col-4">
          <label for="approaching_end_of_life">Approaching End-of-Life <span class="small text-muted ml-1">(optional)</span></label>
          <div class="d-flex">
            <input
              id="approaching_end_of_life"
              v-model="lifecycleData.approachingEndOfLife"
              type="number"
              class="form-control w-50"
              data-tc-field="approaching life"
            >
            <span class="ml-2 align-self-center h6">Months</span>
          </div>
        </div>
        <div class="col-4">
          <label for="po">
            PO#
            <span class="small text-muted ml-1">
              (optional)
            </span>
          </label>
          <div class="d-flex">
            <input
              id="po"
              v-model="lifecycleData.po"
              type="text"
              class="form-control w-50"
              maxlength="50"
            >
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="!isAssetForm"
      class="sweet-buttons sweet-custom-footer pb-0 pt-3 border-top"
    >
      <button
        slot="button"
        class="btn btn-link text-secondary mr-2"
        @click.prevent="closeLifecycleModal"
      >
        Cancel
      </button>
      <submit-button
        :is-saving="disabled"
        :btn-classes="'mx-2 btn-primary'"
        :btn-content="btnContent"
        :saving-content="savingContent"
        @submit="saveLifecycle"
      />
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapGetters, mapMutations, mapActions } from 'vuex';
  import permissionsHelper from 'mixins/permissions_helper';
  import lifecyclesHelper from 'mixins/assets/lifecycles_helper';
  import MultiSelect from "vue-multiselect";
  import CurrencyInput from "components/shared/currency_input.vue";
  import SubmitButton from 'components/shared/submit_button.vue';

  export default {
    components: {
      CurrencyInput,
      SubmitButton,
      MultiSelect,
    },
    mixins: [permissionsHelper, lifecyclesHelper],
    $_veeValidate: {
      validator: "new",
    },
    props: {
      data: {
        type: Object,
        required: false,
        default: () => {},
      },
      activeType: {
        type: String,
        default: '',
      },
      lifecycleType: {
        type: String,
        default: '',
      },
      isAssetForm: {
        type: Boolean,
        default: false,
      },
      resetValues: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        lifecycleData: this.data,
        disabled: false,
        assetType: null,
        assetTag: null,
        customLifecycle: null,
        customLifecycles: [],
      };
    },
    computed: {
      ...mapGetters([
        "assetTags",
        "assetTypes",
      ]),
      costValidation() {
        return { required: false, regex: /^[,\d]+(\.\d{0,2})?$/ };
      },
      isUpdateForm() {
        return !!this.data?.id;
      },
      btnContent() {
        return this.isUpdateForm ? 'Update Lifecycle' : 'Create Lifecycle';
      },
      savingContent() {
        return this.isUpdateForm ? 'Updating...' : 'Creating...';
      },
      customizeBtnText() {
        const btnTextObj = {
          CompanyAssetType: "Customize Asset Types",
          CompanyAssetTag: "Customize Asset Tags",
        };
        return this.isAssetForm ? 'Customize Lifecycles' : btnTextObj[this.activeType] ;
      },
    },
    watch: {
      data() {
        this.lifecycleData = { ...this.data };
        if (!this.isAssetForm) {
          if (this.data?.assetType) {
            this.assetType = { ...this.data.assetType };
          }
          if (this.data?.assetTag) {
            this.assetTag = { ...this.data.assetTag };
          }
        }
        this.$validator.reset();
      },
      resetValues() {
        if (this.resetValues) {
          this.resetData();
        }
      },
      assetType() {
        if (this.assetType && this.$validator.errors.has("asset_type")) {
          this.$validator.errors.remove("asset_type");
        }
      },
      assetTag() {
        if (this.assetTag && this.$validator.errors.has("asset_tag")) {
          this.$validator.errors.remove("asset_tag");
        }
      },
      'lifecycleData.purchasePrice': {
        handler() {
          this.emitLifecycleData();
        },
      },
      'lifecycleData.replacementCost': {
        handler() {
          this.emitLifecycleData();
        },
      },
      'lifecycleData.salvage': {
        handler() {
          this.emitLifecycleData();
        },
      },
      'lifecycleData.usefulLife': {
        handler() {
          this.emitLifecycleData();
        },
      },
      'lifecycleData.approachingEndOfLife': {
        handler() {
          this.emitLifecycleData();
        },
      },
      'lifecycleData.po': {
        handler() {
          this.emitLifecycleData();
        },
      },
    },
    methods: {
      ...mapMutations(["setAssetTypes"]),
      ...mapActions(["fetchCompanyAssetTags"]),
      onWorkspaceChange() {
        if (this.isUpdateForm) {
          const callbackObj = {
            CompanyAssetType: this.getAssetTypes,
            CompanyAssetTag: this.fetchCompanyAssetTags,
          };
          if (this.isAutomaticLifecycles && this.activeType) {
            callbackObj[this.activeType]();
          }
        }
        if (this.isAssetForm) {
          this.fetchCustomLifecycles();
        }
      },
      saveLifecycle() {
        this.$validator.validateAll().then((result) => {
          if (result) {
            const lifecycleObj = {
              purchasePrice: this.lifecycleData.purchasePrice,
              replacementCost: this.lifecycleData.replacementCost,
              usefulLife: this.lifecycleData.usefulLife,
              approachingEndOfLife: this.lifecycleData.approachingEndOfLife,
              salvage: this.lifecycleData.salvage,
              lifecycleType: this.lifecycleType,
              po: this.lifecycleData.po,
            };
            if (this.isAutomaticLifecycles) {
              lifecycleObj.linkedItemType = this.activeType;
              lifecycleObj.linkedItemId = this.isLifecyclesByType ? this.assetType.id : this.assetTag.id;
              lifecycleObj.name = this.isLifecyclesByType ? this.assetType.name : this.assetTag.name;
            } else {
              lifecycleObj.name = this.lifecycleData.name;
            }

            this.$emit('save-lifecycle', lifecycleObj);
          };
        });
      },
      closeLifecycleModal() {
        this.resetData();
        this.lifecycleData = {};
        this.$emit('close-modal');
      },
      resetData() {
        this.assetType = null;
        this.assetTag = null;
        this.customLifecycle = null;
      },
      assignAssetType(type) {
        this.assetType = type;
      },
      assignAssetTag(tag) {
        this.assetTag = tag;
      },
      assignCustomLifecycle(lifecycle) {
        this.customLifecycle = lifecycle;
        this.$emit('assign-custom-lifecycle', lifecycle);
      },
      unAssignCustomLifecycles() {
        this.customLifecycle = null;
        this.$emit('un-assign-custom-lifecycle');
      },
      redirectToPage() {
        const lifecycleActiveTypePages = {
          CompanyAssetType: "/managed_assets/settings/asset_types",
          CompanyAssetTag: "/managed_assets/settings/tags",
        };

        const url = this.isAssetForm
                    ? "/managed_assets/settings/lifecycle_management"
                    : lifecycleActiveTypePages[this.activeType];
        window.open(url, '_blank');
      },
      getAssetTypes() {
        http
          .get("/company_asset_types.json")
          .then(res => {
            this.setAssetTypes(res.data.assetTypes);
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
            this.emitError(`Sorry, there was an error loading asset types.`);
          });
      },
      emitLifecycleData() {
        if(this.isAssetForm) {
          this.$emit('lifecycle-data', this.lifecycleData);
        }
      },
      fetchCustomLifecycles() {
        http
          .get("/asset_lifecycles/custom.json")
          .then(res => {
            this.customLifecycles = res.data;
            if (!this.customLifecycle && this.data?.lifecycleType === 'custom' && this.data?.assetLifecycleId) {
              const lifecycle = res.data.find((cl) => cl.id === this.data.assetLifecycleId);
              if (lifecycle) {
                this.customLifecycle = { ...lifecycle };
              }
            }
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading custom lifecycles.`);
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .lifecycle :deep(.multiselect__tags) {
    z-index: 0;
  }
</style>

