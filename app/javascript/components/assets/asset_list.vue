<template>
  <div>
    <div
      class="clearfix align-items-end mt-3"
    >
      <div class="mt-2">
        <div class="rounded-top text-white box__header">
          <div class="row justify-content-between">
            <div class="d-flex">
              <h5
                class="d-flex font-weight-bold d-inline-block mr-3 ml-4 mt-2 mb-0"
                data-tc-asset-list-heading
              >
                Assets
              </h5>
              <div
                v-if="assetsLoading"
                class="d-flex align-items-center ml-n3 mt-2"
              >
                <span class="d-inline-block align-top ml-2">
                  <pulse-loader
                    color="#0d6efd"
                    size="0.5rem"
                    :loading="true"
                  />
                </span>
              </div>
              <div
                v-if="assetsPresent && !assetsLoading"
                :class="{
                  'asset-heading--bulk-selecting d-flex align-items-center':
                    isBulkSelecting,
                }"
              >
                <div class="d-flex font-weight-normal d-inline-block mr-3 ml-n3 mt-2">
                  <span
                    v-if="pageCount <= 1"
                    class="ml-1 not-as-small text-themed-fair"
                  >
                    (Showing {{ totalRecord }} of {{ totalRecord }})
                  </span>
                  <span
                    v-else
                    class="ml-1 not-as-small text-white"
                  >
                    (Showing {{ pageStart }}&dash;{{ pageEnd }} of {{ totalRecord }})
                  </span>
                  <sup
                    v-if="!selectedAssets.length"
                    v-tooltip.top="'Archived assets will not appear in the list unless their filter is selected.'"
                    class="genuicon-info-circled align-bottom text-faded-light true-small ml-1"
                  />
                </div>
                <span
                  v-if="isBulkSelecting"
                  class="text-muted mt-2"
                >
                  &mdash; Bulk Edit
                </span>
              </div>
            </div>
            <div class="col-auto d-flex align-items-center justify-content-end pagination-wrapper">
              <div
                v-if="!showMassEdit"
                class="text-right col-md-12"
              >
                <filter-button
                  :active-filters-count="activeMultiFiltersCount"
                  :class="disableLinks"
                  @toggle-filter="toggleFilterMenu"
                />
                <view-type-toggle
                  class="ml-3"
                  :module-name="`assets`"
                  @view-type="setToggleView"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-themed-light px-4 pt-3 mb-n3">
        <div class="row justify-content-end m-0">
          <dismissible-container
            ref="dismissibleContainer"
            show-pointer
            class="mt-n4 mb-3 mr-1"
            @close-container="closeFilterMenu"
          >
            <asset-filters
              v-if="isFilterMenuOpen"
              class="bg-themed-box-bg p-1 rounded-pill border"
              @filters-updated="filtersUpdated"
            />
          </dismissible-container>
        </div>
        <div class="row justify-content-between align-items-start">
          <div class="col-auto">
            <search-input 
              v-if="!showLoading"
              class="asset-search-bar"
              @fetch-assets="fetchAssets"  
            />
          </div>
          <div
            v-if="!showMassEdit && !assetsLoading"
            class="col-auto d-flex justify-content-end align-items-start pagination-wrapper"
            :class="{ 'mb-2': isBulkSelecting }"
          >
            <div class="d-flex align-items-end">
              <span 
                v-if="totalRecord > 25"
                class="small ml-2 d-flex align-items-center"
              >
                <span class="text-muted align-text-bottom mr-2">
                  <span> Results per page </span>
                </span>
                <select
                  id="filtersPerPage"
                  class="form-control form-control-sm d-inline-block select-per-page-filter pt-0"
                  :input="pageSize"
                  :value="pageSize"
                  data-tc-filters-per-page
                  @input="changePageSize"
                >
                  <option>25</option>
                  <option>50</option>
                  <option>100</option>
                </select>
              </span>
              <div
                v-if="pageCount > 1"
                class="align-self-center ml-3"
              >
                <paginate
                  ref="paginate"
                  :click-handler="pageSelected"
                  :container-class="'pagination pagination-sm mb-0'"
                  :next-class="'next-item'"
                  :next-link-class="'page-link'"
                  :next-text="'Next'"
                  :page-class="'page-item'"
                  :page-count="pageCount"
                  :page-link-class="'page-link'"
                  :prev-class="'prev-item'"
                  :prev-link-class="'page-link'"
                  :prev-text="'Prev'"
                  :selected="page"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="d-flex flex-row-reverse flex-wrap justify-content-start">
          <span
            v-for="filter in visibleAssetFilters" 
            :key="filter.label"
            class="pb-2"
          >
            <active-filter-multiple-asset
              id-label="id"
              :label="filter.label"
              name="name"
              :all-options="filter.options"
              :filters="filter.active"
              @apply-selected="filtersUpdated"
            />
          </span>
        </div>

        <div 
          v-if="!assetsLoading"
          class="row justify-content-end m-0"
        >
          <div
            v-if="dashboardView === 'management'"
            class="mb-0"
          >
            <active-filter-multiple
              v-if="companyFilter && companyFilter.length > 0"
              :filters="companyFilter"
              :filter-mutation="`setCompanyFilter`"
              @clear-filter="updateSelectedCompanies"
            />
          </div>
        </div>
      </div>
    </div>

    <div
      v-show="isBulkSelecting"
      class="asset-bulk-edit-wrapper position-relative box--flat bg-lighter pt-3 mt-3 mb-n3"
    >
      <div class="row justify-content-between">
        <div class="overflow-auto scrollable--hidden-bar pb-2 col px-3">
          <div class="sub-menu text-nowrap ml-3 mr-3 w-auto">
            <div class="module-sub-tabs w-75">
              <div
                v-for="(action, index) in bulkActions"
                :key="index"
              >
                <div
                  v-if="
                    action.shouldShowCallback &&
                      action.shouldShowCallback() &&
                      !hideBulkUpdate(action)
                  "
                  class="sub-menu-item flex-grow-1 clickable mr-3 pb-1"
                  :class="{
                    'sub-menu-item--active': subIsActive(
                      convertStringToCamelCase(action.name)
                    ),
                  }"
                  @click="action.clickHandler"
                >
                  <i :class="action.iconClass" />
                  <span
                    class="not-as-small"
                    :data-tc-action="action.name"
                  >{{
                    action.name
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="btn-close-basic">
          <a
            v-tooltip="'Close Bulk Edit'"
            @click="unSelectAssets"
          >
            <i class="nulodgicon-android-close" />
          </a>
        </div>
      </div>
      <div class="w-100 text-secondary px-4 py-3">
        <div
          v-show="
            activeBulkAction === 'bulkUpdate' &&
              !allAssetsSelected &&
              selectedAssets.length > 0
          "
          class="row"
        >
          <h6 class="col-12 mb-0 font-weight-normal d-flex align-items-center">
            Bulk Update
          </h6>

          <div class="col-12 not-as-small col-md-auto bg-lighter px-3">
            Quickly edit multiple assets at once, or one-by-one at spreadsheet
            speeds.
          </div>

          <div
            class="col d-flex align-items-end justify-content-md-start justify-content-end pb-1 pt-3 pt-md-1"
          >
            <button
              class="btn btn-sm btn-primary"
              data-tc-btn="bulk update again"
              @click="openBulkUpdateModal"
            >
              Bulk update again
            </button>
          </div>
        </div>

        <div
          v-show="activeBulkAction === 'applyTags' && selectedAssets.length > 0"
          class="row"
        >
          <div class="col-12 col-md-auto bg-lighter px-3 w-100 mb-n5">
            <h6 class="mb-2 font-weight-normal d-flex align-items-center">
              <i
                class="nulodgicon-pricetag text-muted small font-weight-normal mr-1 mb-0"
              />
              <span class="not-as-small">
                Select tags to assign or remove -
              </span>
              <span class="not-as-small ml-2">
                <strong>{{
                  assetTags && assetTags.filter((tag) => tag.checked).length
                }}
                  tag(s)</strong>
                selected
              </span>
            </h6>

            <div class="my-4">
              <tag-asset
                v-for="(tag, index) in companyAssetTags"
                :key="tag.id"
                class="mr-2 mb-2"
                :label="tag.name"
                :checked="tag.checked"
                @toggle="toggleTag(index)"
              />
            </div>
            <div
              v-if="showCollapse"
              class="d-flex justify-content-center position-relative"
            >
              <button
                class="btn btn-pill btn-sm position-absolute btn-view-more"
                @click="showMoreTags"
              >
                {{ showMore ? "View less" : "View more tags" }}
              </button>
            </div>
          </div>

          <div class="col">
            <hr >
            <div class="d-flex row">
              <form
                class="col-3 d-flex"
                data-tc-input="Add new Tag"
                @submit.prevent="addNewTag(newTagInput)"
              >
                <input
                  v-model="newTagInput"
                  type="text"
                  class="form-control pr-7"
                  placeholder="Add new Tag"
                  aria-label="Add new Tag"
                  aria-describedby="button-addon2"
                >
                <button
                  class="btn btn-sm btn-link position-absolute right-0 mt-1 mr-3"
                  type="submit"
                  data-tc-btn="Add new Tag"
                >
                  + New tag
                </button>
              </form>

              <div class="col d-flex">
                <button
                  class="btn btn-sm ml-auto btn-primary"
                  data-tc-save-tag-assignment
                  @click="applyNewTagsSubmit"
                >
                  Save tag assignment
                </button>
              </div>
            </div>
          </div>
        </div>

        <div
          v-show="
            activeBulkAction === 'assignLocations' && selectedAssets.length > 0
          "
          class="row"
        >
          <div class="col-12 col-md-4 bg-lighter rounded">
            <label
              for="assets_locations"
              class="mb-1"
            >
              <i class="nulodgicon-location text-muted h6" />
              Location
            </label>
            <location-options
              :multiple="false"
              :bulk-asset-location="selectedLocation"
              :module-type="'Bulk Assets'"
              @input="assignLocation"
              @fetch-locations="fetchLocations"
            />
          </div>

          <div
            class="col d-flex align-items-end justify-content-md-start justify-content-end pb-1 pt-3 pt-md-1"
          >
            <button
              class="btn btn-sm btn-primary"
              :disabled="saveButtonClicked"
              @click="applyNewLocationsSubmit"
            >
              Save location assignment
            </button>
          </div>
        </div>

        <div
          v-show="
            activeBulkAction === 'assignUsers' && selectedAssets.length > 0
          "
          class="row"
        >
          <div class="col-12 col-md-5 bg-lighter rounded">
            <i class="nulodgicon-person text-muted h6" />
            <label
              for="assets_users"
              class="mb-1"
            > Used By </label>
            <contributors-select
              id="used-by-company-user"
              name="managed_asset[assignment_information_attributes][used_by_contributor_id]"
              :value="usedByContributor"
              compact
              @select="usedByContributorIdChanged"
            />
          </div>

          <div class="col-12 col-md-5 bg-lighter rounded">
            <i class="nulodgicon-person text-muted h6" />
            <label
              for="assets_users"
              class="mb-1"
            > Managed By </label>
            <contributors-select
              id="managed-by-company-user"
              name="managed_asset[assignment_information_attributes][managed_by_contributor_id]"
              :value="managedByContributor"
              compact
              @select="managedByContributorIdChanged"
            />
          </div>

          <div
            class="col d-flex align-items-end justify-content-md-start justify-content-end pb-1 pt-3 pt-md-1"
          >
            <button
              class="btn btn-sm btn-primary"
              :disabled="!usedByContributor && !managedByContributor"
              @click="assignUsersSubmit"
            >
              Save user assignment
            </button>
          </div>
        </div>

        <div
          v-if="activeBulkAction === 'printQRCode' && selectedAssets.length > 0"
          class="row d-flex justify-content-between align-items-baseline w-75 text-secondary py-3"
        >
          <h6 class="col-12 mb-0 font-weight-normal">Generate QR Codes</h6>

          <div class="not-as-small col-md-auto bg-lighter px-3">
            Customize the appearance of your QR codes by configuring the
            printing format in the
            <router-link :to="{ path: `/settings/qr_code` }">
              settings.
            </router-link>
            <button
              class="btn btn-sm btn-primary ml-3"
              @click.stop="openPrintQrModal"
            >
              Print QR Codes
            </button>
          </div>
        </div>
      </div>

      <div class="row mx-0 px-0 mt-2 small py-1 bg-light">
        <div
          class="text-center text-secondary mb-0 col-6 offset-3 rounded-bottom"
        >
          <div>
            <span>{{ itemSelectionMessage }}</span>
            <button
              v-if="selectedAssets.length < totalRecord && !allAssetsSelected"
              class="btn btn-sm btn-link"
              data-tc-asset="select all"
              @click="selectAllAssetsRecord"
            >
              Select all {{ totalRecord }} items
            </button>
          </div>
        </div>
        <div class="col d-flex justify-content-end pr-2">
          <div class="d-flex align-items-center">
            <button
              v-if="selectedAssets.length > 0"
              class="btn btn-sm btn-link text-danger ml-2 rounded"
              @click="unSelectAssets"
            >
              Unselect all
            </button>

            <button
              v-if="selectedAssets.length < assets.length"
              class="btn btn-sm btn-link"
              @click="selectAllAssets"
            >
              Select {{ smallestOfAssetOrPageSize }} on page
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-themed-light px-5 rounded-bottom">
      <hr class="mb-4">
      <toggle-view
        v-if="assetsPresent && !assetsLoading && !showMassEdit"
        is-assets-module
        :view="viewType"
        :table-type="'scrollable'"
        :table-style="'table--spaced'"
        :table-class="'table--spaced'"
        :settings-style="`scrollable-table-settings--spaced`"
        :min-width="tableMinWidth(tableHeader.length)"
        :settings-path="'/settings/table_data'"
        :show-settings-icon="!mspFlag"
        asset-list-view-table
      >
        <asset-item
          v-for="asset in assets"
          slot="grid"
          :key="asset.id"
          :asset="asset"
          :disable-selection="mspFlag"
          :selected-asset-ids-arr="selectedAssets"
          :is-bulk-selected="isBulkSelecting"
          @select-asset="selectAsset"
          @child-asset-opener="childCompanyAssetOpener"
        />
        <template slot="list">
          <thead>
            <table-header-row
              ref="tableHeaderRow"
              :table-header="tableHeader"
              :sort-item="sortItem"
              :active-sort="activeSort"
              :draggable-header-preferences="assetPreferences"
              :active-sort-direction="activeSortDirection"
              :list-table-layout-module-style-override="listTableLayoutModuleStyle"
              :show-select-all-checkbox-inline="showSelectAllCheckbox"
              :is-scroll-allow="false"
              :select-all-value="allPageAssetsSelected"
              @select-all="selectAllCheckboxClicked"
              @change="setSortCookies"
              @update-order="updateAssetListColumns"
            />
          </thead>
          <tbody>
            <asset-row
              v-for="asset in assets"
              :key="asset.id"
              :asset="asset"
              :is-icon="false"
              :asset-preferences="assetPreferences"
              :selected-asset-ids-arr="selectedAssets"
              :show-select-checkbox="!mspFlag"
              :is-bulk-selected="isBulkSelecting"
              @select-asset="selectAsset"
              @child-asset-opener="childCompanyAssetOpener"
            />
          </tbody>
        </template>
      </toggle-view>

      <div
        v-else-if="!assetsLoading && !assetsPresent"
        id="noAssetsPresentHeader"
        class="text-center p-4 mt-5"
      >
        <h4 data-tc-not-tracking-assets>Not tracking anything here, yet.</h4>
        <h6
          v-if="isWrite"
          class="text-secondary font-weight-normal"
        >
          Try clearing the search box, removing filters, or
          <router-link
            to="/discovery_tools/connectors"
            data-tc-link="discover one"
          >
            discover
          </router-link>
          or
          <router-link
            to="/new"
            data-tc-link="add new one"
          >
            add a new one
          </router-link>
        </h6>
      </div>
    </div>
    <div
      v-if="!assetsLoading && pageCount > 1 && !showMassEdit && assetsPresent"
      class="d-flex justify-content-end mt-4 mb-n5"
    >
      <paginate
        ref="paginate"
        :click-handler="pageSelected"
        :container-class="'pagination pagination-sm align-self-center mb-0'"
        :next-class="'next-item'"
        :next-link-class="'page-link'"
        :next-text="'Next'"
        :page-class="'page-item'"
        :page-count="pageCount"
        :page-link-class="'page-link'"
        :prev-class="'prev-item'"
        :prev-link-class="'page-link'"
        :prev-text="'Prev'"
        :selected="page"
        class="align-self-center mb-0 mt-1"
      />
    </div>

    <merge-modal
      v-if="selectedAssets.length"
      ref="mergeAssetsModal"
      :selected-ids.sync="selectedAssets"
      :is-merge-modal-open.sync="isMergeModalOpen"
      @close-modal="closeMergeAssetsModal"
    />

    <div
      v-if="readyToGenerateQR"
      ref="qrPage"
    >
      <div :class="{ 'd-flex flex-wrap': parseInt(qrCodeSettings.numberOfColumns) > 1 }">
        <qr-code
          v-for="asset in qrAssets"
          :key="asset.id"
          :class="{
            'col-5': parseInt(qrCodeSettings.numberOfColumns) === 2,
            'col-4': parseInt(qrCodeSettings.numberOfColumns) === 3,
            hidden: !readyToDownloadQR,
          }"
          :url="generateUrl(asset.id)"
          :asset-details="qrAssetDetails(asset)"
          :is-bulk-printing="true"
          :company-logo-url="companyLogoUrl"
        />
      </div>
    </div>

    <asset-archive
      ref="assetArchive"
      :assets-arr="selectedAssets"
      :status-filter="statusFilter"
      :all-assets-selected="allAssetsSelected"
      @archived="clearAssetArchiving"
      @bulk-update-modal="openBulkAssetsUpdateModal"
      @close="clearAssetArchiving"
    />

    <sweet-modal
      ref="unArchiveAssetModal"
      v-sweet-esc
      title="Before you unarchive these assets..."
    >
      <template slot="default">
        <div class="text-center">
          <p>These will be unarchived. Would you like to continue?</p>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="clearAssetUnArchiving"
      >
        Cancel
      </button>
      <button
        id="modalArchiveBtn"
        slot="button"
        class="btn btn-link text-danger"
        data-tc-unarchive-asset-btn
        @click.stop="unArchiveAssets"
      >
        UnArchive
      </button>
    </sweet-modal>

    <sweet-modal
      ref="bulkAssetUpdateModal"
      v-sweet-esc
      :title="`Assets ${modalTitle} in progress ... `"
    >
      <template slot="default">
        <div>
          <h6 class="d-flex">
            Your selected assets will be {{ modalTitle }}d in a few minutes
            <span class="pl-2">
              <sync-loader
                color="#0d6efd"
                size="0.5rem"
                :loading="true"
              />
            </span>
          </h6>
          <p class="text-muted mt-n2">(You will be notified shortly)</p>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="clearbulkAssetModal"
      >
        Close
      </button>
    </sweet-modal>

    <sweet-modal
      ref="deleteAssetModal"
      v-sweet-esc
      title="Before you delete these assets..."
    >
      <template slot="default">
        <div class="text-center">
          <p>These will be deleted permanently. Would you like to continue?</p>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="clearAssetDeletion"
      >
        Cancel
      </button>
      <button
        id="modalDeletionBtn"
        slot="button"
        class="btn btn-link text-danger"
        data-tc-modal-delete-btn
        @click.stop="deleteAssets"
      >
        Delete
      </button>
    </sweet-modal>

    <sweet-modal
      ref="bulkUpdateModal"
      v-sweet-esc
      blocking
      class="bulk-modal"
      @close="closeUpdateAssets"
    >
      <div class="bulk-update-header mb-3 text-left">
        <h2
          class="font-weight-bold"
          data-tc-title="bulk update"
        >
          Bulk Update
          <span class="small text-muted">
            ({{ selectedAssetsArray.length }})
          </span>
        </h2>
        <p class="modal-detail mb-0">
          Quickly edit multiple assets at once, or one-by-one at spreadsheet
          speeds.
        </p>
      </div>
      <template slot="default">
        <div class="text-center">
          <div>
            <bulk-update-assets
              ref="bulk_update"
              :items-arr="filteredAssets"
              :header="header"
              :assets-types="assetTypes"
              :selected-assets="selectedAssetsArray"
              :contributor-options="companyUsersAndGroups"
              :bulk-update-erros="bulkUpdateErros"
              :is-data-fetched="isDataFetched"
            />
          </div>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        data-tc-btn="bulk cancel changes"
        @click.stop="closeBulkAssets"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        :disabled="!isDataFetched"
        data-tc-btn="bulk update all"
        @click.stop="okUpdateAssets"
      >
        Update All
      </button>
    </sweet-modal>

    <qr-codes-modal
      v-if="printQRRequest"
      ref="printQRCodeModal"
    />
  </div>
</template>
<script>
import dates from "mixins/dates";
import strings from "mixins/string";
import inflections from "mixins/inflections";
import assetImages from "mixins/asset_images";
import http from "common/http";
import { mapMutations, mapGetters, mapActions } from "vuex";
import Paginate from "vuejs-paginate";
import tableLayoutStyle from "mixins/table_layout_style";
import SyncLoader from "vue-spinner/src/SyncLoader.vue";
import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
import { SweetModal } from "sweet-modal-vue";
import sortHelper from "mixins/sorting_helper";
import contributorsSelect from "components/shared/contributors_select.vue";
import tagAsset from "components/shared/tag_asset.vue";
import _ from "lodash";
import permissionsHelper from "mixins/permissions_helper";
import dropdownClose from 'mixins/dropdown_close_outside_click';
import pluralize from "pluralize/pluralize";
import ActiveFilterMultiple from "components/shared/active_filter_multiple.vue";
import printJS from "print-js";
import assetHelper from "mixins/assets/asset_helper";
import activeSortHelper from "mixins/active_sort_helper";
import mspManagingHelper from "mixins/msp_managing_helper";
import mspHelper from "mixins/msp_helper";
import mspFilterLoader from "mixins/msp_filter_loader";
import filters from "mixins/filters";
import AssetItem from "./asset_item.vue";
import AssetRow from "./asset_row.vue";
import ViewTypeToggle from "../shared/view_type_toggle.vue";
import ToggleView from "../shared/toggle_view.vue";
import SearchInput from "./search_input.vue";
import AssetFilters from "./asset_filters.vue";
import DismissibleContainer from "../shared/dismissible_container.vue";
import FilterButton from "../shared/filter_button.vue";
import BulkUpdateAssets from "./bulk_update_assets.vue";
import TableHeaderRow from "../shared/list_view_table_header.vue";
import AssetArchive from "./asset_archive.vue";
import MergeModal from "./merge_asset_modal.vue";
import QrCode from "../shared/qr_code.vue";
import QrCodesModal from "./qr_codes_modal.vue";
import pageSizeHelper from "../../mixins/page_size_helper";
import channelCleanup from "../../mixins/custom_forms/channel_cleanup";
import LocationOptions from "../shared/location_options.vue";
import ActiveFilterMultipleAsset from "../shared/active_filter_multiple_asset.vue";
import assetFiltersHelper from "../../mixins/assets/asset_filters_helper";

export default {
  components: {
    Paginate,
    AssetItem,
    AssetRow,
    SyncLoader,
    PulseLoader,
    SweetModal,
    ToggleView,
    ViewTypeToggle,
    SearchInput,
    AssetFilters,
    DismissibleContainer,
    FilterButton,
    TableHeaderRow,
    BulkUpdateAssets,
    contributorsSelect,
    MergeModal,
    AssetArchive,
    tagAsset,
    QrCode,
    QrCodesModal,
    LocationOptions,
    ActiveFilterMultiple,
    ActiveFilterMultipleAsset,
  },
  mixins: [
    dates,
    strings,
    inflections,
    assetImages,
    sortHelper,
    permissionsHelper,
    tableLayoutStyle,
    channelCleanup,
    pageSizeHelper,
    assetHelper,
    activeSortHelper,
    mspManagingHelper,
    mspHelper,
    mspFilterLoader,
    filters,
    assetFiltersHelper,
    dropdownClose,
  ],
  props: ["work"],
  data() {
    return {
      showMore: false,
      isDeleting: false,
      iconsVisible: {},
      selectingTagsToApply: false,
      selectingLocationToApply: false,
      selectingUsersToAssign: false,
      tagsToApplyArr: [],
      selectedAssets: [],
      locationId: null,
      usedByContributor: null,
      usedByContributorId: null,
      managedByContributor: null,
      managedByContributorId: null,
      isFilterMenuOpen: false,
      showMassEdit: false,
      bulkUpdateErros: [],
      isMergeModalOpen: false,
      allAssetsSelected: false,
      statusFilter: {},
      selectedAssetsArray: [],
      listTableLayoutModuleStyle: "managed_assets",
      newTagInput: "",
      allAssetsDetails: [],
      qrAssets: [],
      readyToGenerateQR: false,
      isFilterRemoved: false,
      readyToDownloadQR: false,
      isDataFetched: false,
      offset: 0,
      perPage: 100,
      selectedLocation: null,
      saveButtonClicked: false,
      bulkActions: {
        applyTags: {
          name: "Apply tags",
          iconClass: "nulodgicon-pricetag text-muted small",
          shouldShowCallback: () => true,
          clickHandler: () => {
            this.showBulkAction("applyTags");
          },
        },
        assignLocations: {
          name: "Assign locations",
          iconClass: "nulodgicon-location text-muted h6",
          shouldShowCallback: () => true,
          clickHandler: () => {
            this.showBulkAction("assignLocations");
          },
        },
        assignUsers: {
          name: "Assign Users",
          iconClass: "nulodgicon-person text-muted",
          shouldShowCallback: () => true,
          clickHandler: () => {
            this.showBulkAction("assignUsers");
            this.assignUsers();
          },
        },
        bulkUpdate: {
          name: "Bulk update",
          iconClass: "nulodgicon-edit text-muted",
          shouldShowCallback: () => true,
          clickHandler: () => {
            this.showBulkAction("bulkUpdate");
            this.openBulkUpdateModal();
          },
        },
        merge: {
          name: "Merge",
          iconClass: "nulodgicon-link text-muted",
          shouldShowCallback: () =>
            this.selectedAssets.length > 1 && !this.allAssetsSelected,
          clickHandler: () => {
            this.showBulkAction("bulkUpdate");
            this.openMergeAssetsModal();
          },
        },
        archive: {
          name: "Archive",
          iconClass: "nulodgicon-archive text-muted",
          shouldShowCallback: () =>
            (this.assetStatus && this.assetStatus.id === "active") ||
            !this.isSeletedAssetsArchived,
          clickHandler: () => {
            this.showBulkAction("archive");
            this.openArchiveModal();
          },
        },
        printQRCodes: {
          name: "QR Codes",
          iconClass: "genuicon-qr-code text-muted",
          shouldShowCallback: () => true,
          clickHandler: () => {
            this.showBulkAction("printQRCode");
            this.generateQRCodes();
          },
        },
        unarchive: {
          name: "Unarchive",
          iconClass: "nulodgicon-unarchive text-muted",
          shouldShowCallback: () =>
            (this.assetStatus && this.assetStatus.id === "archived") ||
            !this.isSeletedAssetsActive,
          clickHandler: () => {
            this.showBulkAction("unarchive");
            this.openUnArchiveModal();
          },
        },
        delete: {
          name: "Delete",
          iconClass: "nulodgicon-trash-b",
          shouldShowCallback: () =>
            (this.assetStatus && this.assetStatus.id === "archived") ||
            this.isSeletedAssetsArchived,
          clickHandler: () => {
            this.showBulkAction("delete");
            this.openDeleteModal();
          },
        },
      },
      activeBulkAction: "applyTags",
      printQRRequest: false,
    };
  },
  computed: {
    ...mapGetters([
      "pageCount",
      "assets",
      "assetTypes",
      "pageIndex",
      "pageSize",
      "assetsLoading",
      "assetTags",
      "companyUsers",
      "assetStatus",
      "assetPreferences",
      "activeFilters",
      "activeFiltersCount",
      "sortByOptions",
      "totalRecord",
      "companyUsersAndGroups",
      "currentAsset",
      "assetAvailabilityStatus",
      "assetStatuses",
      "viewType",
      "qrCodeSettings",
      "companyLogoUrl",
      "warrantyStates",
      "search",
      'activeMultiFiltersCount',
    ]),
    ...mapGetters("GlobalStore", [
      "locations",
      "selectedCompanies",
      "userCompanies",
      "dashboardView",
      "companyFilter",
    ]),
    companyAssetTags() {
      return this.showMore ? this.assetTags : this.assetTags.slice(0, 25);
    },
    showCollapse() {
      return this.companyAssetTags.length > 24;
    },
    allPageAssetsSelected() {
      return this.selectedAssets.length >= this.smallestOfAssetOrPageSize;
    },
    isBulkSelecting() {
      return (
        this.isWrite && this.assetsPresent && this.selectedAssets.length > 0
      );
    },
    itemSelectionMessage() {
      if (this.allAssetsSelected) {
        return `All ${this.totalRecord} items are selected.`;
      }
      const hasMoreThanOne = this.selectAssetsCount > 1;
      const allItemsOnPage =
        this.selectAssetsCount === this.pageSize ||
        this.selectAssetsCount === this.assets.length;

      return `${allItemsOnPage && hasMoreThanOne ? "All" : ""} ${
        this.selectAssetsCount
      }  ${pluralize("item", this.selectAssetsCount)} ${
        hasMoreThanOne ? "are" : "is"
      } selected.`;
    },
    smallestOfAssetOrPageSize() {
      return Math.min(this.assets.length, this.pageSize);
    },
    assetsPresent() {
      return this.assets && this.assets.length > 0;
    },
    page() {
      return this.pageIndex;
    },
    checkSelectedAsset(assetId) {
      return this.selectedAssets.filter((a) => a.id === assetId);
    },
    selectedAssetArr() {
      return this.assets.filter((a) => this.selectedAssets.includes(a.id));
    },
    showLoading() {
      return !this.assets;
    },
    tableHeader() {
      return this.assetPreferences.map((preference) => ({
        title: preference.friendlyName,
        label: preference.friendlyName,
        name: preference.friendlyName,
        fieldType: preference.name || '',
        sortBy: preference.name,
      }));
    },
    isSeletedAssetsActive() {
      return this.selectedAssetArr.every((asset) => !asset.archived);
    },
    isSeletedAssetsArchived() {
      return this.selectedAssetArr.every((asset) => asset.archived);
    },
    header() {
      return [
        "Id",
        "Name",
        "Machine Serial Number",
        "Ip Address",
        "Product Number",
        "Asset Tag",
        "Department",
        "Acquisition Date",
        "Warranty Expiration",
        "Install Date",
        "Model",
        "Used By Contributor Id",
        "Managed By Contributor Id",
        "Description",
        "Asset Type Id",
        "Custom Status Id",
      ];
    },
    filteredAssets() {
      const assets = [];
      const contributorFieldMappings = {
        usedByContributorId: "usedBy",
        managedByContributorId: "managedBy",
      };
      for (let index = 0; index < this.selectedAssetArr.length; index += 1) {
        assets[index] = {};
        this.header.forEach((item) => {
          if (["Used By Contributor Id", "Managed By Contributor Id"].includes(item)) {
            const camelCaseItem = _.camelCase(item);
            assets[index][camelCaseItem] =
              this.selectedAssetArr[index][contributorFieldMappings[camelCaseItem]]?.id;
          } else {
            assets[index][_.camelCase(item)] =
              this.selectedAssetArr[index][_.camelCase(item)];
            }
        });
      }
      return assets;
    },
    singleAssetSeleceted() {
      return this.selectedAssetArr.length === 1;
    },
    selectAssetsCount() {
      return this.selectedAssets.length;
    },
    hasAssociatedInfo() {
      return (
        this.selectedAssetArr[0].helpTickets.length ||
        this.selectedAssetArr[0].managedBy ||
        this.selectedAssetArr[0].usedBy ||
        this.selectedAssetArr[0].universalLinksCount
      );
    },
    showSelectAllCheckbox() {
      return this.viewType === "list" && !this.mspFlag;
    },
    modalTitle() {
      return this.isDeleting ? "delete" : "update";
    },
    selectedAllAssets() {
      return this.allAssetsSelected || this.selectedAssets.length === this.totalRecord;
    },
    pageStart() {
      return Math.min(((this.pageIndex * this.pageSize) + 1), this.totalRecord);
    },
    pageEnd() {
      return Math.min(
        (this.page * this.pageSize) + this.pageSize,
        this.totalRecord
      );
    },
    visibleAssetFilters() {
      return Object.entries(this.assetFilters)
        .filter(([key, filter]) => key && Array.isArray(filter.active) && filter.active.length && !this.mspFlag)
        .map(([label, filter]) => ({ label, ...filter }));
    },
  },
  watch: {
    assetTypes() {
      const typeId = this.$route.query.type;
      if (typeId && this.assetTypes) {
        const assetTypeId = Number(typeId);
        this.setAssetTypeById(assetTypeId);
      }
    },
    locations() {
      const locationId = this.$route.query.location_id;
      if (locationId && this.locations) {
        const assetLocationId = Number(locationId);
        this.setLocationByID(assetLocationId);
      }
    },
    assetStatus() {
      this.selectedAssets = [];
    },
    assets() {
      if (this.allAssetsSelected) {
        this.assets.map((a) => {
          if (!this.selectedAssets.includes(a.id)) {
            this.selectedAssets.push(a.id);
          }
          return;
        });
      }
    },
    selectedAssetsArray() {
      if (this.assetTags) {
        const updatedTags = this.assetTags.map((tag) => ({
          ...tag,
          checked: this.selectedAssetsArray.some((asset) =>
            asset.tags?.some((assetTag) => assetTag.name === tag.name)
          ),
        }));
        this.setAssetTags(updatedTags);
      }
    },
    activeSort: {
      handler(newValue, oldValue) {
        this.setSortByOptions({
          activeSort: newValue,
          activeSortDirection: this.sortByOptions.activeSortDirection,
        });
        if (oldValue && oldValue.length) {
          this.fetchAssets();
        }
      },
    },
    activeSortDirection: {
      handler(newValue, oldValue) {
        this.setSortByOptions({
          activeSort: this.sortByOptions.activeSort,
          activeSortDirection: newValue,
        });
        if (oldValue && oldValue.length) {
          this.fetchAssets();
        }
      },
    },
    selectedAssetArr: {
      handler(newVal) {
        this.selectedAssetsArray = JSON.parse(JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeDestroy() {
    this.setAssets([]);
    this.setSortByOptions([]);
    this.setCurrentAsset(null);
    this.setWarrantyStatus(null);
    this.setAssociatedTicketsHistory([]);
    document.removeEventListener("click", this.handleOutsideClick);
    this.unbindAll();
  },
  methods: {
    ...mapMutations([
      "addTag",
      "setAssetType",
      "setCurrentAsset",
      "setAssociatedTicketsHistory",
      "setPageIndex",
      "setPageSize",
      "setSearchTerm",
      "setSearch",
      "clearAssetFilter",
      "setSortByOptions",
      "setCurrentLocation",
      "setAssetAvailableStatus",
      "setAssetStatus",
      "setPageIndex",
      "setViewType",
      "setWarrantyStatus",
      "setAssets",
      "setAssetTags",
      "setActiveTab",
      "setPeople",
    ]),
    ...mapMutations("GlobalStore", ["setSelectedCompanies"]),
    ...mapActions("GlobalStore", ["fetchLocations", "fetchUserCompanies"]),
    ...mapActions(["updateAssetListColumns"]),
    childCompanyAssetOpener(asset) {
      const url = `/user_accesses.json?company_id=${asset.companyId}&redirect_route=/managed_assets/${asset.id}`;
      http.get(url).then((res) => {
        const accessUrl = res.data.url;
        window.open(accessUrl, "_blank");
      });
    },
    toggleTag(index) {
      this.assetTags[index].checked = !this.assetTags[index].checked;
    },
    showMoreTags() {
      this.showMore = !this.showMore;
    },
    hideBulkUpdate(action) {
      if (action.name !== "Bulk update") return false;
      return this.selectedAllAssets && (this.pageSize < this.totalRecord);
    },
    filterAssetsForBulkPrinting() {
      if (this.allAssetsSelected) {
        return this.allAssetsDetails;
      }

      return this.allAssetsDetails.filter((asset) =>
        this.selectedAssets.includes(asset.id)
      );
    },
    subIsActive(bulkAction) {
      return this.activeBulkAction === bulkAction;
    },
    showBulkAction(bulkAction) {
      if (bulkAction === "assignLocations") {
        this.fetchLocations({ offset: this.offset, limit: this.perPage });
      }
      this.loading = true;
      this.activeBulkAction = bulkAction;
      this.$emit("current-filter", this.activeFilter);
    },
    async setUpFilters() {
      if (this.$route.query.warranty_filter) {
        this.filterWarranty(this.$route.query.warranty_filter);
        this.filtersUpdated();
      } else {
        this.viewTypeFromCookie();
        await this.assetPreferencesCall();
        this.checkSortCookies();
        this.fetchAssets();
      }
    },
    onWorkspaceChange() {
      this.checkPerPage("assets", this.setPageSize);
      if (this.dashboardView === "management") {
        this.loadFilters();
        this.loadFilterAndFetchData();
      }
      this.$store.commit("setAssets", []);
      this.setCurrentAsset(null);
      this.setSearchTerm(null);
      this.setSearch(null);
      this.setActiveTab(null);
      this.setPeople(false);
      this.setAssociatedTicketsHistory(null);
      if (this.$route.query.availabilityStatus) {
        this.setAvailableStatus();
      }
      if (this.$route.query.status) {
        this.setStatus();
      }
      if (this.$route.query.used_by) {
        this.$store.dispatch("usedByContributorId", this.$route.query.used_by);
      } else {
        this.$store.dispatch("usedByContributorId", null);
      }
      if (this.$route.query.managed_by) {
        this.$store.dispatch(
          "managedByContributorId",
          this.$route.query.managed_by
        );
      } else {
        this.$store.dispatch("managedByContributorId", null);
      }
      this.$store.commit("setPageIndex", 0);

      this.$store.dispatch("fetchCompanyUserOptions");
      this.$store.dispatch("fetchCompanyLogoUrl");
      this.$store.dispatch("fetchAssetPreferences");
      this.setSelectedColumnforSorting();
      this.setupPusherListeners();
      if (!this.assetAvailabilityStatus.length) {
        this.$store.dispatch("fetchCompanyAssetStatuses");
      }
      if (!this.dashboardView || this.dashboardView === "company" || this.dashboardView === "management" || this.$superAdminUser) {
        this.setUpFilters();
      }
    },
    loadFilters() {
      if (this.$route.query.warranty_filter) {
        this.filterWarranty(this.$route.query.warranty_filter);
        this.$store.commit("setPageIndex", 0);
      }
    },
    fetchAssets() {
      this.$store.dispatch("fetchAssets", {
        isBulkUpdating: false,
        mspFlag: this.mspFlag,
      });
    },
    filtersUpdated() {
      this.$store.commit("setPageIndex", 0);
      this.fetchAssets();
      if (this.isFilterRemoved) {
        this.isFilterRemoved = false;
        this.allAssetsSelected = false;
      } else {
        this.selectedAssets = [];
      }
    },
    usedByContributorIdChanged(obj) {
      this.usedByContributorId = obj.id;
      this.usedByContributor = obj;
    },
    managedByContributorIdChanged(obj) {
      this.managedByContributorId = obj.id;
      this.managedByContributor = obj;
    },
    clearFilter(filter) {
      if (Object.keys(this.$route.query).length > 0) {
        this.removeParamFromRoute(filter);
      }
      this.clearAssetFilter(filter);
      this.isFilterRemoved = true;
      this.filtersUpdated();
    },
    async setToggleView(type) {
      if (this.viewType && this.viewType !== type) {
        this.setViewType(type);
        await this.assetPreferencesCall();
        this.fetchAssets();
      }
    },
    assignLocation(location) {
      this.selectedLocation = location;
      this.locationId = location.id;
    },
    pageSelected(p) {
      this.setPageIndex(p - 1);
      this.fetchAssets();
    },
    changePageSize(e) {
      this.setPageSize(e.currentTarget.value);
      this.setPageIndex(0);
      this.setPerPageInCookie("assets", this.pageSize);
      this.fetchAssets();
    },
    showHelpTickets(asset) {
      const { port } = window.location;
      let url = `//${window.location.hostname}`;
      if (port.length > 0) {
        url = `${url}:${port}`;
      }
      url += `/help_tickets?managed_asset_id=${asset.id}`;
      window.location = url;
    },
    setAssetTypeById(id) {
      this.assetTypes.forEach((t) => {
        if (t.id === id) {
          this.setAssetType(t);
        }
      });
    },
    selectAsset(assetId) {
      this.allAssetsSelected = false;
      const idx = this.selectedAssets.indexOf(assetId);
      if (idx > -1) {
        this.selectedAssets.splice(idx, 1);
      } else {
        this.selectedAssets.push(assetId);
      }
    },
    openArchiveModal() {
      this.selectingTagsToApply = false;
      this.selectingLocationToApply = false;
      this.selectingUsersToAssign = false;
      this.setCurrentAsset(null);

      if (this.singleAssetSeleceted && this.hasAssociatedInfo) {
        this.$refs.assetArchive.openAssociatedModal(this.selectedAssetArr[0]);
      } else if (this.singleAssetSeleceted) {
        this.$refs.assetArchive.openSingleAssetArchiveModal();
      } else {
        this.$refs.assetArchive.openMultipleAssetsArchiveModal();
      }
    },
    openUnArchiveModal() {
      this.selectingTagsToApply = false;
      this.selectingLocationToApply = false;
      this.selectingUsersToAssign = false;
      this.$refs.unArchiveAssetModal.open();
    },
    openPrintQrModal() {
      this.printQRRequest = true;
      this.qrAssets = this.filterAssetsForBulkPrinting();
      setTimeout(() => {
        this.$refs.printQRCodeModal.open();
      }, 200);
      this.processQRCode();
    },
    openDeleteModal() {
      this.selectingTagsToApply = false;
      this.selectingLocationToApply = false;
      this.selectingUsersToAssign = false;
      this.$refs.deleteAssetModal.open();
    },
    openBulkUpdateModal() {
      this.$store.dispatch('fetchCompanyUserAndGroupsOptions');
      this.selectingTagsToApply = false;
      this.selectingLocationToApply = false;
      this.selectingUsersToAssign = false;
      this.$refs.bulkUpdateModal.open();
      this.fetchAssetsData();
      this.$store.dispatch('fetchAssetTypes');
    },
    openMergeAssetsModal() {
      this.isMergeModalOpen = true;
      this.$refs.mergeAssetsModal.open();
    },
    closeMergeAssetsModal() {
      this.isMergeModalOpen = false;
      this.$refs.mergeAssetsModal.close();
    },
    clearAssetDeletion() {
      this.selectedAssets = [];
      this.$refs.deleteAssetModal.close();
    },
    clearbulkAssetModal() {
      this.unSelectAssets();
      if (this.$refs.bulkAssetUpdateModal) {
        this.isDeleting = false;
        this.$refs.bulkAssetUpdateModal.close();
      }
    },
    openBulkAssetsUpdateModal() {
      this.$refs.bulkAssetUpdateModal.open();
    },
    clearAssetArchiving() {
      this.selectedAssets = [];
    },
    clearAssetLocation() {
      this.selectingLocationToApply = false;
      this.selectedAssets = [];
    },
    clearAssetUsers() {
      this.selectingUsersToAssign = false;
      this.selectedAssets = [];
    },
    clearAssetUnArchiving() {
      this.selectedAssets = [];
      this.$refs.unArchiveAssetModal.close();
    },
    closeUpdateAssets() {
      this.selectedAssets = [];
      this.bulkUpdateErros = [];
      this.isDataFetched = false;
    },
    closeBulkAssets() {
      this.$refs.bulkUpdateModal.close();
    },
    applyNewLocationsSubmit() {
      if (this.selectedAssets.length) {
        this.saveButtonClicked = true;
        http
          .post(`/bulk_managed_assets/bulk_add_locations.json`, {
            assetIds: this.selectedAssets,
            locationId: this.locationId,
            allSelected: this.allAssetsSelected,
            statusFilter: this.statusFilter,
          })
          .then((res) => {
            this.fetchAssets();
            this.clearAssetLocation();
            this.saveButtonClicked = false;
            if (res.data.bulkUpdate) {
              this.$refs.bulkAssetUpdateModal.open();
            }
          })
          .catch(() => {
            this.saveButtonClicked = false;
            this.emitError(
              `Sorry, there was an error updating locations to these assets`
            );
          });
      } else {
        this.clearAssetLocation();
      }
    },
    assignUsersSubmit() {
      if (this.selectedAssets.length) {
        http
          .post(`/bulk_managed_assets/bulk_assign_users.json`, {
            assetIds: this.selectedAssets,
            usedByContributorId: this.usedByContributorId,
            managedByContributorId: this.managedByContributorId,
            allSelected: this.allAssetsSelected,
            statusFilter: this.statusFilter,
          })
          .then((res) => {
            this.fetchAssets();
            this.clearAssetUsers();
            if (res.data.bulkUpdate) {
              this.$refs.bulkAssetUpdateModal.open();
            }
          })
          .catch(() => {
            this.emitError(
              `Sorry, there was an error assigning users to these assets`
            );
          });
      } else {
        this.clearAssetUsers();
      }
    },
    unArchiveAssets() {
      if (this.selectedAssets.length) {
        http
          .post(`/bulk_managed_assets/bulk_unarchive.json`, {
            assetIds: this.selectedAssets,
            allSelected: this.allAssetsSelected,
            statusFilter: this.statusFilter,
          })
          .then((res) => {
            this.fetchAssets();
            this.clearAssetUnArchiving();
            if (res.data.bulkUpdate) {
              this.$refs.bulkAssetUpdateModal.open();
            }
          })
          .catch(() => {
            this.emitError(
              `Sorry, there was an error unarchiving these assets`
            );
          });
      } else {
        this.clearAssetArchiving();
      }
    },
    deleteAssets() {
      if (this.selectedAssets.length) {
        this.$refs.deleteAssetModal.close();
        this.$store.commit("setAssetsLoading", true);
        http
          .post(`/bulk_managed_assets/bulk_delete.json`, {
            assetIds: this.selectedAssets,
            allSelected: this.allAssetsSelected,
            statusFilter: this.statusFilter,
          })
          .then((res) => {
            if (res.data.bulkUpdate) {
              this.isDeleting = true;
              this.openBulkAssetsUpdateModal();
            }
            this.clearAssetDeletion();
            this.emitSuccess("Your assets have been deleted successfully");
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error deleting these assets`);
          })
          .finally(() => this.$store.commit("setAssetsLoading", false));
      } else {
        this.clearAssetDeletion();
      }
    },
    applyNewTags(assetId) {
      const idx = this.selectedAssets.indexOf(assetId);
      if (idx > -1) {
        this.selectedAssets.splice(idx, 1);
      } else {
        this.selectedAssets.push(assetId);
      }
    },
    applyTags() {
      this.selectingTagsToApply = true;
      this.selectingLocationToApply = false;
      this.selectingUsersToAssign = false;
    },
    applyLocations() {
      this.selectingTagsToApply = false;
      this.selectingLocationToApply = true;
      this.selectingUsersToAssign = false;
    },
    assignUsers() {
      this.selectingUsersToAssign = true;
      this.selectingTagsToApply = false;
      this.selectingLocationToApply = false;
    },
    applyNewTagsSubmit() {
      const tagsArr = this.assetTags
        .filter((tag) => tag.checked)
        .map(({ name }) => name);
      const removedTags = this.assetTags
        .filter((tag) => !tag.checked)
        .map(({ name }) => name);
      const params = {
        assetIds: this.selectedAssets,
        tags: tagsArr,
        removedTags,
        allSelected: this.allAssetsSelected,
        statusFilter: this.statusFilter,
      };
      if (
        this.selectedAssets.length &&
        (tagsArr.length || removedTags.length)
      ) {
        http
          .post(`/managed_asset_tags/add_tags.json`, params)
          .then((res) => {
            this.fetchAssets();
            this.clearAssetTagging();
            if (res.data.bulkUpdate) {
              this.$refs.bulkAssetUpdateModal.open();
            }
          })
          .catch((error) => {
            this.emitError(
              `Sorry, there was an error for these tags (${error.response.data.message}).`
            );
          });
      } else {
        this.clearAssetTagging();
      }
    },
    clearAssetTagging() {
      this.selectingTagsToApply = false;
      this.selectedAssets = [];
      this.tagsToApplyArr = [];
    },
    addNewTag(newTag) {
      const tag = newTag.trim().toLowerCase();
      if (!tag.length) {
        return;
      }
      const existingTag = this.assetTags.find((t) => t.name === tag);
      if (existingTag) {
        this.emitError(`Tag already exists`);
        return;
      }
      this.newTagInput = "";
      this.addTag({ id: tag, name: tag, checked: true });
    },
    removeTag(tag) {
      if (!this.isWrite) {
        this.emitError(`Sorry, you do not have permissions to remove tags.`);
        return;
      }
      http
        .delete(`/managed_asset_tags/${tag.id}.json?assets_ids=${this.selectedAssets}`)
        .then(() => {
          this.$store.dispatch("fetchCompanyAssetTags");
          this.fetchAssets();
          this.emitSuccess("Tags removed successfully");
        })
        .catch((error) => {
          this.emitError(
            `Sorry, there was an error deleting this tag (${error.response.data.message}).`
          );
        });
    },
    selectAllAssets() {
      this.selectedAssets = this.assets.map(({ id }) => id);
    },
    selectAllAssetsRecord() {
      this.selectedAssets = this.assets.map(({ id }) => id);
      this.allAssetsSelected = true;
      this.findStatusFilter();
    },
    findStatusFilter() {
      this.statusFilter = {};
      this.activeFilters.forEach((filter) => {
        if (Array.isArray(filter.filter)) {
          this.statusFilter[filter.filterName] = filter.filter.map(item => item.id);
        } else {
          this.statusFilter[filter.filterName] = filter.filter.id;
        }
      });
      if (this.search) {
        this.statusFilter.search = this.search;
      }
    },
    unSelectAssets() {
      this.allAssetsSelected = false;
      this.selectedAssets = [];
    },
    selectAllCheckboxClicked(selectAllValue) {
      selectAllValue ? this.selectAllAssets() : this.unSelectAssets();
    },
    setSortCookies() {
      this.setActiveSortInCookie(
        "assets",
        this.activeSort,
        this.activeSortDirection
      );
    },
    toggleFilterMenu() {
      this.hideAllOtherMenus();
      this.isFilterMenuOpen = !this.isFilterMenuOpen;
      if (this.isFilterMenuOpen) {
        document.addEventListener("click", this.handleOutsideClick);
      } else {
        document.removeEventListener("click", this.handleOutsideClick);
      }
      this.$refs.dismissibleContainer.toggleOpen();
    },
    handleOutsideClick(e) {
      const ref = this.$refs.dismissibleContainer;
      if (ref) {
        const container = ref.$el;
        if (!container.contains(e.target) || e.target.className === 'dropdown d-inline-block') {
          this.closeFilterMenu();
        }
      }
    },
    closeFilterMenu() {
      this.isFilterMenuOpen = false;
      document.removeEventListener("click", this.handleOutsideClick);
      this.$refs.dismissibleContainer.forceClose();
    },
    setLocationByID(id) {
      const location = this.locations.find((loc) => loc.id === id);
      if (location) {
        this.setCurrentLocation(location);
      }
    },
    setAvailStatusByID(id) {
      const availableStatus = this.assetAvailabilityStatus.find(
        (availStatus) => availStatus.id === id
      );
      if (availableStatus) {
        this.setAssetAvailableStatus(availableStatus);
      }
    },
    setStatusByID(id) {
      if (id === "all") {
        this.setAssetStatus(null);
      } else {
        const status = this.assetStatuses.find((stat) => stat.id === id);
        if (status) {
          this.setAssetStatus(status);
        }
      }
    },
    qrAssetDetails(asset) {
      const qrCodeDetails = [];
      if (this.qrCodeSettings.printAssetTag) {
        qrCodeDetails.push(this.checkAssetId(asset));
      }
      if (this.qrCodeSettings.printCompanyName) {
        qrCodeDetails.push(
          this.qrCodeSettings.labelText
            ? this.qrCodeSettings.labelText
            : this.$companyName
        );
      }
      if (this.qrCodeSettings.printAssetName) {
        qrCodeDetails.push(asset.name);
      }
      return qrCodeDetails;
    },
    checkAssetId(asset) {
      if (asset.tag && asset.tag.trim()) {
        return asset.tag.toString();
      }
      return asset.id.toString();
    },
    removeParamFromRoute(filter) {
      const query = { ...this.$route.query };
      if (filter.filterName === "currentLocation") delete query.location_id;
      if (filter.filterName === "assetType") delete query.type;
      if (filter.filterName === "warrantyStatus") delete query.warranty_filter;
      this.$router.replace({ query });
    },
    setSelectedColumnforSorting() {
      this.activeSort = this.sortByOptions.activeSort;
      this.activeSortDirection = this.sortByOptions.activeSortDirection;
    },
    generateQRCodes() {
      this.$store.dispatch("fetchAssetPreferences");
      this.fetchAllQRAssetsDetails();
      this.readyToGenerateQR = true;
    },
    okUpdateAssets() {
      this.$refs.bulk_update.validateAndCheck().then((result) => {
        if (result) {
          http
            .post("/bulk_managed_assets/bulk_update", {
              assets: this.filteredAssets,
              usedByContributorId: this.usedByContributorId,
              managedByContributorId: this.managedByContributorId,
              allSelected: this.allAssetsSelected,
              statusFilter: this.statusFilter,
            })
            .then((res) => {
              this.fetchAssets();
              this.closeBulkAssets();
              this.isDataFetched = false;
              if (res.data.bulkUpdate) {
                this.$refs.bulkAssetUpdateModal.open();
              }
            })
            .catch((err) => {
              this.bulkUpdateErros = err.response.data.message;
              this.emitError(
                `Sorry, there was an error updating these assets.`
              );
            });
        } else {
          this.emitError(
            `Please correct the highlighted errors before submitting.`
          );
        }
      });
    },
    stickyColumns() {
      const stickColyArray = this.tableHeader.reduce(
        (acc, col, index) =>
          col.title === "Type" || col.title === "Asset Name"
            ? acc.concat(index)
            : acc,
        []
      );
      return stickColyArray;
    },
    setAvailableStatus() {
      const availStatus = this.$route.query.availabilityStatus;
      if (availStatus && this.assetAvailabilityStatus) {
        this.setAvailStatusByID(availStatus);
      }
    },
    setStatus() {
      const { status } = this.$route.query;
      if (status && this.assetStatus) {
        this.setStatusByID(status);
      }
    },
    generateUrl(assetId) {
      return `${window.location.origin}/managed_assets/${assetId}`;
    },
    processQRCode() {
      this.readyToGenerateQR = true;
      this.readyToDownloadQR = true;
      setTimeout(() => {
        this.printBulkQRCodes();
        this.$refs.printQRCodeModal.qrLoader = false;
      }, 500);
    },
    printBulkQRCodes() {
      printJS({
        printable: this.$refs.qrPage,
        type: "html",
        showModal: true,
        modalMessage: "Printing QR Codes...",
        maxWidth: "100%",
        style: `@page {
          size: ${this.qrCodeSettings.pageSize} ${this.qrCodeSettings.pageOrientation};
          margin-left: ${this.qrCodeSettings.pageMarginLeft}in;
          margin-top: ${this.qrCodeSettings.pageMarginTop}in;
        }`,
        documentTitle: this.$companyName,
        targetStyles: ["*"],
        font_size: "",
      });
      this.readyToGenerateQR = false;
      this.readyToDownloadQR = false;
      this.$refs.printQRCodeModal.close();
    },
    fetchAllQRAssetsDetails() {
      this.findStatusFilter();
      const params = { status_filter: this.statusFilter };
      http
        .get("/qr_codes.json", { params })
        .then((res) => {
          this.allAssetsDetails = res.data.assetDetails;
          this.qrAssets = this.filterAssetsForBulkPrinting();
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error fetching all assets ids.`);
        });
    },
    setupPusherListeners() {
      if (this.$pusher) {
        const channel = this.$pusher.subscribe(
          `${this.$currentCompanyId}_managed_assets`
        );
        channel.bind("successful-assets-update", (data) => {
          this.clearbulkAssetModal();
          this.pageSelected(1);
          this.emitSuccess(
            `Your assets have been ${data.action}d successfully`
          );
        });
        channel.bind("unsuccessful-assets-update", (data) => {
          this.clearbulkAssetModal();
          this.pageSelected(1);
          this.emitError(
            `Sorry, there was an error ${data.action.slice(0, -1)}ing assets.`
          );
        });
      }
    },
    fetchAssetsData() {
      this.$store.dispatch("fetchAssets", true).then(() => {
        const filteredArray = this.assets.filter((newAssetData) =>
          this.selectedAssetsArray.some(
            (oldAssetData) => oldAssetData.id === newAssetData.id
          )
        );
        this.selectedAssetsArray = filteredArray;
        this.isDataFetched = true;
      });
    },
    fetchMspData() {
      this.fetchAssets();
    },
    checkSortCookies() {
      if (this.sortPresentInCookie()) {
        const cookieSortItem = this.getCookieValue('assets-sort-item');
        const cookieSortDirection = this.getCookieValue('assets-sort-direction');
        const preferences = this.assetPreferences.map((n) => n.name);

        if (cookieSortItem && cookieSortDirection && preferences.includes(cookieSortItem)) {
          this.setSortByOptions({
            activeSort: cookieSortItem,
            activeSortDirection: cookieSortDirection,
          });
          this.setSelectedColumnforSorting();
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
  [type="checkbox"] {
    display: none;
  }

  :checked ~ .tag-option {
    background-color: color.adjust($teal, $lightness: -15%);
    border-color: color.adjust($teal, $lightness: -15%);
  }

  .select-all {
    cursor: pointer;
  }
  .bulk-modal :deep(.sweet-modal) {
    max-width: 80.375rem;
    top: 2rem;

    .sweet-modal-overlay {
      height: 100%;
      width: 100%;
    }

    .sweet-box-actions {
      right: 1.5rem;
      top: 1.875rem;
    }

    .sweet-content {
      padding: 2rem;

      .bulk-update-header {
        .modal-detail {
          font-size: 0.875rem;
        }
      }
    }
  }

  :deep(.merge-asset-modal .sweet-modal) {
    .sweet-content-content {
      width: 100%;
    }
  }

  .asset-bulk-edit-wrapper {
    border-top-left-radius: 0 !important;

    .nulodgicon-android-close {
      font-size: 22px;

      &:before {
        color: $themed-muted;
      }
    }
  }

  .asset-heading--bulk-selecting {
    z-index: map-get($zIndex, "above");
  }

  .badge {
    width: 1.0625rem;
    height: 1.0625rem;
  }

  .btn-view-more {
    z-index: 9;
    top: 0.313rem;
    min-width: 7.375rem;

    &:hover {
      background-color: #dae0e5;
      border: 1px solid rgba(33, 37, 41, 0.1);
    }
  }

  .asset-search-bar {
    width: 42rem;

    @media (max-width: 1390px) {
      width: 40rem;
    }
    @media (max-width: 1185px) {
      width: 25rem;
    }
    @media (max-width: 508px) {
      width: 20rem;
    }
  }

  @media (max-width: 940px) {
    .pagination-wrapper {
      width: 100%;
      justify-content: flex-end;
    }
  }
</style>
