<template>
  <div v-if="asset">
    <div
      v-if="showSmartAlert"
      class="px-0 mb-4"
      data-tc-section="smart alerts"
    >
      <h6 class="mb-3">Smart Alerts</h6>

      <div class="alert position-relative row mx-0 p-0 d-flex justify-content-between m-0">
        <div
          class="box box--flat bg-lighter alert-basic-light col-7"
          data-tc-box="smart alerts"
        >
          <div
            v-if="!diskSmartAlertEnabled"
            class="box--disabled-overlay t-0"
          />
          <div class="box__inner align-self-start d-flex flex-column h-100">
            <div
              class="position-absolute right-0 t-0 mt-2"
              style="z-index: 10;"
            >
              <material-toggle
                v-tooltip="{ content: tooltipContent }"
                :init-active="diskSmartAlertEnabled"
                @toggle-sample="toggleDiskSmartAlert"
              />
            </div>

            <div class="row h-100 mr-0 ml-n2">
              <div class="col-auto text-secondary mt-2 pl-0">
                <span class="d-inline-flex align-items-center text-center btn-text text-secondary">
                  <span class="btn-icon-circle btn-icon-circle icon--line-height alert-badge">
                    <img
                      class="align-middle pb-1"
                      width="22px"
                      src="https://nulodgic-static-assets.s3.amazonaws.com/images/assets/server.svg"
                    >
                    <img
                      class="tips-icon secondary-icon mb-1"
                      src="https://nulodgic-static-assets.s3.amazonaws.com/images/tips_lightbulb.svg"
                    >
                  </span>
                </span>
              </div>

              <div class="col d-flex flex-column">
                <div class="row flex-column mx-0">
                  <span class="not-as-small d-inline-block align-bottom">
                    <span class="font-weight-semi-bold text-secondary"> Disk Space </span>
                  </span>
                  <div
                    class="small mb-0 mt-2"
                  >
                    <div class="mt-1">Alert when disk space is
                      <span>
                        <input
                          id="threshold-input"
                          v-model="smartAlertThreshold"
                          class="w-25 mx-1 form-control d-inline inline-input"
                          type="number"
                          min="0"
                          max="100"
                        >
                      </span>
                      % left
                    </div>
                    <div class="mt-1">Current disk usage is {{ diskUsagePercentage }}</div>
                    <div
                      class="d-flex align-items-center assignee-dropdown assignee-dropdown__smart-alert position-absolute b-0 right-0"
                      @click="toggleDiskRecipientsDropdown"
                    >
                      <div>
                        <div v-if="shouldShowDiskSmartDropdown">
                          Recipients
                        </div>
                        <RecipientsAvatar
                          v-else
                          class="w-75 clickable"
                          :class="{ 'dropdown-toggle align-items-center': editingDiskSmartAlert }"
                          :assignees="smartAlertAssignees"
                        />
                      </div>
                      <div v-if="shouldShowDiskSmartDropdown">
                        <basic-dropdown
                          ref="dropdown"
                          class="dropdown-menu not-as-small helpdesk-ticket-dropdown dropdown-filter inline-assign-staff-dropdown"
                          dropdown-height="22rem"
                          dynamic-content
                          :show-dropdown="showDiskSmartDropdown"
                          @on-blur="toggleDiskRecipientsDropdown"
                        >
                          <contributors-select
                            ref="contributorSelect"
                            class="users-select"
                            show-selected-as-group
                            compact
                            :excludes="excludeIds"
                            :is-list-view="true"
                            @select="onSelectRecipient"
                            @remove="onRemoveRecipients"
                          />
                        </basic-dropdown>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="px-0 mb-4 mt-4"
      data-tc-section="calendar alerts"
    >
      <h6>Calendar Alerts</h6>
      <div
        ref="scrollableList"
        class="scrollable-list"
      >
        <div
          v-for="(alert, index) in asset.alertDates"
          :key="`alert-${index}`"
          class="position-relative"
          :class="{ 'mt-2': index > 0 }"
        >
          <div v-if="!alert._destroy">
            <alert-date-item
              :id="`alert_date_${index}`"
              :key="dateKey"
              :asset-alert="alert"
              :new-alert="newAlert"
              :is-active-item="isActiveItem(index)"
              @item-focused="setActiveElement(index)"
              @change-alert-date="changeAlertDate(index, $event)"
              @alert-notes-input="updateNotes(index, $event)"
              @add-recipient="onAddRecipient($event, index)"
              @remove-recipient=" onRemoveRecipient($event, index)"
              @remove-alert="removeAlert(alert, index)"
              @save-new-alert="saveAlerts"
              @create-new-alert="newAlerts"
              @cancel-new-alert="newAlert = false"
            />
            <hr class="my-2">
          </div>
        </div>
      </div>
      <div
        v-if="asset.alertDates"
        class="d-flex align-items-center mt-3 clickable mb-5"
        @click="addAlert"
      >
        <span
          class="btn btn-primary btn-sm text-white rounded-circle mr-2 btn-flat"
          role="button"
          data-tc-btn="calendar alerts"
        >
          <i class="nulodgicon-plus-round" />
        </span>
        <input
          class="form-control clickable"
          type="text"
          placeholder="Add Alert"
          data-tc-field="add alert"
        >
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import _cloneDeep from 'lodash/cloneDeep';
  import dates from 'mixins/dates';
  import assetAlertUpdateHelper from 'mixins/assets/asset_alert_update_helper';
  import MaterialToggle from "components/shared/material_toggle.vue";
  import ContributorsSelect from "components/shared/contributors_select.vue";
  import RecipientsAvatar from 'components/help_tickets/reports/recipients_avatar.vue';
  import BasicDropdown from 'components/shared/basic_dropdown.vue';
  import AlertDateItem from "./alert_dates_item.vue";

  export default {
    components: {
      MaterialToggle,
      AlertDateItem,
      RecipientsAvatar,
      ContributorsSelect,
      BasicDropdown,
    },
    mixins: [dates, assetAlertUpdateHelper],
    props: ['managedAsset'],
    data() {
      const managedAsset = _cloneDeep(this.managedAsset);
      const assetSmartAlert = managedAsset.smartAlert && managedAsset.smartAlert.length > 0 ? managedAsset.smartAlert[0] : null;
      return {
        asset: managedAsset,
        smartAlert: assetSmartAlert,
        dateKey: false,
        defaultRecipients: [],
        recipients: [],
        editingDiskSmartAlert: true,
        showDiskSmartDropdown: false,
        newAlert: false,
        activeItem: -1,
        alertRecipients: [],
        isNewAlertItem: false,
        smartAlertThreshold: parseFloat(assetSmartAlert?.threshold),
        originalSmartAlertThreshold: parseFloat(assetSmartAlert?.threshold),
      };
    },
    computed: {
      excludeIds() {
        return this.smartAlert?.recipients?.map(assigneeId => parseInt(assigneeId, 10));
      },
      alertLabel() {
        return this.asset?.alertDates?.length ? 'Add another alert' : 'Add alert';
      },
      diskSmartAlertEnabled() {
        return this.smartAlert?.active;
      },
      smartAlertAssignees() {
        return this.smartAlert?.recipients;
      },
      showSmartAlert() {
        return !(this.$route.path.includes("edit") || this.$route.path.includes("new"));
      },
      shouldShowDiskSmartDropdown() {
        return this.editingDiskSmartAlert && this.showDiskSmartDropdown;
      },
      tooltipContent() {
        return this.diskSmartAlertEnabled ? 'Deactivate the disk space alert' : 'Activate the disk space alert';
      },
    },
    watch: {
      managedAsset() {
        this.asset = _cloneDeep(this.managedAsset);
        if (this.asset.alertDates) {
          this.setUTCAlertDates();
        }
        this.setDefaultRecipients();
      },
      smartAlertThreshold(newValue) {
        this.updateSmartAlertThreshold(parseFloat(newValue));
      },
      smartAlert: {
        handler(newVal) {
          this.smartAlertThreshold = parseFloat(newVal?.threshold);
        },
        deep: true,
      },
    },
    mounted() {
      this.fetchAlertRecipients();
    },
    methods: {
      onWorkspaceChange() {
        this.fetchAlertRecipients();
      },
      updateSmartAlertThreshold(value) {
        this.smartAlert.threshold = value;
      },
      fetchAlertRecipients() {
        this.setDefaultRecipients();
        if (this.defaultRecipients) {
          const params = { recipient_ids: this.defaultRecipients };
          http
            .get("/managed_asset_alert_dates/get_recipients", { params })
            .then((res) => {

              this.recipients = res.data;
            }).catch(() => {
              this.emitError("Sorry, there was an error loading alert recipients.");
            });
        }
      },
      onSelectRecipient(contributor) {
        this.smartAlert.recipients.push(contributor.id);
        this.$emit('add-recipient', contributor.id);
        this.showDiskSmartDropdown = false;
      },
      onRemoveRecipients(contributor) {
        const index = this.smartAlert.recipients.findIndex(res => res === contributor.id);
        this.$emit('remove-recipient', contributor.id);
        if (index > -1 ) {
          this.smartAlert.recipients.splice(index, 1);
        }
        this.showDiskSmartDropdown = false;
      },
      changeAlertDate(index, value) {
        if (this.asset) {
          if (value && this.toISOstring(value).toDate() > moment().toDate()) {
            this.asset.alertDates[index].date = value;
          } else {
            this.emitError("Alert dates cannot be in the past.");
          }
          this.dateKey = !this.dateKey;
        }
      },
      removeAlert(alert, index) {
        const indexPoint = this.asset.alertDates.findIndex(alertDate => alertDate.id === alert.id);
        if (alert.id) {
          Vue.set(this.asset.alertDates, indexPoint, { id: alert.id, date: alert.date, notes: alert.notes, recipients: alert.recipients, _destroy: true });
        } else {
          this.asset.alertDates.splice(index, 1);
        }
        if (this.$route.path.includes("edit")) {
          this.saveAlerts();
        }
      },
      addAlert() {
        if (!this.invalidAlerts) {
          this.setDefaultRecipients();
          const recipient = this.defaultRecipients.filter(item => item !== null);
          this.asset.alertDates.push({
            date: '',
            notes: '',
            recipients: recipient,
          });
          this.newAlert = true;
          this.activeItem = this.asset.alertDates.length - 1;
          this.$nextTick(() => {
            this.scrollToBottom();
          });
        }
      },
      setUTCAlertDates() {
        this.asset.alertDates = this.asset.alertDates.map(alertDate => {
          const newAlertObject = { ...alertDate };
          if (alertDate.date) {
            newAlertObject.date = moment.utc(alertDate.date).format("MMM DD[,] YYYY");
          }
          return newAlertObject;
        });
      },
      updateNotes(index, notes) {
        this.asset.alertDates[index].notes = notes;
      },
      onAddRecipient(contributorId, index) {
        this.asset.alertDates[index].recipients.push(contributorId);
      },
      onRemoveRecipient(contributorId, index) {
        const idx = this.asset.alertDates[index].recipients.findIndex(res => res === contributorId);
        if (idx > -1) {
          this.asset.alertDates[index].recipients.splice(idx, 1);
        }
      },
      setDefaultRecipients() {
        const assignmentInfo = this.asset.assignmentInformationAttributes;
        this.defaultRecipients = [this.asset.creatorId, assignmentInfo?.managedByContributorId, assignmentInfo?.usedByContributorId];
      },
      saveAlerts() {
        this.smartAlertCheck();
        this.saveAlertDates();
      },
      toggleDiskSmartAlert() {
        this.smartAlert.active = !this.smartAlert.active;
      },
      smartAlertCheck() {
        const inputValue = Number(document.getElementById('threshold-input').value);
        if (Number.isNaN(inputValue) || inputValue < 0 || inputValue > 100) {
          this.smartAlertThreshold = this.originalSmartAlertThreshold;
          this.emitError('The threshold should be a number between 0 and 100.');
        } else {
          this.smartAlert.threshold = inputValue;
          http
            .put(`/managed_asset_alert_dates/${this.asset.id}/update_smart_alert.json`, { smart_alert: this.smartAlert })
            .catch(error => {
              this.emitError('Error updating smart alert.', error);
            });
        }
      },
      focusTeammateSelectInput() {
        if (this.$refs.contributorSelect) {
          this.$refs.contributorSelect.$el.querySelector('input').focus();
        }
      },
      toggleDiskRecipientsDropdown() {
        if (this.smartAlert.active) {
          this.showDiskSmartDropdown = !this.showDiskSmartDropdown;
          if (this.showDiskSmartDropdown) {
            setTimeout(() => {
              this.focusTeammateSelectInput();
            });
          }
        }
      },
      scrollToBottom() {
        if (this.$refs.scrollableList) {
          this.$refs.scrollableList.scrollTop = this.$refs.scrollableList.scrollHeight;
        }
      },
      isActiveItem(idx) {
        return this.activeItem === idx;
      },
      setActiveElement(idx) {
        this.activeItem = idx;
      },
      newAlerts() {
        this.$emit("asset-alerts", this.asset);
      },
    },
  };
</script>

<style lang="scss" scoped>
/* styles.css */
.tips-icon {
  border: 1px solid #eaeaea;
  position: absolute;
  background-color: rgba(240, 240, 240, 0.85);
  border-radius: 100%;
  padding: 0.1875rem;
  right: 0.625rem;
  top: -0.375rem;
  width: 1.25rem;
}

.alert-basic-light {
  border: 1px solid $themed-light !important;

  .alert-badge {
    background: $color-safe;
  }
}

.assignee-dropdown__smart-alert :deep(.avatar-holder) {
  margin-right: .25rem;
}

$list-top-offset-rem: 13;
$list-bottom-offset-rem: 9;

.scrollable-list {
  margin-left: -1.5rem;
  margin-right: -1.75rem;
  max-height: calc(100vh - #{$list-top-offset-rem + $list-bottom-offset-rem}rem);
  padding-left: 1.5rem;
  padding-right: 1.5rem;

  &::-webkit-scrollbar {
    transition: opacity 0s linear;
    width: 0.5rem;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: $themed-very-muted;
    border-radius: 0.25rem;
  }
}

.inline-input {
  height: 1.5rem;
}
</style>
