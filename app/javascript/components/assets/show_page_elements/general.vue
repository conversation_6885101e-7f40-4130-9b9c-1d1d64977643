<template>
  <div v-if="managedAsset">
    <div class="row">
      <div
        id="location-usage"
        class="col-12 scroll-elem mb-5 pt-1"
      >
        <usage
          :managed-asset="managedAsset"
          :merged-asset="mergedAsset"
          @open-usage-modal="openLocationAndUsageModal"
        />
      </div>
      <div
        v-if="showSystemDetails"
        id="asset-summary"
        class="col-12 scroll-elem"
      >
        <h5 class="font-weight-normal mb-3 d-inline-block">
          Summary
        </h5>
        <div class="box p-3 box--with-title bg-themed-box-bg d-block">
          <system-insights :hardware-detail="hardwareDetail" />
          <tech-spec 
            v-if="!isMerakiDevice"
            :show-summary="true" 
          />
        </div>
      </div>

      <div
        id="asset-hardware"
        class="col-12 scroll-elem"
        :class="{ 'pt-5': showSystemDetails}"
      >
        <system-details v-if="showSystemDetails" />
        <hardware-details v-else />
      </div>
      <div
        id="asset-software"
        class="col-12 pt-5 scroll-elem"
      >
        <software-details />
      </div>
      <div
        id="cost-depreciation"
        class="w-100 pt-5 scroll-elem"
        data-tc-box="cost depreciation"
      >
        <div class="col-12">
          <h5 class="font-weight-normal mb-3 d-inline-block">
            Cost & Depreciation
          </h5>
          <a
            v-if="isWrite && !mergedAsset"
            href="#"
            class="float-right not-as-small btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm"
            data-tc-icon="edit cost depreciation"
            @click.stop.prevent="openCostDepreciationModal = true"
          >
            <i class="nulodgicon-edit text-secondary" />
          </a>
          <cost
            :open-cost-depreciation-modal="openCostDepreciationModal"
            :managed-asset="managedAsset"
            :disabled="disabled"
            @submit="saveAsset"
            @close-modal="closeCostDepreciationModal"
            @open-cost-modal="openCostDepreciationModal = true"
          />
        </div>
      </div>
      <div
        id="warranty-acquisition"
        class="w-100 pt-5"
      >
        <div class="col-12">
          <h5 class="font-weight-normal mb-3 d-inline-block scroll-elem">
            Warranty & Acquisition
          </h5>
          <a
            v-if="isWrite && !mergedAsset"
            href="#"
            class="float-right not-as-small btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm"
            @click.stop.prevent="$refs.warrantyAndAcquisitionModal.open()"
          >
            <i class="nulodgicon-edit text-secondary" />
          </a>
          <div class="box p-3 box--with-title">
            <div class="box__inner">
              <div
                v-if="managedAsset.warrantyExpiration"
                class="row"
              >
                <div class="col-md-12">
                  <div class="float-left text-center">
                    <p class="text-muted small mb-0">
                      Acquisition Date
                    </p>
                    <h6> {{ acquisitionDate }} </h6>
                  </div>
                  <div class="float-right text-center">
                    <p class="text-muted small mb-0">
                      Warranty Expiration
                    </p>
                    <h6>{{ endDate }}</h6>
                  </div>
                </div>
                <div class="col-md-12">
                  <warranty-bar :asset="currentAsset" />
                </div>
              </div>
              <p
                v-else
                class="text-center font-weight-normal not-as-small"
              >
                Warranty date not set.
                <span v-if="isWrite && !mergedAsset">
                  You can set it
                  <a
                    href="#"
                    @click.stop.prevent="$refs.warrantyAndAcquisitionModal.open()"
                  >
                    here
                  </a> or you can
                  <a
                    href="#"
                    @click.stop.prevent="$refs.warrantyAndAcquisitionModal.open()"
                  >
                    enter the machine serial number
                  </a>
                  and let us find the warranty expiration for you!
                </span>
              </p>
              <div
                v-if="currentAsset.installDate"
                class="font-weight-normal mt-2 mb-0 not-as-small text-center"
              >
                Installed on:
                <p class="my-0 d-inline">
                  {{ showDate(currentAsset.installDate) }}
                </p>
              </div>
              <div
                v-else-if="isWrite && !mergedAsset"
                class="font-weight-normal mt-2 mb-0 not-as-small text-center"
              >
                Installed on:
                <p class="d-inline text-muted">
                  Not set,
                  <a
                    href="#"
                    data-tc-link-btn="warranty & acquisition"
                    @click.stop.prevent="$refs.warrantyAndAcquisitionModal.open()"
                  >
                    set it here.
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        id="asset-related-items"
        class="col-12 pt-5 scroll-elem"
      >
        <display-universal-links
          v-if="!mergedAsset"
          :key="linkRefreshKey"
          :entity-type="'ManagedAsset'"
          :entity="currentAsset"
          @updateRelatedItems="updateRelatedItems"
        />

        <div
          v-if="$superAdminUser && currentAsset.discoveredAssetId"
          class="mt-3 not-as-small"
        >
          <a
            :href="`/managed_assets/discovered_assets/${currentAsset.discoveredAssetId}`"
            target="_blank"
          >
            View Discovered Asset
            <i class="nulodgicon-external-link ml-1" />
          </a>
        </div>
      </div>
      <div
        v-if="showCloudAssetAttrs"
        id="cloud-information"
        class="col-12 pt-5 scroll-elem"
      >
        <cloud-assets-attributes :is-managed-asset="true" />
      </div>
      <div
        v-if="showDetails"
        id="asset-details"
        class="col-12 pt-5 scroll-elem"
      >
        <managed-asset-details />
      </div>
      <div
        v-else
        id="asset-details"
        class="col-12 pt-5 scroll-elem"
      >
        <asset-details />
      </div>
      <div
        id="asset-notes"
        class="col-12 pt-5 scroll-elem"
      >
        <notes />
      </div>
      <div
        id="asset-alerts"
        class="col-12 pt-5 scroll-elem"
      >
        <alerts />
      </div>
      <div
        id="asset-sources"
        class="col-12 pt-5 scroll-elem"
      >
        <sources-details :is-managed-asset="true"/>
      </div>
      <div
        id="asset-history"
        class="col-12 pt-5 scroll-elem"
      >
        <history />
      </div>
      <div
        id="asset-automation-history"
        class="col-12 pt-5 scroll-elem"
      >
        <automation-history :current-asset="currentAsset" />
      </div>
    </div>

    <attachments
      id="asset-attachments"
      :attachments="currentAsset.managedAssetAttachments"
    />

    <sweet-modal
      ref="locationAndUsageModal"
      v-sweet-esc
      title="Usage Status"
      data-tc-title="Usage Status"
      class="qr-modal"
      @close="closeLocationAndUsageModal"
    >
      <template #default>
        <form>
          <location-usage-fields
            ref="locationAndUsageForm"
            :key="locationUsageKey"
            :managed-asset="managedAsset"
            :is-edit-modal="true"
            :asset-type="assetValue"
            @input="updateUsage"
            @open-location-modal="openNewLocationModal"
            @open-user-modal="openNewUserModal"
            @submit="saveAsset"
          />
        </form>
        <div class="d-flex justify-content-between align-items-center pb-0 pt-3 border-top">
          <button
            slot="button"
            class="btn-sm btn btn-link text-secondary mr-2"
            data-tc-btn="cancel usage modal"
            @click.stop="cancelLocationAndUsageModal"
          >
            Cancel
          </button>
          <submit-button
            :is-saving="disabled"
            :btn-classes="'mx-2 btn-primary'"
            btn-content="Save Status"
            saving-content="Saving Status"
            @submit="saveLocationUsageData"
          />
        </div>
      </template>
    </sweet-modal>

    <sweet-modal
      ref="warrantyAndAcquisitionModal"
      v-sweet-esc
      title="Warranty & Acquisition"
      data-tc-title="Warranty & Acquisition"
      class="warranty-acquisition-modal qr-modal"
      @close="closeWarrantyAndAcquisitionModal"
    >
      <template #default>
        <warranty-acquisition-fields
          ref="warrantyAndAcquisitionForm"
          :managed-asset="managedAsset"
          :lock-field="lockField"
          :permission-asset="permissionAsset"
          @submit="saveAsset"
        />
        <div v-if="canFetchWarranty">
          <button
            class="btn btn-primary btn-sm mb-1"
            :disabled="disabled"
            @click.stop.prevent="fetchWarranty"
          >
            Fetch warranty
          </button>
          <p class="text-muted not-as-small">
            You can try fetching your latest warranty information by clicking on this button. It can take few minutes.
          </p>
        </div>
        <div class="d-flex justify-content-between align-items-center pb-0 pt-3 border-top">
          <button
            slot="button"
            class="btn-sm btn btn-link text-secondary mr-2"
            @click.stop="$refs.warrantyAndAcquisitionModal.close"
          >
            Cancel
          </button>
          <submit-button
            :is-saving="disabled"
            :btn-classes="'btn-sm'"
            btn-content="Save"
            saving-content="Saving"
            @submit="saveWarrantyAcquisitionData"
          />
        </div>
      </template>
    </sweet-modal>

    <sweet-modal
      ref="newLocationModal"
      v-sweet-esc
      blocking
      title="Add a new location"
      @close="newLocationFormKey = !newLocationFormKey"
    >
      <template #default>
        <location-custom-form-viewer
          render-from-modal
          @new-location="newLocation"
        />
      </template>
    </sweet-modal>

    <sweet-modal
      ref="newUserModal"
      v-sweet-esc
      title="Add a new teammate"
      blocking
    >
      <template #default>
        <company-user-custom-form-viewer
          render-from-modal
          @new-user="newUser"
        />
      </template>
    </sweet-modal>
  </div>

</template>

<script>
  import http from 'common/http';
  import VueMoment from 'vue-moment';
  import moment from 'moment-timezone';
  import _cloneDeep from 'lodash/cloneDeep';
  import LocationCustomFormViewer from 'components/shared/custom_forms/custom_form_viewer/location_custom_form_viewer.vue';
  import CompanyUserCustomFormViewer from 'components/shared/custom_forms/custom_form_viewer/company_user_custom_form_viewer.vue';
  import dates from 'mixins/dates';
  import { mapGetters, mapMutations } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import pluralize from 'pluralize/pluralize';
  import currency from 'mixins/currency';
  import universalLink from "mixins/universal_link";
  import permissionsHelper from 'mixins/permissions_helper';
  import phoneFormatter from 'mixins/phone_number_formatter';
  import assetHelper from 'mixins/assets/asset_helper';
  import cloudAssetsAttributes from 'components/assets/cloud_asset_attributes.vue';
  import WarrantyBar from "../warranty_bar.vue";
  import LocationUsageFields from '../form_elements/location_usage_fields.vue';
  import WarrantyAcquisitionFields from '../form_elements/warranty_acquisition_fields.vue';
  import Cost from './cost.vue';
  import Attachments from './attachments.vue';
  import DisplayUniversalLinks from '../../shared/universal_link/display_universal_links.vue';
  import SubmitButton from '../../shared/submit_button.vue';
  import SystemInsights from './system_insights.vue';
  import History from './history.vue';
  import AutomationHistory from './automation_history.vue';
  import SoftwareDetails from './software_details.vue';
  import SourcesDetails from '../sources.vue';
  import AssetDetails from './asset_details.vue';
  import Notes from './managed_asset_notes.vue';
  import Alerts from './managed_asset_alerts.vue';
  import TechSpec from './tech_specs.vue';
  import ManagedAssetDetails from './managed_asset_details.vue';
  import HardwareDetails from './hardware_details.vue';
  import SystemDetails from './system_details.vue';
  import Usage from './usage.vue';

  Vue.use(VueMoment, {
    moment,
  });

  export default {
    $_veeValidate: {
      validator: 'new',
    },
    components: {
      LocationCustomFormViewer,
      WarrantyBar,
      LocationUsageFields,
      WarrantyAcquisitionFields,
      SweetModal,
      Cost,
      Attachments,
      DisplayUniversalLinks,
      CompanyUserCustomFormViewer,
      SubmitButton,
      cloudAssetsAttributes,
      SystemInsights,
      SoftwareDetails,
      History,
      SourcesDetails,
      AssetDetails,
      Notes,
      Alerts,
      TechSpec,
      ManagedAssetDetails,
      HardwareDetails,
      AutomationHistory,
      SystemDetails,
      Usage,
    },
    mixins: [dates, currency, universalLink, permissionsHelper, phoneFormatter, assetHelper],
    data() {
      return {
        lockField: false,
        managedAsset: null,
        permissionAsset: {},
        openCostDepreciationModal: false,
        linkRefreshKey: false,
        disabled: false,
        locationUsageKey: false,
        newLocationFormKey: false,
        alertDatesKey: false,
      };
    },
    computed: {
      ...mapGetters([
        'currentAsset',
        'companyLocations',
        'integratedSources',
        'locationAndUsageModal',
      ]),
      assetValue() {
        return this.managedAsset.assetType;
      },
      isPhoneMobType() {
        return ['Mobile', 'Phone'].includes(this.managedAsset.assetType);
      },
      canFetchWarranty() {
        return this.currentAsset.manufacturer && this.currentAsset.machineSerialNumber;
      },
      managedByHref() {
        if (this.managedAsset.managedBy.type === "CompanyUser") {
          return `/company/users/${this.managedAsset.managedBy.rootId}`;
        }
        return `/company/groups/${this.managedAsset.managedBy.rootId}/edit`;
      },
      endDate() {
        if (this.managedAsset.warrantyExpiration) {
          return moment(this.managedAsset.warrantyExpiration).format("MMM DD[,] YYYY");
        }
        return "-";
      },
      acquisitionDate() {
        if (this.managedAsset.acquisitionDate) {
          return moment(this.managedAsset.acquisitionDate).format("MMM DD[,] YYYY");
        }
        return "-";
      },
      impactText() {
        if (this.managedAsset.impact === 'Low') {
          return 'text-success';
        } else if (this.managedAsset.impact === 'Medium') {
          return 'text-warning';
        }
        return 'text-danger';
      },
      haveAssetAlerts() {
        return Object.keys(this.managedAsset.alertDates).length > 0;
      },
      universalLinks() {
        return this.managedAsset.universalLinks;
      },
      mergedAsset() {
        return this.currentAsset.merged;
      },
      hardwareDetail() {
        return this.currentAsset.hardwareDetail;
      },
    },
    watch: {
      currentAsset() {
        this.managedAsset = _cloneDeep(this.currentAsset);
        this.setLockField();
        this.lockPopulatedFields();
        this.linkRefreshKey = !this.linkRefreshKey;
      },
      locationAndUsageModal() {
        if (this.locationAndUsageModal) {
          this.openLocationAndUsageModal();
        } else {
          this.$refs.locationAndUsageModal.close();
        }
      },
    },
    beforeDestroy() {
      this.managedAsset = null;
      this.alertDatesKey = false;
      this.permissionAsset = null;
      this.linkRefreshKey = false;
      this.locationUsageKey = false;
      this.newLocationFormKey = false;
      this.resetAsset();
    },
    methods: {
      ...mapMutations([
        'resetAsset',
        'updateAsset',
        'setCurrentAsset',
        'setLocationAndUsageModal',
      ]),
      onWorkspaceChange() {
        this.$store.dispatch('fetchAsset', this.$route.params.id);
        this.setLockField();
        this.lockPopulatedFields();
      },
      fetchWarranty() {
        this.disabled = true;

        http
          .put(`/managed_asset_warranties/${this.currentAsset.id}`)
          .then(() => {
            this.emitSuccess('Asset warranty fetching is initiated.');
            this.disabled = false;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error fetching asset warranty.');
            this.disabled = false;
          });
      },
      openNewUserModal() {
        this.$refs.newUserModal.open();
      },
      openNewLocationModal() {
        this.$refs.newLocationModal.open();
      },
      newUser() {
        this.$refs.newUserModal.close();
        this.$store.dispatch('fetchCompanyUserOptions');
      },
      updateUsage(asset) {
        this.managedAsset.locationId = asset.locationId;
        this.managedAsset.assignmentInformationAttributes.locationId = asset.locationId;
        this.managedAsset.assignmentInformationAttributes.department = asset.assignmentInformationAttributes.department;
        this.managedAsset.assignmentInformationAttributes.usedByContributorId = asset.assignmentInformationAttributes.usedByContributorId;
        this.managedAsset.assignmentInformationAttributes.managedByContributorId = asset.assignmentInformationAttributes.managedByContributorId;
        this.managedAsset.assignmentInformationAttributes.phoneNumber = asset.assignmentInformationAttributes.phoneNumber;
        this.managedAsset.assignmentInformationAttributes.phoneNumberCountryCode = asset.assignmentInformationAttributes.phoneNumberCountryCode;
        this.managedAsset.assignmentInformationAttributes.phoneNumberCountryCodeNumber = asset.assignmentInformationAttributes.phoneNumberCountryCodeNumber;
      },
      openLocationAndUsageModal() {
        if (this.$refs.locationAndUsageModal) {
          this.$refs.locationAndUsageModal.open();
        }
      },
      displayCurrency(value) {
        return this.formatToUnits(value);
      },
      newLocation() {
        this.$refs.newLocationModal.close();
        this.$store.dispatch('fetchCompanyLocations');
      },
      pluralize(...args) {
        return pluralize(...args);
      },
      setLockField() {
        if (this.managedAsset) {
          if (['probe', 'agent', 'selfonboarding'].includes(this.managedAsset.source)) {
            this.lockField = true;
          }
        }
      },
      lockPopulatedFields() {
        if (this.lockField) {
          Object.keys(this.managedAsset).forEach((key) => {
            if (this.managedAsset[key] == null) {
              this.permissionAsset[key] = false;
            } else if (this.managedAsset[key].toString().trim() === '') {
              this.permissionAsset[key] = false;
            } else {
              this.permissionAsset[key] = true;
            }
          });
        } else if (this.managedAsset) {
          Object.keys(this.managedAsset).forEach((key) => {
            this.permissionAsset[key] = true;
          });
        }
      },
      saveAsset(data) {
        const assetData = 'createUsageHistory' in data ? data.asset : data;
        const createUsageHistory = 'createUsageHistory' in data ? data.createUsageHistory : false;
        this.$validator.validateAll().then((result) => {
          if (result) {
            assetData.source = "manually_added";
            this.disabled = true;
            http
              .put(`/managed_assets/${this.managedAsset.id}.json`, { managed_asset: assetData, create_usage_history: createUsageHistory })
              .then((res) => {
                this.emitSuccess("Asset was successfully updated");
                this.updateAsset(res.data.asset);
                const params = { id: this.managedAsset.id };
                this.$store.dispatch('fetchAuditHistory', params);
                this.setCurrentAsset(res.data.asset);
                this.closeAssetModals();
              })
              .catch((error) => {
                if (error.response && error.response.data) {
                  this.emitError(`Sorry, there was an error saving this asset: ${error.response.data.message}.`);
                } else {
                  this.emitError(`Sorry, there was an error saving this asset: ${error.message}.`);
                }
              })
              .finally(() => {
                this.disabled = false;
              });
          } else {
            this.emitError('Please correct the highlighted errors before submitting.');
          }
        });
      },
      closeAssetModals() {
        this.$refs.locationAndUsageModal.close();
        this.$refs.warrantyAndAcquisitionModal.close();
        this.closeCostDepreciationModal();
      },
      closeCostDepreciationModal() {
        this.openCostDepreciationModal = false;
      },
      openAlertDatesModal() {
        this.$refs.alertModal.$refs.alertDateModal.open();
      },
      updateRelatedItems(links) {
        this.currentAsset.universalLinks = links;
        this.setCurrentAsset(this.currentAsset);
      },
      closeAlertDateModal() {
        this.managedAsset.alertDates = _cloneDeep(this.currentAsset.alertDates);
        this.alertDatesKey = !this.alertDatesKey;
      },
      closeLocationAndUsageModal() {
        this.managedAsset.assignmentInformationAttributes = _cloneDeep(this.currentAsset.assignmentInformationAttributes);
        this.managedAsset.locationId = _cloneDeep(this.currentAsset.locationId);
        this.locationUsageKey = !this.locationUsageKey;
        if (this.locationAndUsageModal) {
          this.setLocationAndUsageModal();
        };
      },
      cancelLocationAndUsageModal() {
        this.$refs.locationAndUsageModal.close();
        if (this.locationAndUsageModal) {
          this.setLocationAndUsageModal();
        };
      },
      closeWarrantyAndAcquisitionModal() {
        this.$refs.warrantyAndAcquisitionForm.initWarrantyAcquisitionInfo();
      },
      saveLocationUsageData() {
        this.$refs.locationAndUsageForm.submitAsset();
      },
      saveWarrantyAcquisitionData() {
        this.$refs.warrantyAndAcquisitionForm.submitAsset();
      },
      formatNumber() {
        return this.processPhoneNumber(
          this.currentAsset.assignmentInformationAttributes.phoneNumber,
          this.currentAsset.assignmentInformationAttributes.phoneNumberCountryCode,
          this.currentAsset.assignmentInformationAttributes.phoneNumberCountryCodeNumber
        );
      },
      saveAssetAlertDates(asset) {
        this.saveAlertDates(asset);
      },
    },
  };
</script>

<style scoped lang="scss">
  .alert-modal :deep(.sweet-modal) {
    overflow: unset;
    width: 450px;
  }
  .scroll-elem {
    scroll-margin-top: 2rem;
  }
</style>
