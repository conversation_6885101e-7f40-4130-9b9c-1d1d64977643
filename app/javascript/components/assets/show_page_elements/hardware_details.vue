<template>
  <div>
    <h5
      class="font-weight-normal mb-3 d-inline-block"
      data-tc-hardware-details
    >
      System Details
    </h5>
    <span
      v-if="showHardwareDetails && isWrite && !mergedAsset"
      v-tooltip="'Edit Details'"
      class="d-inline-block float-right btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm"
      data-tc-edit="hardware"
      @click.stop.prevent="$refs.hardwareDetailsModal.open()"
    >
      <i class="nulodgicon-edit clickable h6" />
    </span>
    <div class="box pt-3">
      <div
        v-if="showHardwareDetails"
        class="w-100"
      >
        <div class="box__inner asset-attributes">
          <component
            :is="harwareDetailType"
            :current-asset="currentAsset"
            css="col-md-4 col-sm-12 mb-4"
          />
        </div>

        <sweet-modal
          v-if="tempAsset"
          ref="hardwareDetailsModal"
          v-sweet-esc
          title="Hardware Details"
          blocking
          @close="resetTempAsset"
        >
          <template slot="default">
            <hardware-fields
              v-if="tempAsset.hardwareDetailType != 'OtherDetail' && tempAsset.assetTypeName != 'Other'"
              :asset="tempAsset"
              :lock-field="lockField"
              @change-firmware="changeFirmware"
              @change-ports="changePorts"
              @change-sku="changeSku"
              @change-serial-number="changeSerialNumber"
              @change-product-id="changeProductId"
            />
            <div
              v-if="tempAsset.hardwareDetailType === 'OtherDetail' || tempAsset.assetTypeName === 'Other'"
              class="form-group"
            >
              <label>Serial Number</label>
              <input
                id="hardware_detail_serial_number"
                v-model="tempAsset.machineSerialNumber"
                class="form-control"
                name="managed_asset[machine_serial_number]"
              >
            </div>
            <div
              v-if="tempAsset.hardwareDetailType === 'OtherDetail' || tempAsset.assetTypeName === 'Other'"
              class="form-group"
            >
              <label>Product/Model Number</label>
              <input
                id="hardware_detail_model_number"
                v-model="tempAsset.sku"
                class="form-control"
                name="managed_asset[sku]"
                maxlength="100"
                placeholder="sku, upc, mpn, cpn, ean, getin etc"
              >
            </div>
            <div class="form-group">
              <label>Mac Addresses</label>
              <textarea
                id="hardware_detail_mac_address"
                v-model="tempAsset.macAddressesText"
                v-validate="'macAddress'"
                class="form-control"
                :class="{ 'isvalid': errors.has('managed_asset[mac_addresses_text]') }"
                rows="4"
                name="managed_asset[mac_addresses_text]"
              />
              <span
                v-if="errors.has('managed_asset[mac_addresses_text]')"
                class="small text-danger"
              >
                Please enter the valid MAC address
              </span>
            </div>
            <div class="form-group">
              <label>IP Address</label>
              <input
                id="hardware_detail_ip_address"
                v-model="tempAsset.ipAddress"
                v-validate="'ipAddress'"
                class="form-control"
                :class="{ 'isvalid': errors.has('managed_asset[ip_address]') }"
                name="managed_asset[ip_address]"
              >
              <span
                v-if="errors.has('managed_asset[ip_address]')"
                class="small text-danger"
              >
                Please enter the valid IP address.
              </span>
            </div>
          </template>
          <button
            slot="button"
            class="btn-sm btn btn-link text-secondary mr-2"
            data-tc-cancel="hardware details"
            @click.stop="$refs.hardwareDetailsModal.close()"
          >
            Cancel
          </button>
          <button
            slot="button"
            class="btn btn-sm btn-primary"
            data-tc-save="hardware details"
            @click.stop="validateThenSave"
          >
            Save
          </button>
        </sweet-modal>
      </div>
      <div 
        v-else
        class="text-center pt-3 w-100" 
      >
        <p
          class="text-secondary"
          data-tc-no-hardware-details
        >
          This asset type ({{ currentAsset.assetTypeName }}) doesn't have hardware details associated with it. You can change the asset type
          <router-link
            :to="`/${currentAsset.id}/edit`"
          >
            here.
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapGetters, mapMutations } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import assetSpecific from "mixins/asset_specific";
  import HardwareFields from '../form_elements/hardware_fields.vue';
  import * as hardwareDetailTypes from './hardware_detail_types/index';

  export default {
    $_veeValidate: {
      validator: 'new',
    },
    components: {
      SweetModal,
      HardwareFields,
      ...hardwareDetailTypes,
    },
    mixins: [permissionsHelper, assetSpecific],
    data() {
      return {
        lockField: false,
        tempAsset: null,
      };
    },
    computed: {
      ...mapGetters(['currentAsset']),
      harwareDetailType() {
        return this.currentAsset.hardwareDetailType ? this.currentAsset.hardwareDetailType  : "OtherDetail";
      },
      mergedAsset() {
        return this.currentAsset.merged;
      },
      showHardwareDetails() {
        return (this.currentAsset && this.currentAsset.hardwareDetailType && this.currentAsset.hardwareDetail) || this.currentAsset.assetTypeName === 'Other';
      },
    },
    watch: {
      currentAsset() {
        this.resetTempAsset();
      },
    },
    created() {
      this.addMacAddressValidation();
      this.addIpAddressValidation();
    },
    methods: {
      ...mapMutations([
        'updateAsset',
        'setCurrentAsset',
      ]),

      onWorkspaceChange() {
        this.resetTempAsset();
        if (['probe', 'agent', 'selfonboarding'].includes(this.currentAsset.source)) {
          this.lockField = true;
        }
      },
      changeFirmware(value) {
        this.tempAsset.firmware = value;
      },
      changePorts(value) {
        this.tempAsset.hardwareDetail.ports = value;
      },
      changeSku(value) {
        this.tempAsset.sku = value;
      },
      changeSerialNumber(value) {
        this.tempAsset.machineSerialNumber = value;
      },
      changeProductId(value) {
        this.tempAsset.hardwareDetail.id = value;
      },
      submitEdit() {
        this.tempAsset.hardwareDetailAttributes = this.tempAsset.hardwareDetail;
        http
          .put(`/managed_assets/${this.tempAsset.id}.json`, {managed_asset: this.tempAsset})
          .then((res) => {
            this.emitSuccess("Asset was successfully updated");
            this.updateAsset(res.data.asset);
            this.setCurrentAsset(res.data.asset);
            this.$refs.hardwareDetailsModal.close();
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error saving this asset: (${error.response.data.message}).`);
          });
      },
      resetTempAsset() {
        this.tempAsset = JSON.parse(JSON.stringify(this.currentAsset));
      },
      validateThenSave() {
        this.$validator.validateAll().then((result) => {
          if (result) {
            this.submitEdit();
          } else {
            this.emitError('Please correct the highlighted errors before submitting.');
          }
        });
      },
    },
  };
</script>

<style scoped lang="scss">
  .asset-attributes {
    :deep(img) {
      height: 14px;
    }
  }
</style>
