<template>
  <div>
    <div>
      <h5 class="font-weight-normal mb-3">
        History
      </h5>
      <div v-if="historyLoading">
        <span class="h6 text-secondary float-left">Loading history...</span>
        <sync-loader
          :loading="true"
          class="mx-3 mt-1 float-left"
          color="#0d6efd"
          size="0.5rem"
        />
      </div>
      <div v-else>
        <div
          class="box mt-3 p-3 pr-5 d-flex align-items-start justify-content-start overflow-auto scollable"
          :class="{'history-row': auditHistoryItems.length > 0}"
        >
          <div
            class="w-100 history-row-item d-flex mt-1"
            :class="{
              'justify-content-end': historyPageCount > 1,
              'justify-content-center': historyPageCount <= 1
            }"
          >
            <span class="small d-flex align-items-center">
              <span class="text-muted align-text-bottom mt-2 mr-2">
                Results per page
              </span>
              <select
                id="filtersPerPage"
                class="form-control form-control-sm d-inline-block select-per-page-filter pt-0"
                :input="historyPerPage"
                :value="historyPerPage"
                data-tc-filters-per-page
                @input="changePageSize"
              >
                <option>25</option>
                <option>50</option>
                <option>100</option>
              </select>
            </span>
            <div
              v-if="historyPageCount > 1"
              class="ml-3"
            >
              <paginate
                ref="paginate"
                :click-handler="pageSelected"
                :container-class="'pagination pagination-sm'"
                :next-class="'next-item'"
                :next-link-class="'page-link'"
                :next-text="'Next'"
                :page-class="'page-item'"
                :page-count="historyPageCount"
                :page-link-class="'page-link'"
                :prev-class="'prev-item'"
                :prev-link-class="'page-link'"
                :prev-text="'Prev'"
                :selected="indexPage"
              />
            </div>
          </div>

          <div
            v-for="historyItem in auditHistoryItems"
            :key="historyItem.id"
            class="w-100 history-row-item"
          >
            <asset-audit
              v-if="(historyItem.auditableType === 'ManagedAsset' || historyItem.auditableType === 'AlertDate')"
              :history-item="historyItem"
              :company-products="companyProducts"
              :status-options="statusOptions"
            />
  
            <assignment-information-audit
              v-if="historyItem.auditableType === 'AssignmentInformation'"
              :history-item="historyItem"
            />
  
            <asset-tag-audit
              v-if="historyItem.auditableType === 'ManagedAssetTag'"
              :history-item="historyItem"
            />
  
            <asset-hardware-audit
              v-if="hardwareDetails.includes(historyItem.auditableType)"
              :history-item="historyItem"
            />
  
            <asset-software-audit
              v-if="historyItem.auditableType === 'AssetSoftware'"
              :history-item="historyItem"
            />
  
            <cost-audit
              v-if="historyItem.auditableType === 'Cost'"
              :history-item="historyItem"
            />
  
            <asset-attachment-audit 
              v-if="historyItem.auditableType === 'ManagedAssetAttachment'"
              :history-item="historyItem"
            />
            
            <universal-link-audits
              v-if="historyItem.auditableType === 'LinkableLink'"
              :history-item="historyItem"
            />
          </div>
  
          <div
            v-if="showCreatedAt"
            class="w-100 history-row-item"
          >
            <div class="row">
              <div class="col-12 p-3 not-as-small">
                <div class="mobile-two-col d-flex flex-row align-items-start w-100">
                  <div class="mobile-icon-col d-flex flex-column align-items-start justify-content-start w-[20%]">
                    <span class="action-icon mr-2">
                      <i class="nulodgicon-plus-round" />
                    </span>
    
                    <span v-tooltip="`Source: ${toTitle(createdSource)}`">
                      <img
                        v-if="createdSource"
                        class="history-source-icon mb-2 mr-2"
                        :src="getSourceIcon(createdSource)"
                      >
                    </span>
                  </div>
                  <div class="mobile-content-col d-flex flex-column align-items-start justify-content-center ms-1 w-[80%]">
                    <span>
                      {{ currentAsset.name }} created
                    </span>
                  </div>
                </div>
                <span class="float-right small text-muted">{{ createdOn }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="currentAsset.helpTickets.length > 0">
      <h5 class="font-weight-normal mb-5 mt-5">
        Associated Help Tickets
      </h5>
      <div class="pr-0 mt-4">
        <div class="box__inner">
          <div
            v-if="ticketsHistory == null"
            class="pt-4"
          >
            <span class="h5 text-secondary float-left">Loading history...</span>
            <sync-loader
              :loading="true"
              class="mx-3 mt-1 float-left"
              color="#0d6efd"
              size="0.5rem"
            />
          </div>
          <div v-else>
            <scrollable-table
              class="mb-0"
              :min-width="'40rem'"
              :min-height="480"
            >
              <thead>
                <tr>
                  <th>Ticket Number</th>
                  <th>Subject</th>
                  <th>Status</th>
                  <th>Created At</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="ticket in ticketsHistory"
                  :key="ticket.id"
                >
                  <td>
                    <a
                      class="alert-link ml-1"
                      target="_blank"
                      :href="`/help_tickets/${ticket.id}`"
                    >
                      {{ ticket.ticketNumber }}
                    </a>
                  </td>
                  <td>
                    <span
                      v-tooltip="{
                        content: ticket.subject,
                        show: (ticket.subject && ticket.subject.length > 40 && hoveredIndex == ticket.id),
                        trigger: 'manual'
                      }"
                      class="truncated"
                      @mouseover="hoveredIndex = ticket.id"
                      @mouseleave="hoveredIndex = null"
                    >
                      {{ truncate(ticket.subject, 40) }}
                    </span>
                  </td>
                  <td>{{ ticket.status }}</td>
                  <td>{{ ticketCreatedAt(ticket.createdAt) }}</td>
                </tr>
              </tbody>
            </scrollable-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import UniversalLinkAudits from 'components/shared/universal_link/universal_link_audits.vue';
  import ScrollableTable from 'components/shared/scrollable_table.vue';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import { mapGetters } from 'vuex';
  import audits from 'mixins/audits';
  import strings from 'mixins/string';
  import Paginate from 'vuejs-paginate';
  import permissionsHelper from 'mixins/permissions_helper';
  import pageSizeInCookies from 'mixins/page_size_helper';
  import assetAudit from '../audit_elements/asset.vue';
  import assetSoftwareAudit from '../audit_elements/asset_software.vue';
  import assetHardwareAudit from '../audit_elements/asset_hardware.vue';
  import assignmentInformationAudit from '../audit_elements/assignment_information.vue';
  import assetTagAudit from '../audit_elements/asset_tag.vue';
  import costAudit from '../audit_elements/cost.vue';
  import AssetAttachmentAudit from '../audit_elements/managed_asset_attachments_audit.vue';

  const HARDWARE_DETAILS = ['ComputerDetail', 'PrinterDetail', 'MobileDetail', 'OtherDetail', 'NetworkingDeviceDetail', 'PhoneDetail'];

  export default {
    components: {
      assetSoftwareAudit,
      assetHardwareAudit,
      assignmentInformationAudit,
      assetTagAudit,
      assetAudit,
      costAudit,
      AssetAttachmentAudit,
      UniversalLinkAudits,
      ScrollableTable,
      SyncLoader,
      Paginate,
    },
    mixins: [audits, strings, permissionsHelper, pageSizeInCookies],
    data() {
      return {
        hardwareDetails: HARDWARE_DETAILS,
        statusOptions: [],
        indexPage: 0,
        hoveredIndex: null,
      };
    },
    computed: {
      ...mapGetters([
        'auditHistoryItems',
        'historyPageCount',
        'historyPerPage',
        'historyLoading',
        'currentAsset',
        'companyProducts',
        'associateTicketsHistory']),
      createdOn() {
        return this.toISOstring(this.currentAsset.createdAt).format("MMM DD, YYYY");
      },
      ticketsHistory() {
        return this.associateTicketsHistory;
      },
      showCreatedAt() {
        return this.indexPage === this.historyPageCount || this.indexPage + 1 === this.historyPageCount;
      },
    },
    methods: {
      onWorkspaceChange() {
        this.checkPerPage();
        this.loadAuditHistory();
        this.getStatusOptions();
        this.$store.dispatch('fetchCompanyProducts');
        this.fetchUserOptions();
        this.$store.dispatch('fetchCompanyLocations');
        this.$store.dispatch('fetchAssetTypes');
        this.$store.dispatch('fetchDepreciations');
        if(this.currentAsset.helpTickets.length > 0) {
          this.$store.dispatch('fetchAssociateTicketsHistory', this.currentAsset.helpTickets);
        }
        // this.$store.dispatch('fetchSoftwares', this.currentAsset.id);
      },
      checkPerPage() {
        if (this.perPagePresentInCookie('asset_history-module=')) {
        const cookieData = this.getCookieValue('asset_history-per-page');
        if (cookieData) {
          this.$store.commit('setHistoryPerPage', parseInt(cookieData, 10));
        }
      }
      },
      changePageSize(e) {
        this.$store.commit('setHistoryPerPage', e.currentTarget.value);
        this.indexPage = 0;
        this.setPerPageInCookie("asset_history", parseInt(this.historyPerPage, 10));
        this.loadAuditHistory();
      },
      ticketCreatedAt(createdAt) {
        return moment(createdAt).format('MMM DD, YYYY');
      },
      loadAuditHistory() {
        const params = {
          id: this.currentAsset.id,
          page: this.indexPage + 1,
        };
        this.$store.dispatch('fetchAuditHistory', params);
      },
      getStatusOptions() {
        http
          .get('/managed_asset_statuses')
          .then(res => {
            this.statusOptions = res.data;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error getting asset status options. Please refresh the page and try again.`);
          });
      },
      fetchUserOptions() {
        const params = { archived: "all" };

        this.$store.dispatch('fetchAuditedUserOptions', params);
        this.$store.dispatch('fetchCompanyUserAndGroupsOptions', params);
      },
      pageSelected(p) {
        this.indexPage = p - 1;
        this.loadAuditHistory();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .action-icon {
    background-color: $purple;
  }

  ::v-deep .action-icon {
    position: relative;
  }
  
  ::v-deep .history-source-icon {
    width: 30px;
    height: 30px;
    border: 1px solid #d6d6d6;
    border-radius: 50%;
    padding: 2px;
    margin-top: 2.7px;
    margin-left: -25px;
    transition: margin-left .4s ease-in-out;

    &:hover {
      margin-left: -10px;
    }
  }

  ::v-deep .history-source-icon {
    @media (max-width: 600px) {
      border: 1px solid #d6d6d6;
      border-radius: 50%;
      padding: 2px;
      margin-top: -40px;
      margin-left: 20px;
      transition: margin-left .4s ease-in-out;
      }
      &:hover {
      margin-left: 0px;
    }

  }
  .history-row:after {
      content: '';
      position: absolute;
      left: 20px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: $themed-light;
      z-index: 0;
      display: block;
    }
  
  @media (min-width: 768px) {
    .history-row-item {
      justify-content: flex-end !important;
    }
  }
</style>
