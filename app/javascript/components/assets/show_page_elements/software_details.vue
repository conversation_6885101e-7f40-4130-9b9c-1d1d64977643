<template>
  <div>
    <h5 class="font-weight-normal mb-3 d-inline-block">
      Software
    </h5>

    <div class="box p-3 pb-5 box--with-title bg-themed-box-bg d-block">
      <div class="row mb-3">
        <div class="search-holder col-12 col-lg-4 mb-3 mb-lg-0">
          <div class="search-wrap">
            <input
              ref="searchInput"
              v-model="softwareSearch"
              data-tc-search-software-field
              type="text"
              class="form-control search-input"
              placeholder="Search your softwares by name..."
              @input="updateSearch"
            >
            <i
              class="nulodgicon-ios-search-strong search-input-icon"
              @click.prevent="$refs.searchInput.focus"
            />
          </div>
        </div>

        <div class="col-12 col-lg-8 d-md-flex align-items-center justify-content-end text-center text-md-right">
          <div class="mb-3 mb-md-0 d-flex justify-content-center">
            <dropdown-filter
              :options="assetStatuses"
              id-label="id"
              label="Status"
              :value="softwareStatusId"
              class="mr-3"
              name="name"
              data-tc-software-filter-status
              @selected="filterStatus"
            />
            <div class="per-page-position mr-2">
              <span class="text-position btn-sm text-secondary"> Results per page </span>
              <select
                id="filtersPerPage"
                data-tc-filters-per-page
                :input="perPage"
                class="form-control"
                @input="changePageSize"
              >
                <option>25</option>
                <option>50</option>
                <option>100</option>
              </select>
            </div>
          </div>
          <button
            v-if="isWrite && !mergedAsset"
            data-tc-add-software
            class="not-as-small btn btn-link"
            @click.stop.prevent="openSoftwareModal"
          >
            <i class="nulodgicon-plus-round mr-1" />
            Add Software
          </button>
        </div>
      </div>
      <div
        v-if="pageCount > 1"
        class="float-right pr-3 mt-1"
      >
        <paginate
          :click-handler="pageSelected"
          :container-class="'pagination pagination-sm'"
          :next-class="'next-item'"
          :next-link-class="'page-link'"
          :next-text="'Next'"
          :page-class="'page-item'"
          :page-count="pageCount"
          :page-link-class="'page-link'"
          :prev-class="'prev-item'"
          :prev-link-class="'page-link'"
          :prev-text="'Prev'"
          :selected="pageIndex"
        />
      </div>

      <div>
        <div v-if="isLoading">
          <span class="h6">Loading softwares...</span>
          <sync-loader
            class="float-left mx-3 mt-1"
            color="#0d6efd"
            size="0.5rem"
            :loading="true"
          />
        </div>
        <div v-else-if="pageCount > 0">
          <div class="table-responsive">
            <table class="table table-striped tbl-sft">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Software Type</th>
                  <th>Product Key</th>
                  <th>Install Date</th>
                  <th />
                </tr>
              </thead>
              <tbody>
                <software-item
                  v-for="software in assetSoftwares"
                  :key="`${software.id}_${software.name}`"
                  :software="software"
                  :edit-permission="!mergedAsset"
                  @fetch-softwares="fetchAssetSoftwares"
                />
              </tbody>
            </table>
          </div>
          <div
            v-if="pageCount > 1"
            class="float-right"
          >
            <paginate
              :click-handler="pageSelected"
              :container-class="'pagination pagination-sm'"
              :next-class="'next-item'"
              :next-link-class="'page-link'"
              :next-text="'Next'"
              :page-class="'page-item'"
              :page-count="pageCount"
              :page-link-class="'page-link'"
              :prev-class="'prev-item'"
              :prev-link-class="'page-link'"
              :prev-text="'Prev'"
              :selected="pageIndex"
            />
          </div>
        </div>
        <div
          v-else
          class="text-center p-4"
        >
          <h4 data-tc-no-tracking>
            Not tracking anything here, yet.
          </h4>
          <h6 class="text-secondary font-weight-normal">
            Try clearing the search box, removing filters,
            <span v-if="isWrite && !mergedAsset">
              or
              <button
                class="btn btn-link"
                @click.stop.prevent="openSoftwareModal"
              >
                Add Software
              </button>
            </span>
          </h6>
        </div>
      </div>
    </div>
    <sweet-modal
      ref="softwareDetailsModal"
      class="modal-height"
      v-sweet-esc
      title="New Software"
      blocking
      data-tc-title="new software"
    >
      <software-form
        ref='softwareForm'
        @fetch-softwares="fetchAssetSoftwares"
        @close-modal='$refs.softwareDetailsModal.close()'
      />
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapGetters, mapMutations } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import Paginate from 'vuejs-paginate';
  import dates from 'mixins/dates';
  import permissionsHelper from "mixins/permissions_helper";
  import _debounce from 'lodash/debounce';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import SoftwareFields from '../form_elements/software_fields';
  import SoftwareForm from '../show_page_elements/software_form';
  import SoftwareItem from '../show_page_elements/software_item';
  import TableHeaderChecked from '../../shared/table_header.vue';
  import DropdownFilter from '../../shared/dropdown_filter.vue';

  export default {
    components: {
      DropdownFilter,
      SoftwareForm,
      SoftwareItem,
      TableHeaderChecked,
      Paginate,
      SweetModal,
      SoftwareFields,
      SoftwareForm,
      SyncLoader,
    },
    mixins: [dates, permissionsHelper],
    data() {
      return {
        lockField: false,
        checkedSoftwares: [],
        softwareStatusId: 'active',
        page: 1,
        perPage: 25,
        softwareSearch: "",
        softwares: [],
        totalSoftwares: 0,
        isLoading: true,
      }
    },
    computed: {
      ...mapGetters(['currentAsset', 'softwareTypes', 'assetStatuses']),

      assetSoftwares(){
        return this.softwares.sort((a) => a.softwareType === "Operating System" ? -1 : 1 );
      },
      pageCount() {
        return Math.ceil(this.totalSoftwares / this.perPage);
      },
      tableHeaders() {
        return [{name: 'Link', include: true, iconClass: "nulodgicon-edit"},
                {name: 'Archive', include: true, iconClass: "nulodgicon-trash-b"},
              ]
      },
      pageIndex() {
        return this.page - 1;
      },
      mergedAsset() {
        return this.currentAsset.merged;
      }
    },
    methods: {
      ...mapMutations([
        'updateAsset',
        'setCurrentAsset',
      ]),

      onWorkspaceChange() {
        if (['probe', 'agent', 'selfonboarding'].includes(this.currentAsset.source)) {
          this.lockField = true;
        }
        this.fetchAssetSoftwares();
      },
      fetchAssetSoftwares() {
        this.isLoading = true;
        const params = { page: this.page, perPage: this.perPage, searchText: this.softwareSearch, status: this.softwareStatusId };
        http
          .get(`/managed_assets/${this.currentAsset.id}/asset_softwares`, { params })
          .then((res) => {
            this.softwares = res.data.softwares;
            this.totalSoftwares = res.data.totalSoftwares;
            this.isLoading = false;
          })
          .catch(() => {
            this.isLoading = false;
            this.emitError('Sorry, there was an error while fetching softwares.');
          });
      },
      updateSearch: _debounce(
        function () {
          this.page = 1;
          this.fetchAssetSoftwares();
        },
        1000
      ),
      filterStatus(e)  {
        this.softwareStatusId = e;
        this.page = 1;
        this.fetchAssetSoftwares();
      },
      openSoftware(software) {
        this.$router.push(`software/${software.id}`);
      },
      changePageSize(e) {
        this.perPage = e.currentTarget.value;
        this.page = 1;
        this.fetchAssetSoftwares();
      },
      submitEdit() {
        this.currentAsset.assetSoftwaresAttributes = this.assetSoftwares;
        http
          .put(`/managed_assets/${this.currentAsset.id}.json`, {managed_asset: this.currentAsset})
          .then((res) => {
            this.emitSuccess("Asset was successfully updated");
            this.updateAsset(res.data.asset);
            this.setCurrentAsset(res.data.asset);
          })
          .catch((error) => {
            this.emitError(`Sorry, there was an error saving this asset: (${error.response.data.message}).`);
          })
          .finally(() => {
            this.$refs.softwareDetailsModal.close();
          });
      },
      pageSelected(p) {
        this.page = p;
        this.fetchAssetSoftwares();
      },
      showToolTip(name, property) {
        if (name.length >= 35) {
          this[property] = name;
        } else {
          this[property] = null;
        }
      },
      openSoftwareModal() {
        this.$refs.softwareForm.setSoftware({});
        this.$refs.softwareDetailsModal.open(); 
      }
    }
  }
</script>

<style lang="scss" scoped>

.tbl-sft td {
  max-width: 300px;
}

.search-holder {
  width: 398px;
  height: 40px;
}

.modal-height {
  :deep(.sweet-modal) {
    height: 38rem;
  }
}

.per-page-position {
  display: flex;
  span {
    width: 120px;
  }
  select {
    height: 35px;
    width: 75px;
  }
}
</style>
