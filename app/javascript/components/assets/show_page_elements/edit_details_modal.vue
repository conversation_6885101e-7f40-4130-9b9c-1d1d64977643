<template>
  <div>
    <sweet-modal
      ref="assetDetailsModal"
      v-sweet-esc
      title="Managed Asset Details"
    >
      <template>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>Key</th>
              <th>Value</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(detail, index) in details"
              :key="index"
            >
              <td>
                <input
                  id="detail_key"
                  v-model="detail[0]"
                  class="form-control"
                />
              </td>
              <td>
                <input
                  id="detail_value"
                  v-model="detail[1]"
                  class="form-control"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </template>
      <button
        slot="button"
        class="btn-sm btn btn-link text-secondary mr-2"
        @click.stop="$emit('cancel')"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-sm btn-primary"
        @click.stop="$emit('update-details')"
      >
        Save
      </button>
    </sweet-modal>
  </div>
</template>

<script>
import { SweetModal } from 'sweet-modal-vue';

export default {
  props: ['details'],
  components: {
    SweetModal,
  },
  methods: {
    open() {
      this.$refs.assetDetailsModal.open();
    },
    close() {
      this.$refs.assetDetailsModal.close();
    },
  },
};
</script>
