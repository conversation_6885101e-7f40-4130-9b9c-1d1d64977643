<template>
  <div>
    <h5 class="font-weight-normal mb-3">
      Asset Automation History
    </h5>
    <div class="box p-2">
      <div
        v-if="currentAsset"
        class="d-flex flex-column w-100 managed-asset-activities position-relative"
      >
        <div
          v-if="assetActivitiesPageCount > 1"
          class="position-relative"
          style="height: 1rem;"
        >
          <paginate
            ref="paginate"
            class="mb-5 px-2 position-absolute pagination-position"
            :click-handler="pageSelected"
            :container-class="'pagination pagination-sm'"
            :next-class="'next-item'"
            :next-link-class="'page-link'"
            :next-text="'Next'"
            :page-class="'page-item'"
            :page-count="assetActivitiesPageCount"
            :page-link-class="'page-link'"
            :prev-class="'prev-item'"
            :prev-link-class="'page-link'"
            :prev-text="'Prev'"
            :selected="assetActivitiesPage"
          />
        </div>
        <div v-if="!assetActivities">
          <span class="text-secondary font-weight-normal">
            Loading activities
          </span>
          <pulse-loader
            :loading="true"
            class="ml-3 d-inline-block"
            color="#0d6efd"
            size="0.5rem"
          />
        </div>
        <div v-else-if="assetActivities && assetActivities.length == 0">
          There are no automation activities yet for this asset
        </div>
        <div
          v-else
          class="mt-2 rounded bg-themed-box-bg px-3"
        >
          <div class="d-flex flex-column">
            <div
              v-for="activity in assetActivities"
              :key="activity.id"
            >
              <activity
                :activity="activity"
              >
                <template #activity-owner>
                  <div class="col-sm-12 col-md-auto mt-sm-1 mt-md-0 text-right small text-muted p--responsive">
                    <span>{{ ownerName }}</span>
                    <br>
                    <span class="small">{{ createdAt(activity) }}</span>
                  </div>
                </template>
              </activity>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <h5 class="text-muted">
          Loading Asset Automation History
        </h5>
        <pulse-loader
          :loading="true"
          class="float-left ml-3"
          color="#0d6efd"
          size="0.5rem"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import { mapActions, mapGetters, mapMutations } from 'vuex';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import Paginate from 'vuejs-paginate';
  import companyUser from 'mixins/company_user';
  import momentTimezone from 'mixins/moment-timezone';
  import customFormFields from 'mixins/custom_forms/fields';
  import strings from 'mixins/string';
  import permissionsHelper from 'mixins/permissions_helper';
  import * as ActivityTypes from '../../help_tickets/show/history/activities/index';
  import Activity from '../../help_tickets/show/history/activity.vue';

  export default {
    components: {
      PulseLoader,
      Activity,
      Paginate,
      ...ActivityTypes,
    },
    mixins: [companyUser, momentTimezone, customFormFields, strings, permissionsHelper],
    props: {
      currentAsset: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        offsetRow: false,
        ownerName: "System",
      };
    },
    computed: {
      ...mapGetters({
        assetActivities: 'assetActivities',
        assetActivitiesPage: 'assetActivitiesPage',
        assetActivitiesPageCount: 'assetActivitiesPageCount',
      }),
    },
    mounted() {
      this.$store.dispatch('fetchAssetActivities', this.currentAsset.id);
    },
    methods: {
      ...mapMutations(['setAssetActivitiesPage']),
      ...mapActions({
        fetchAssetActivities: 'fetchAssetActivities',
      }),
      pageSelected(p) {
        this.setAssetActivitiesPage(p - 1);
        this.$store.dispatch('fetchAssetActivities', this.currentAsset.id);
      },
      createdAt(activity) {
        return this.timezoneDatetime(activity.createdAt, Vue.prototype.$timezone);
      },
    },
  };
</script>

<style scoped lang="scss">
  .managed-asset-activities:after {
    position: absolute;
    border: 0.0625rem solid $themed-very-fair;
    width: 0;
    height: calc(100% - 3em);
    display: block;
    content: '';
    z-index: 0;
    margin-left: 1.94rem;
    bottom: 1em;
    @media($max: $medium) {
      margin-left: 0.625rem;
    }
  }
  .pagination-position {
    right: 0.625rem;
  }
</style>
