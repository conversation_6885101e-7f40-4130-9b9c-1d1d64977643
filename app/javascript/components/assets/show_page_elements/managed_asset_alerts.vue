<template>
  <div>
    <h5 class="font-weight-normal mb-3">
      Alerts
      <span
        v-if="hasEditPermission"
        class="d-inline-block float-right mr-2"
        data-tc-edit-btn="alerts"
      >
        <i
          v-tooltip="'Edit Alerts'"
          class="nulodgicon-edit clickable h6"
          @click="enableEditing"
        />
      </span>
    </h5>

    <div class="box p-3 mt-3">
      <div
        v-if="paginatedAlerts.length > 0"
        class="w-100"
      >
        <div class="col-12 px-0 mb-4 mt-1">
          <h6>Smart Alerts</h6>
          <div class="alert position-relative row mx-0 p-0 d-flex justify-content-between m-0">
            <div class="box box--flat bg-lighter alert-basic-light col-6">
              <div
                v-if="!diskSmartAlertEnabled"
                class="box--disabled-overlay t-0"
              />
              <div class="box__inner align-self-start d-flex flex-column h-100">
                <material-toggle
                  v-tooltip="'Activate the disk space alert'"
                  class="position-absolute right-0 t-0 mt-2"
                  style="z-index: 10;"
                  :disabled="!diskSmartAlertEnabled"
                  :init-active="diskSmartAlertEnabled"
                  @toggle-sample="diskSmartAlertEnabled = !diskSmartAlertEnabled"
                />
                <div class="row h-100 mr-0 ml-n2">
                  <div class="col-auto text-secondary mt-2 pl-0">
                    <span class="d-inline-flex align-items-center text-center btn-text text-secondary">
                      <span class="btn-icon-circle btn-icon-circle icon--line-height alert-badge">
                        <img
                          class="align-middle pb-1"
                          width="22px"
                          src="https://nulodgic-static-assets.s3.amazonaws.com/images/assets/server.svg"
                        >
                        <img
                          class="tips-icon secondary-icon mb-1"
                          src="https://nulodgic-static-assets.s3.amazonaws.com/images/tips_lightbulb.svg"
                        >
                      </span>
                    </span>
                  </div>
                  <div class="col d-flex flex-column">
                    <div class="row flex-column mx-0">
                      <span class="not-as-small d-inline-block align-bottom">
                        <span class="font-weight-semi-bold text-secondary"> Disk Space </span>
                      </span>
                      <div class="small mb-0 mt-2">
                        <div>Alert when disk space is <strong>{{ assetAlertThreshold }}%</strong> free</div>
                        <div>Current disk usage is {{ diskUsagePercentage }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-12 px-0">
          <h6>Calendar Alerts</h6>
          <div class="alert position-relative row mx-0 p-0 d-flex m-0">
            <div
              v-for="(nextAlert, index) in paginatedAlerts"
              :key="index"
              class="readable-length adjust-length align-middle col-md-3 p-2 text-center text-md-left my-1 mr-2 bg-lighter border-light text-muted rounded"
              :class="alertClass(nextAlert.date)"
            >
              <span class="d-inline-flex align-items-center text-center btn-text text-dark">
                <span class="btn-icon-circle btn-icon-circle-xs icon--line-height alert-badge">
                  <i class="genuicon-alerts align-middle text-secondary h6" />
                </span>
              </span>
              <span class="not-as-small d-inline-block align-bottom">
                <span class="font-weight-semi-bold text-dark ml-2">{{ nextAlert.date }}</span>
              </span>
              <div>
                <div
                  v-if="nextAlert.notes"
                  v-tooltip="{
                    content: nextAlert.notes,
                    boundariesElement: 'body',
                    placement: 'top-start',
                  }"
                  class="small mb-0 mt-2 ml-1 clickable truncate"
                >
                  <span>{{ nextAlert.notes }}</span>
                </div>
                <div 
                  v-else 
                  class="small mb-0 mt-2 ml-1"
                >
                  <span>&nbsp;</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div 
          v-if="pageCount > 1" 
          class="align-self-center mr-3 float-right"
        >
          <paginate
            ref="paginate"
            :click-handler="pageSelected"
            :container-class="'pagination pagination-sm mb-0'"
            :next-class="'next-item'"
            :next-link-class="'page-link'"
            :next-text="'Next'"
            :page-class="'page-item'"
            :page-count="pageCount"
            :page-link-class="'page-link'"
            :prev-class="'prev-item'"
            :prev-link-class="'page-link'"
            :prev-text="'Prev'"
            :selected="alertIndex"
            :force-page="page - 1"
          />
        </div>
      </div>

      <div
        v-else
        class="text-center pt-4 w-100"
      >
        <h4>No alerts yet.</h4>
        <h5
          v-if="hasEditPermission"
          class="text-secondary font-weight-normal"
        >
          <button 
            class="btn btn-lg btn-link"
            @click="enableEditing"
          >
            <i class="nulodgicon-plus-round" />
            Add an alert
          </button>
        </h5>
      </div>
    </div>

    <div v-if="isEdit">
      <alert-date-modal
        :managed-asset="currentAsset"
        :is-edit="isEdit"
        @cancel-edit="cancelEditing"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import permissionsHelper from 'mixins/permissions_helper';
import MaterialToggle from 'components/shared/material_toggle.vue';
import Paginate from 'vuejs-paginate';
import assetAlertUpdateHelper from 'mixins/assets/asset_alert_update_helper';
import alertDateModal from '../alerts_date_modal.vue';

export default {
  components: {
    MaterialToggle,
    alertDateModal,
    Paginate,
  },
  mixins: [permissionsHelper, assetAlertUpdateHelper],
  data() {
    return {
      isEdit: false,
      page: 1,
      alertIndex: 0,
      pageSize: 10,
    };
  },
  computed: {
    ...mapGetters(['currentAsset']),
    hasEditPermission() {
      return this.isWrite && !this.currentAsset.merged;
    },
    alerts() {
      if (this.currentAsset && this.currentAsset.alertDates.length > 0) {
        return this.currentAsset.alertDates.map(alertDate => ({
          date: moment.parseZone(alertDate.date).format('MMMM DD, YYYY'),
          notes: alertDate.notes || '',
        }));
      }
      return [];
    },
    diskSmartAlertEnabled() {
      return this.currentAsset?.smartAlert[0]?.active;
    },
    paginatedAlerts() {
      const start = (this.page - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.alerts.slice(start, end);
    },
    pageCount() {
      return Math.ceil(this.alerts.length / this.pageSize);
    },
    assetAlertThreshold() {
      return parseFloat(this.currentAsset?.smartAlert[0]?.threshold);
    },
  },
  methods: {
    enableEditing() {
      this.isEdit = true;
    },
    cancelEditing() {
      this.isEdit = false;
    },
    alertClass(nextAlertDate) {
      const days = this.daysUntilToday(nextAlertDate);
      if (days <= 5) {
        return 'alert-basic-danger';
      } else if (days <= 10) {
        return 'alert-basic-warning';
      }
      return 'alert-basic-light';
    },
    pageSelected(newPage) {
      this.page = newPage;
      this.alertIndex = newPage - 1;
    },
    daysUntilToday(date) {
      const today = moment().startOf('day');
      const alertDate = moment(date, 'MMMM DD, YYYY').startOf('day');
      return alertDate.diff(today, 'days');
    },
  },
};
</script>

<style lang="scss" scoped>
.tips-icon {
  border: 1px solid #eaeaea;
  position: absolute;
  background-color: rgba(240, 240, 240, 0.85);
  border-radius: 100%;
  padding: 0.1875rem;
  right: 0.625rem;
  top: -0.375rem;
  width: 1.25rem;
}

.alert-basic-danger .alert-badge {
  background: $color-danger-light;
}

.alert-basic-warning .alert-badge {
  background: $color-warning-light;
}

.alert-basic-light {
  border: 1px solid $themed-light !important;

  .alert-badge {
    background: $color-safe;
  }
}

.adjust-length {
  max-width: 16rem;
}

.truncate {
  max-width: 14rem;
}
</style>
