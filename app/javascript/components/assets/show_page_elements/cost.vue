<template>
  <div
    v-if="managedAsset"
    class="box p-3 box--with-title"
  >
    <div class="box__inner">
      <div class="row">
        <div class="col-12 col-md-4 pl-4 mb-4 mb-md-0">
          <div v-if="tempAsset.depreciation && isOldDepreciationType">
            <p class="text-muted mb-1 not-as-small">
              Depreciation
            </p>
            <h6 class="text-dark">
              <span>
                {{ oldDepreciation() }}
              </span>
            </h6>
          </div>
          <p class="text-muted mb-1 not-as-small">
            Purchase Price
          </p>
          <h6 class="text-dark">
            <span
              v-if="tempAsset.cost && tempAsset.cost.purchasePrice"
              data-tc-view="purchase price"
            >
              <sup>$</sup>{{ formatToUnits(tempAsset.cost.purchasePrice, 2) }}
            </span>
            <span v-else-if="!isWrite || mergedAsset">
              none
            </span>
            <span v-else>
              <a
                href="#"
                class="not-as-small"
                data-tc-btn="add cost data"
                @click.stop.prevent="openCostModal"
              >Add cost data</a>
            </span>
          </h6>
          <p class="text-muted mb-1 not-as-small">
            Replacement Cost
          </p>
          <h6 class="text-dark">
            <span
              v-if="tempAsset.cost && tempAsset.cost.replacementCost"
              data-tc-view="replacement cost"
            >
              <sup>$</sup>{{ formatToUnits(tempAsset.cost.replacementCost, 2) }}
            </span>
            <span v-else-if="!isWrite || mergedAsset">
              none
            </span>
            <span v-else>
              <a
                href="#"
                class="not-as-small"
                data-tc-btn="add cost data"
                @click.stop.prevent="openCostModal"
              >Add replacement cost</a>
            </span>
          </h6>
          <p class="text-muted mb-1 not-as-small mt-4">
            Salvage Value
          </p>
          <h6 class="text-dark">
            <span
              v-if="tempAsset.cost && tempAsset.cost.salvage"
              data-tc-view="salvage value"
            >
              <sup>$</sup>{{ formatToUnits(tempAsset.cost.salvage, 2) }}
            </span>
            <span v-else-if="!isWrite || mergedAsset">
              none
            </span>
            <span v-else>
              <a
                href="#"
                class="not-as-small"
                data-tc-btn="add salvage data"
                @click.stop.prevent="openCostModal"
              >Add salvage data</a>
            </span>
          </h6>
          <p class="text-muted mb-1 not-as-small mt-4">
            Useful Life
          </p>
          <h6 class="text-dark">
            <span
              v-if="tempAsset.cost && tempAsset.cost.usefulLife"
              data-tc-view="useful life"
            >
              {{ tempAsset.cost.usefulLife }}
              <span>years</span>
            </span>
            <span v-else-if="!isWrite || mergedAsset">
              none
            </span>
            <span v-else>
              <a
                href="#"
                class="not-as-small"
                data-tc-btn="add salvage data"
                @click.stop.prevent="openCostModal"
              >Add useful life</a>
            </span>
          </h6>
          <p class="text-muted mb-1 not-as-small mt-4">
            Approaching End-of-Life
          </p>
          <h6 class="text-dark">
            <span
              v-if="tempAsset.cost && tempAsset.cost.approachingEndOfLife"
              data-tc-view="approaching end of life"
            >
              {{ tempAsset.cost.approachingEndOfLife }}
              <span>months</span>
            </span>
            <span v-else-if="!isWrite || mergedAsset">
              none
            </span>
            <span v-else>
              <a
                href="#"
                class="not-as-small"
                data-tc-btn="add salvage data"
                @click.stop.prevent="openCostModal"
              >Add approaching end-of-life</a>
            </span>
          </h6>
          <p class="text-muted mb-1 not-as-small mt-4">
            PO#
          </p>
          <h6 class="text-dark">
            <span
              v-if="tempAsset.cost && tempAsset.cost.po"
            >
              {{ tempAsset.cost.po }}
            </span>
            <span v-else-if="!isWrite || mergedAsset">
              none
            </span>
            <span v-else>
              <a
                href="#"
                class="not-as-small"
                data-tc-btn="add salvage data"
                @click.stop.prevent="openCostModal"
              >
                Add PO#
              </a>
            </span>
          </h6>
        </div>
        <div class="col-12 col-md-8">
          <line-chart
            v-if="tempAsset && chartArr.length > 0"
            :chart-data="depreciationChartData"
            :height="300"
          />
          <line-chart
            v-else
            :chart-data="fakeChartData"
            :height="300"
          />
        </div>
      </div>
    </div>

    <sweet-modal
      ref="costAndDepreciationModal"
      v-sweet-esc
      title="Cost & Depreciation"
      width="50%"
      data-tc-title="Cost & Depreciation"
      @close="$emit('close-modal')"
    >
      <template #default>
        <lifecycle-form
          v-if="tempAsset && costAttributes"
          is-asset-form
          :data="costAttributes"
          :reset-values="openCostDepreciationModal"
          @assign-custom-lifecycle="assignCustomLifecycle"
          @un-assign-custom-lifecycle="unAssignCustomLifecycle"
          @lifecycle-data="updateLifecycleData"
        />
        <div class="sweet-buttons sweet-custom-footer pb-0 pt-3 border-top">
          <button
            slot="button"
            class="btn-sm btn btn-link text-secondary mr-2"
            @click.stop="closeModal"
          >
            Cancel
          </button>
          <button
            slot="button"
            class="btn btn-sm btn-primary"
            data-tc-btn="save cost"
            :disabled="isDisabled"
            @click.stop="submitAsset"
          >
            Save
          </button>
        </div>
      </template>
    </sweet-modal>
  </div>
</template>

<script>
  import http from 'common/http';
  import currency from 'mixins/currency';
  import string from 'mixins/string';
  import { mapGetters } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from "mixins/permissions_helper";
  import _cloneDeep from 'lodash/cloneDeep';
  import LifecycleForm from 'components/assets/lifecycle_form.vue';
  import LineChart from './line_chart.vue';

  export default {
    components: {
      LineChart,
      SweetModal,
      LifecycleForm,
    },
    mixins: [currency, string, permissionsHelper],
    props: ['openCostDepreciationModal',
            'managedAsset',
            'disabled'],
    data() {
      return {
        chartArr: [],
        showChart: false,
        disableSaveBtn: false,
        costAttributes: null,
        tempAsset: {
          cost: null,
          costAttributes: null,
          acquisitionDate: null,
          depreciation: null,
        },
      };
    },
    computed: {
      ...mapGetters(['currentAsset']),
      hasFieldsNeededForDepreciation() {
        return this.tempAsset &&
               this.tempAsset.cost &&
               this.tempAsset.cost.purchasePrice &&
               this.tempAsset.cost.salvage &&
               this.tempAsset.cost.usefulLife;
      },
      isOldDepreciationType() {
        return ['declining_balance', 'sum_of_years_digits'].includes(this.tempAsset.depreciation.depreciationType);
      },
      depreciationChartData() {
        const labels = [];
        const length = this.chartArr?.length;
        for (let idx = 0; idx < length; idx += 1) {
          const start = this.tempAsset.acquisitionDate ? moment(this.tempAsset.acquisitionDate) : moment();
          labels.push(start.add(idx, "year").format('YYYY'));
        }
        return {
          labels,
          datasets: [
            {
              pointBackgroundColor: '#2bb673',
              borderWidth: 2,
              borderColor: '#2bb673',
              fill: false,
              pointBorderColor: '#2bb673',
              data: this.chartArr,
            },
          ],
        };
      },
      fakeChartData() {
        return {
          labels: ["2020", "2021", "2022", "2023", "2024", "2025", "2026"],
          datasets: [
            {
              pointBackgroundColor: '#2bb673',
              borderWidth: 2,
              borderColor: '#2bb673',
              fill: false,
              pointBorderColor: '#2bb673',
              data: [0,0,0,0,0,0,0],
            },
          ],
        };
      },
      mergedAsset() {
        return this.currentAsset.merged;
      },
      isDisabled() {
        return this.disabled || this.disableSaveBtn;
      },
    },
    watch: {
      openCostDepreciationModal() {
        if (this.openCostDepreciationModal) {
          return this.openModal();
        }
        return this.closeModal();
      },
      tempAsset() {
        this.updateDepreciation();
        this.getYearlySpendBreakdown();
      },
      managedAsset() {
        this.initCostInfo();
      },
    },
    methods: {
      onWorkspaceChange() {
        this.initCostInfo();
        this.getYearlySpendBreakdown();
      },
      initCostInfo() {
        const asset = _cloneDeep(this.managedAsset);
        this.costAttributes = { ...this.managedAsset.costAttributes };
        this.tempAsset = {
          cost: asset.cost,
          costAttributes: asset.costAttributes,
          acquisitionDate: asset.acquisitionDate,
          depreciation: asset.depreciation,
        };
      },
      updateDepreciation() {
        if (this.tempAsset.costAttributes.assetLifecycleId) {
          this.tempAsset.depreciation = null;
        }
      },
      oldDepreciation() {
        return this.tempAsset?.depreciation?.name;
      },
      updateLifecycleData(costData) {
        this.costAttributes = { ...this.costAttributes, ...costData };
      },
      assignCustomLifecycle(lifecycle) {
        if (!lifecycle) { return; }
        const { purchasePrice, replacementCost, usefulLife, approachingEndOfLife, salvage, po } = lifecycle;
        this.costAttributes = {
          ...this.costAttributes,
          purchasePrice,
          replacementCost,
          usefulLife,
          approachingEndOfLife,
          salvage,
          po,
          lifecycleType: 'custom',
          assetLifecycleId: lifecycle.id,
        };
      },
      unAssignCustomLifecycle() {
        this.costAttributes = {
          ...this.costAttributes,
          lifecycleType: null,
          assetLifecycleId: null,
        };
      },
      openCostModal() {
        this.$emit('open-cost-modal');
      },
      openModal() {
        this.$refs.costAndDepreciationModal.open();
      },
      getYearlySpendBreakdown() {
        if (this.hasFieldsNeededForDepreciation) {
          http
            .get(`/asset_depreciation_breakdown/${this.managedAsset.id}.json`)
            .then((res) => {
              this.chartArr = res.data.yearlyValues;
              this.showChart = true;
            })
            .catch((error) => {
              this.emitError(`Sorry, there was an error getting depreciation data (${error.response.data.message}).`);
            });
        }
      },
      submitAsset() {
        const updatedAsset = {
          ...this.tempAsset,
          costAttributes: { ...this.costAttributes },
        };
        this.$emit('submit', updatedAsset);
      },
      closeModal() {
        this.initCostInfo();
        this.$refs.costAndDepreciationModal.close();
      },
      checkErrors(flag) {
        this.disableSaveBtn = flag;
      },
    },
  };
</script>
