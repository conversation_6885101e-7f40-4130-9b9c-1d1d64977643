<template>
  <div
    v-if="showActions"
    class="asset-import clearfix"
  >
    <div v-if="isAssetTasks">
      <router-link
        :to="{ path: '/automated_tasks?new=true' }"
        class="btn btn-primary float-right"
      >
        <i class="nulodgicon-plus-round mr-1" />
        New Task
      </router-link>
    </div>
    <div
      v-else-if="!isImportPage"
      class="d-flex"
    >
      <div
        v-if="!isVerticalNav"
        class="d-inline-block"
      >
        <router-link
          v-if="isWrite && showImportAction"
          v-tooltip.top="'Import Managed Asset(s)'"
          class="btn btn-link text-secondary mr-3 float-right"
          to="/import_assets"
        >
          <i class="nulodgicon-cloud-upload text-fair mr-1" />
          Import
        </router-link>

        <button
          v-if="showExportAction"
          v-tooltip.top="exportTooltip"
          class="btn btn-link text-secondary float-right"
          :class="{ 'text-muted': !isExportReady }"
          :disabled="!isExportReady"
          @click="startExport"
        >
          <i class="nulodgicon-cloud-download text-fair mr-1" />
          Export
        </button>
      </div>
      <div
        id="addAssetGroup"
        class="d-inline-block"
      >
        <router-link
          v-if="(isWrite || isScoped) && showAssetAction"
          to="/new"
          class="btn btn-primary float-right"
          data-tc-add-assets-btn
        >
          <i class="nulodgicon-plus-round mr-1" />
          Managed Asset
        </router-link>

        <router-link
          v-else-if="isDiscoveryToolsConnectionsPage"
          to="/discovery_tools/connectors"
          class="btn btn-primary float-right"
        >
          <i class="nulodgicon-plus-round mr-1" />
          Add Connector
        </router-link>

        <router-link
          v-if="isWrite && showDiscoverAction"
          to="/discovery_tools/connectors"
          class="btn btn-featured float-right"
          :class="discoverBtnStyle"
        >
          <i class="nulodgicon-android-bulb mr-1" />
          Discover
        </router-link>
      </div>
    </div>
    <div v-else>
      <div class="text-right">
        <a
          class="text-secondary mr-4"
          @click="redirectBack"
        >
          <i class="nulodgicon-arrow-left-c white mr-2" />
          <span>Back to
            <strong>assets</strong>
          </span>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import permissionsHelper from 'mixins/permissions_helper';

  export default {
    mixins: [permissionsHelper],
    props: {
      isAssetsAnalytics: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        exportInProgress: false,
        isExportBlocked: false,
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['isVerticalNav', 'downloadingAssetReports']),
      isExportReady() {
        if (this.exportInProgress || this.isExportBlocked) return false;
        return (
          !this.downloadingAssetReports.length ||
          this.downloadingAssetReports.every( report => report.percentage === 1 )
        );
      },
      isImportPage() {
        return this.$route.path === "/import_assets";
      },
      showActions() {
        return !/^\/discovery_tools\/connectors/.test(this.$route.fullPath) &&
               !/^\/discovery_tools\/probes/.test(this.$route.fullPath) &&
               !/^\/settings/.test(this.$route.fullPath) &&
               !/^\/discovery_tools\/logs/.test(this.$route.fullPath);
      },
      isDiscoveredAssetsPage() {
        return /^\/discovered_assets/.test(this.$route.fullPath);
      },
      discoverBtnStyle() {
        return this.isDiscoveredAssetsPage ? 'btn-primary' : 'btn-outline-primary mr-3';
      },
      isDiscoveryToolsAgentsPage() {
        return this.$route.path === "/discovery_tools/agents";
      },
      isDiscoveryToolsConnectionsPage() {
        return this.$route.path === '/discovery_tools/connections';
      },
      showExportAction() {
        return !this.isAssetsAnalytics &&
               !this.isDiscoveredAssetsPage &&
               !this.isDiscoveryToolsAgentsPage &&
               !this.isAssetTasks &&
               !this.isDiscoveryToolsConnectionsPage;
      },
      showImportAction() {
        return !this.isAssetsAnalytics &&
               !this.isDiscoveryToolsAgentsPage &&
               !this.isDiscoveredAssetsPage &&
               !this.isAssetTasks;
      },
      showDiscoverAction() {
        return !this.isDiscoveryToolsAgentsPage && 
               !this.isAssetTasks && 
               !this.isDiscoveryToolsConnectionsPage;
      },
      showAssetAction() {
        return !this.isDiscoveryToolsAgentsPage &&
               !this.isDiscoveredAssetsPage &&
               !this.isAssetTasks &&
               !this.isDiscoveryToolsConnectionsPage;
      },
      isAssetTasks() {
        return this.$route.path.startsWith("/automated_tasks");
      },
      isPeoplePage() {
        return this.$route.path.includes('people_assets');
      },
      exportTooltip() {
        return this.isPeoplePage ? 'Export People Assets Data' : 'Export Managed Asset Data';
      },
    },
    watch: {
      downloadingAssetReports: {
        handler(reports) {
          if (
            reports.length &&
            reports.every(report => report.percentage === 1)
          ) {
            this.exportInProgress = false;
            this.removedExportBlocked();
          }
        },
        deep: true,
      },
    },
    mounted() {
      this.checkExportBlocked();
      window.addEventListener('storage', this.handleStorageChange);
    },
    beforeDestroy() {
      window.removeEventListener('storage', this.handleStorageChange);
    },
    methods: {
      startExport() {
        this.exportInProgress = true;
        const timeStamp = new Date().getTime();
        const assetDownloadReport = {
          name: this.isPeoplePage ? 'people_in_assets.xlsx' : 'managed_assets.xlsx',
          percentage: 0,
          timestamp: timeStamp,
          dummy: true,
        };

        localStorage.setItem('asset_export_status', JSON.stringify(assetDownloadReport));
        this.$store.dispatch('GlobalStore/updateReportPercentage', assetDownloadReport);
        this.downloadAssetData();
      },
      downloadAssetData() {
        this.$emit('download-asset-data', this.isPeoplePage);
      },
      redirectBack() {
        window.history.length > 2 ? this.$router.go(-1) : this.$router.push('/');
      },
      checkExportBlocked() {
        const item = localStorage.getItem('asset_export_status');
        this.isExportBlocked = !!item;
      },
      removedExportBlocked() {
        localStorage.removeItem('asset_export_status');
        this.isExportBlocked = false;
      },
      handleStorageChange(event) {
        if (event.key === 'asset_export_status') {
          this.checkExportBlocked();
        }
      },
    },
  };
</script>
