<template>
  <div>
    <div v-if="asset.assignmentInformationAttributes">
      <input
        id="managed_asset_assignment_information_attributes_id"
        v-model="asset.assignmentInformationAttributes.id"
        type="hidden"
        name="managed_asset[assignment_information_attributes][id]"
      >
      <div
        v-if="isEditModal"
        class="form-group"
        data-tc-section="asset usage"
      >
        <label class="font-weight-semi-bold">Asset Status</label>
        <div class="d-flex align-items-center justify-content-between mb-5">
          <assets-status-dropdown
            :current-asset="currentAsset"
            @change-status="updateStatus"
          />
          <span
            class="btn btn-text text-muted"
            @click.stop="redirectToStatuses"
          >
            <i class="nulodgicon-external-link text-muted mr-1 clickable external-link-size" />
            Customize Statuses
          </span>
        </div>
      </div>
      <div class="form-group row">
        <div class="col-md-6">
          <label for="current_state_since">In current state since</label>
          <nice-datepicker
            id="current_state_since"
            :value="currentStateSince"
            :name="'managed_asset[assignment_information_attributes][current_state_since]'"
            header-text="In current state since:"
            @input="updateDate('currentStateSince', $event)"
          />
        </div>
        <div class="col-md-6">
          <label for="expected_check_in">Expected check-in date <span class="small text-muted ml-1">(optional)</span></label>
          <nice-datepicker
            id="expected_check_in"
            :value="expectedCheckIn"
            :name="'managed_asset[assignment_information_attributes][expected_check_in]'"
            header-text="Expected check-in date:"
            @input="updateDate('expectedCheckIn', $event)"
          />
        </div>
      </div>
      <div class="form-group multi-users">
        <label for="managed_asset_assignment_information_attributes_used_by_contributor_id">Used by <span class="small text-muted ml-1">(optional)</span></label>
        <contributors-select
          id="used-by-company-user"
          name="managed_asset[assignment_information_attributes][used_by_contributor_id]"
          :value="usedBy"
          :add-new-user="true"
          :allow-multiple="true"
          :placeholder="currentPlaceholders['usedByPlaceHolder']"
          data-tc-used-by
          compact
          inside-modal
          show-assignees
          @open-user-modal="$emit('open-user-modal')"
          @select="usedByContributorIdChanged"
          @remove="usedByContributorIdRemoved"
        />
      </div>

      <div class="form-group multi-users">
        <label for="managed_asset_assignment_information_attributes_managed_by_contributor_id">Managed by <span class="small text-muted ml-1">(optional)</span></label>
        <contributors-select
          id="managed-by-company-user"
          name="managed_asset[assignment_information_attributes][managed_by_contributor_id]"
          :value="managedBy"
          :add-new-user="true"
          :allow-multiple="true"
          :placeholder="currentPlaceholders['managedByPlaceHolder']"
          data-tc-managed-by
          compact
          inside-modal
          show-assignees
          @open-user-modal="$emit('open-user-modal')"
          @select="managedByContributorIdChanged"
          @remove="managedByContributorIdRemoved"
        />
      </div>
    </div>

    <div class="form-group">
      <label for="managed_asset_location_id">Location <span class="small text-muted ml-1">(optional)</span></label>
      <location-options
        :multiple="false"
        :asset-location-id="asset.locationId"
        :managed-asset="managedAsset"
        :module-type="'ManagedAsset'"
        @input="assignLocation"
        @fetch-locations="fetchLocations"
      />
      <div
        v-if="!isEditModal && isSpecificPermission('write', 'CompanyUser')"
        class="form-text text-muted small"
      >
        Location not created yet?
        <a
          id="addNewLocationForm"
          href="#"
          data-tc-location-form
          @click.prevent="$emit('open-location-modal')"
        >Add new location</a>
      </div>
    </div>

    <div class="form-group">
      <add-department
        id="managed_asset_assignment_information_attributes_department"
        name="managed_asset[assignment_information_attributes][department_id]"
        :show-optional-text="true"
        :multiple="false"
        :selected-departments="assetDepartments"
        @input="setDepartment"
      />
    </div>

    <div v-if="isPhoneType">
      <div class="form-group">
        <label for="managed_asset_assignment_information_attributes_phone_number">Phone Number <span class="small text-muted ml-1">(optional)</span></label>
        <phone-number-input
          id="managed_asset_assignment_information_attributes_phone_number"
          :model="phoneModel"
          @updated-model="updatePhone"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import dates from 'mixins/dates';
  import { mapGetters, mapActions } from 'vuex';
  import _cloneDeep from 'lodash/cloneDeep';
  import permissionsHelper from 'mixins/permissions_helper';
  import contributorsSelect from 'components/shared/contributors_select.vue';
  import NiceDatepicker from 'components/shared/nice_datepicker';
  import PhoneNumberInput from '../../shared/phone_number_input.vue';
  import LocationOptions from '../../shared/location_options.vue';
  import AssetsStatusDropdown from '../../shared/assets_status_dropdown.vue';
  import AddDepartment from '../../shared/add_department.vue';

  export default {
    components: {
      contributorsSelect,
      PhoneNumberInput,
      LocationOptions,
      NiceDatepicker,
      AssetsStatusDropdown,
      AddDepartment,
    },
    mixins: [ permissionsHelper, dates ],
    props: {
      managedAsset: {
        type: Object,
        required: true,
      },
      isEditModal: {
        required: false,
        default: false,
      },
      assetType: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        asset: {
          companyAssetStatusId: null,
          locationId: '',
          assignmentInformationAttributes: {},
        },
        currentPlaceholders: { usedByPlaceHolder: 'Select People', managedByPlaceHolder: 'Select People' },
        offset: 0,
        pageSize: 100,
        currentAsset: {},
        assetDepartments: [],
      };
    },
    computed: {
      ...mapGetters(['companyUsersAndGroups']),
      ...mapGetters('GlobalStore', ['locations', 'departments']),
      usedBy() {
        const { usedByContributorId } = this.asset.assignmentInformationAttributes;
        if (!usedByContributorId) {
          return null;
        }
        return { id: usedByContributorId };
      },
      managedBy() {
        const { managedByContributorId } = this.asset.assignmentInformationAttributes;
        if (!managedByContributorId) {
          return null;
        }
        return { id: managedByContributorId };
      },
      phoneModel() {
        return {
          phone: this.asset.assignmentInformationAttributes.phoneNumber || '',
          countryCode: this.asset.assignmentInformationAttributes.phoneNumberCountryCode || '',
          countryCodeNo: this.asset.assignmentInformationAttributes.phoneNumberCountryCodeNumber || '',
        };
      },
      isPhoneType() {
        return this.assetType && ['Mobile', 'Phone'].includes(this.assetType);
      },
      currentStateSince() {
        return this.asset.assignmentInformationAttributes.currentStateSince || '';
      },
      expectedCheckIn() {
        return this.asset.assignmentInformationAttributes.expectedCheckIn || '';
      },
      createUsageHistory() {
        const assignmentInfo1 = this.currentAsset.assignmentInformationAttributes;
        const assignmentInfo2 = this.managedAsset.assignmentInformationAttributes;
        const { currentStateSince, expectedCheckIn } = this.asset.assignmentInformationAttributes;
        const attributesToCompare = [
          'locationId',
          'departmentId',
          'phoneNumber',
          'phoneNumberCountryCode',
          'phoneNumberCountryCodeNumber',
          'usedByContributorId',
          'managedByContributorId',
        ];

        const isSomethingUpdated = attributesToCompare.some(attr => assignmentInfo1[attr] !== assignmentInfo2[attr]);
        const isStatusUpdated = this.currentAsset.status.id !== this.managedAsset.status.id;
        const isCurrentStateSinceUpdated = this.showDate(currentStateSince) !== this.showDate(assignmentInfo2.currentStateSince);
        const isExpectedCheckInUpdated = this.showDate(expectedCheckIn) !== this.showDate(assignmentInfo2.expectedCheckIn);
        return isSomethingUpdated || isStatusUpdated || isCurrentStateSinceUpdated || isExpectedCheckInUpdated;
      },
    },
    watch: {
      managedAsset() {
        this.initAssignmentInfo();
      },
      departments() {
        this.populateDepartments();
      },
    },
    created() {
      this.initAssignmentInfo();
      this.fetchDepartments();
      this.populateDepartments();
    },
    methods: {
      ...mapActions('GlobalStore', ['fetchLocations', 'fetchDepartments']),
      onWorkspaceChange() {
        this.fetchLocations({ offset: this.offset, limit: this.pageSize });
        this.$store.dispatch('fetchCompanyUserAndGroupsOptions', { archived: 'all' });
      },
      initAssignmentInfo() {
        if (this.managedAsset && Object.keys(this.managedAsset).length) {
          const assignmentInfo = this.managedAsset.assignmentInformationAttributes;
          this.currentAsset = _cloneDeep(this.managedAsset);
          this.asset = {
            companyAssetStatusId: this.currentAsset?.status?.id,
            locationId: this.managedAsset.locationId,
            assignmentInformationAttributes: {
              id: assignmentInfo.id,
              locationId: this.managedAsset.locationId,
              departmentId: assignmentInfo.departmentId,
              phoneNumber: assignmentInfo.phoneNumber,
              phoneNumberCountryCode: assignmentInfo.phoneNumberCountryCode,
              phoneNumberCountryCodeNumber: assignmentInfo.phoneNumberCountryCodeNumber,
              usedByContributorId: assignmentInfo.usedByContributorId,
              managedByContributorId: assignmentInfo.managedByContributorId,
              currentStateSince: assignmentInfo.currentStateSince,
              expectedCheckIn: assignmentInfo.expectedCheckIn,
            }};
        }
      },
      populateDepartments() {
        if (!this.currentAsset?.assignmentInformationAttributes?.departmentId || !this.departments?.length) {
          return [];
        }
        const { departmentId } = this.currentAsset.assignmentInformationAttributes;
        this.assetDepartments = this.departments.filter(dept => dept.id === departmentId);
        return this.assetDepartments;
      },
      submitAsset() {
        this.$emit('submit', { asset: this.asset, createUsageHistory: this.createUsageHistory });
      },
      updateStatus(status) {
        this.currentAsset.status = { ...status };
        this.asset.companyAssetStatusId = status.id;
      },
      redirectToStatuses() {
        window.open('/managed_assets/settings/statuses', '_blank');
      },
      assignLocation(location) {
        // eslint-disable-next-line vue/no-mutating-props
        this.managedAsset.location = location;
        this.asset.locationId = location ? location.id : null;
        this.asset.assignmentInformationAttributes.locationId = location ? location.id : null;
        this.$emit('input', this.asset);
      },
      usedByContributorIdChanged(p) {
        this.currentPlaceholders.usedByPlaceHolder = 'Loading...';
        this.asset.assignmentInformationAttributes.usedByContributorId = p.id;
        this.$emit('input', this.asset);
      },
      usedByContributorIdRemoved() {
        this.currentPlaceholders.usedByPlaceHolder = 'Select People';
        this.asset.assignmentInformationAttributes.usedByContributorId = null;
        this.$emit('input', this.asset);
      },
      managedByContributorIdChanged(p) {
        this.currentPlaceholders.managedByPlaceHolder = 'Loading...';
        this.asset.assignmentInformationAttributes.managedByContributorId = p.id;
        this.$emit('input', this.asset);
      },
      managedByContributorIdRemoved() {
        this.currentPlaceholders.managedByPlaceHolder = 'Select People';
        this.asset.assignmentInformationAttributes.managedByContributorId = null;
        this.$emit('input', this.asset);
      },
      setDepartment(department) {
        const departmentId = department ? department.id : null;
        this.assetDepartments = department;
        this.currentAsset.assignmentInformationAttributes.departmentId = departmentId;
        this.asset.assignmentInformationAttributes.departmentId = departmentId;
        this.$emit('input', this.asset);
      },
      updatePhone(phoneData) {
        this.asset.assignmentInformationAttributes.phoneNumber = phoneData.phone;
        this.asset.assignmentInformationAttributes.phoneNumberCountryCode = phoneData.countryCode;
        this.asset.assignmentInformationAttributes.phoneNumberCountryCodeNumber = phoneData.countryCodeNo;
        this.$emit('input', this.asset);
      },
      updateDate(key, date) {
        this.asset.assignmentInformationAttributes[key] = date;
      },
    },
  };
</script>

<style lang="scss" scoped>
form {
  max-width: 480px;
  margin: 0 auto;
}

.multi-users :deep(.multiselect) {
  .multiselect__tags {
    .multiselect__single {
      .row {
        .col-3 {
          max-width: 12%;
        }
      }
    }
  }
}

</style>
