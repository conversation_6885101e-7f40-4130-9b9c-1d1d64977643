<template>
  <div>
    <associated-informations
      v-if="currentAsset"
      ref="associatedInfo"
      module-name="Archive"
      :allowed-item-list="allowedItemList"
      @skip-assignments="archiveAssetWithAssociation"
      @info-assigned="archiveAssetWithAssociation"
      @close="closeAssetArchivalModal"
    />

    <sweet-modal
      ref="singleAssetArchive"
      v-sweet-esc
      title="Before you archive this asset..."
    >
      <template slot="default">
        <h6 class="text-left">
          Are you sure you want to archive this asset? You can unarchive it any time later.
        </h6>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="closeAssetArchivalModal"
      >
        Cancel
      </button>
      <button
        id="modalArchiveBtn"
        slot="button"
        class="btn btn-link text-danger"
        data-tc-modal-asset-archive-btn
        @click.stop="archiveSingleAsset"
      >
        Archive
      </button>
    </sweet-modal>

    <sweet-modal
      ref="multipleAssetsArchive"
      v-sweet-esc
      title="Before you archive these assets..."
    >
      <template slot="default">
        <h6 class="text-left">
          Are you sure you want to archive {{ selectedAssets }} assets? You can unarchive them any time later.
        </h6>
        <div class="bg-lighter p-4 rounded">
          <h6 class="text-cyan not-as-big mb-1 d-inline-block align-bottom">
            Note
          </h6>
          <div class="row col-md">
            <p class="mb-0 pt-1">
              You can assign the associated information of selected managed assets to other managed assets before
              archiving. In order to assign information, you have to archive the managed assets one by one. The
              following attributes can be assigned:
            </p>
            <ul class="p-2 pl-3 mb-0">
              <li>
                <strong>Associated Help Tickets</strong>
              </li>
              <li>
                <strong>Used By</strong>
              </li>
              <li>
                <strong>Managed By</strong>
              </li>
            </ul>
          </div>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="closeAssetArchivalModal"
      >
        Cancel
      </button>
      <button
        id="modalArchiveBtn"
        slot="button"
        class="btn btn-link text-danger"
        data-tc-bulk-asset-archive-btn
        @click.stop="archiveMultipleAssets"
      >
        Archive
      </button>
    </sweet-modal>
  </div>
</template>

<script>
  import { mapGetters, mapMutations } from "vuex";
  import { SweetModal } from 'sweet-modal-vue';
  import http from 'common/http';
  import AssociatedInformations from './associated_informations/associated_informations.vue';

  export default {
    components: {
      AssociatedInformations,
      SweetModal,
    },
    props: {
      assetsArr: {
        type: Array,
        default: () => [],
      },
      statusFilter: {
        type: Object,
        default: () => {},
      },
      allAssetsSelected: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        requestToOpen: false,
      };
    },
    computed: {
      ...mapGetters(["currentAsset", "totalRecord"]),
      allowedItemList() {
        return [
          "Help Ticket",
          "Related Items",
          "Assignments",
        ];
      },
      selectedAssets(){
        if (this.allAssetsSelected) {
          return `all ${this.totalRecord}`;
        }
          return 'selected';
      },
    },
    watch: {
      currentAsset() {
        this.$nextTick(() => {
          if (this.currentAsset && this.requestToOpen) {
            this.$refs.associatedInfo.openModal();
          }
        });
      },
    },
    methods: {
      ...mapMutations(["setAssociatedTicketsHistory", "setPageIndex"]),
      openAssociatedModal(asset) {
        this.setAssociatedTicketsHistory(null);
        this.$store.dispatch("fetchAsset", asset.id);
        this.requestToOpen = true;
      },
      openSingleAssetArchiveModal() {
        this.setAssociatedTicketsHistory(null);
        this.$refs.singleAssetArchive.open();
      },
      openMultipleAssetsArchiveModal() {
        this.setAssociatedTicketsHistory(null);
        this.$refs.multipleAssetsArchive.open();
      },
      archiveAssetWithAssociation() {
        const params = {
          asset: this.currentAsset,
          router: this.$router,
        };
        this.requestToOpen = false;
        this.$refs.associatedInfo.closeModal();
        this.$store.dispatch("archiveAsset", params);
        this.$emit("archived");
      },
      archiveSingleAsset() {
        const params = {
          asset: { id: this.assetsArr[0] },
          router: this.$router,
        };
        this.$refs.singleAssetArchive.close();
        this.$store.dispatch("archiveAsset", params);
        this.$emit("archived");
      },
      archiveMultipleAssets() {
        if (this.assetsArr.length) {
          http
            .post("/bulk_managed_assets/bulk_archive.json", { assetIds: this.assetsArr, allSelected: this.allAssetsSelected, statusFilter: this.statusFilter  })
            .then(res => {
              if (res.data.bulkUpdate){
                this.$emit('bulk-update-modal');
              }
              else {
                this.emitSuccess("Successfully archived assets");
              }
            })
            .catch(() => {
              this.emitError("Sorry, there was an error archiving these assets");
            })
            .finally(() => {
              this.closeAssetArchivalModal();
            });
        } else {
          this.closeAssetArchivalModal();
        }
      },
      closeAssetArchivalModal() {
        this.$refs.singleAssetArchive.close();
        this.$refs.multipleAssetsArchive.close();
        this.requestToOpen = false;
        this.$emit("close");
      },
    },
  };
</script>
