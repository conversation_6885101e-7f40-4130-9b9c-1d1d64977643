<template>
  <div 
    class="help-ticket-list mt-3"
    style="--module-content-margin: 1rem;"
  >
    <div class="helpesk-tickets-wrap">
      <toggle-view
        ref="listViewTable"
        table-type="scrollable"
        table-class="table--spaced"
        settings-path="/list_view_columns"
        :view="viewType"
        :min-width="tableMinWidth(selectedColumnsHeader.length)"
        :show-settings-icon="!mspFlag"
        :is-loading="ticketLoadingStatus"
        :data-count="tickets.length"
        :use-flex-classes="false"
        :additional-classes="{'help-ticket-grid': true && viewType !== 'splitPane', 'few-items': tickets.length <= 2}"
        use-contained-layout
      >
        <template slot="header">
          <div class="row justify-content-between my-n2">
            <div
              v-if="statusFilterValue != 'closed'"
              class="d-flex align-items-center col-auto position-relative my-2"
            >
              <h5 class="box-title">
                <i class="box-title__icon genuicon-nav-tickets" /> Tickets
              </h5>
              <div :class="{ 'loader-and-counter-wrap ml-2.5': viewType !== 'kanban' }">
                <div
                  v-if="!ticketLoadingStatus"
                  class="d-flex align-items-center"
                >
                  <span
                    v-if="selectedTickets.length > 0"
                    class="not-as-small d-flex align-items-center"
                  >
                    ({{ selectedTickets.length }} of {{ viewType === 'kanban' ? totalTicketCount : pageEnd }} selected)
                    <i
                      v-tooltip="{
                        content: `Hold <kbd class='bg-light rounded'>shift</kbd> to quickly select multiple tickets.`
                      }"
                      class="nulodgicon-information-circled text-info base-font-size align-text-top ml-1"
                    />
                  </span>
                  <span
                    v-else-if="pageCount <= 1 && viewType !== 'kanban'"
                    class="not-as-small text-themed-fair"
                  >
                    (Showing {{ totalTicketCount }} of {{ totalTicketCount }})
                  </span>
                  <span
                    v-else-if="viewType !== 'kanban'"
                    class="not-as-small text-themed-fair"
                  >
                    (Showing {{ pageStart }}&dash;{{ pageEnd }} of {{ totalTicketCount }})
                  </span>
                  <sup
                    v-if="!selectedTickets.length"
                    v-tooltip.top="`Closed and Archived tickets will not appear in the list unless their filter is selected or the default 'active' filter is deselected.`"
                    class="genuicon-info-circled align-bottom text-faded-light--unthemed true-small ml-1"
                  />
                </div>
                <div
                  v-else
                  class="d-flex justify-content-start"
                >
                  <pulse-loader
                    color="#0d6efd"
                    size="0.5rem"
                    :loading="true"
                  />
                </div>
              </div>
              <!-- Commenting out the code related to save column settings -->
              <!-- <div
                v-if="viewType == 'kanban'"
                class="ml-2 dropdown d-flex align-items-center"
              >
                <a
                  class="btn btn-text btn-pill text-secondary bg-themed-badge-border form-btn--responsive d-flex align-items-center"
                  @click.stop.prevent="openKanbanSettingDropdown"
                >
                  <i class="nulodgicon-eye text-red mr-1"/>
                  <span class="d-block text-truncate text-capitalize">{{ selectedKanbanSetting.viewName }}</span>
                  <i class="clickable nulodgicon-arrow-down-b text-muted ml-2" />
                </a>

                <basic-dropdown
                  class="dropdown-menu not-as-small dropdown-filter d-block"
                  :show-dropdown="kanbanSettingsDropdown"
                  :position="'bottom-right'"
                  :right="0"
                  @on-blur="kanbanSettingsDropdown = false"
                >
                  <div
                    v-for="setting in savedKanbanSettings"
                    :key="setting.id"
                  >
                    <span
                      class="text-secondary action-dropdown-box pr-1"
                      @click.stop.prevent="changeKanbanBoard(setting)"
                    >
                      <div class="d-flex justify-content-between px-2">
                        <span class="d-flex">{{ setting.viewName }}</span>
                        <i
                          class="nulodgicon-android-close d-flex"
                          @click.stop.prevent="confirmDeleteKanbanSetting(setting)"
                        />
                      </div>
                    </span>
                  </div>
                </basic-dropdown>

                <div
                  v-tooltip="'Save the current columns setting'"
                  class="btn btn-sm ml-2 rounded-circle border save-icon-background"
                  @click.stop.prevent="openSaveSettingModal"
                >
                  <i class="genuicon-save save-icon"/>
                </div>
              </div>

              <transition name="modal">
                <kanban-setting-modal
                  v-if="isKanbanSettingModalVisible"
                  ref="kanbanSettingModal"
                  :saved-kanban-settings="savedKanbanSettings"
                  @close="isKanbanSettingModalVisible = false"
                  @save="saveKanbanSetting"
                />
              </transition>

              <Teleport to="body">
                <sweet-modal
                  ref="deleteKanbanSettingModal"
                  v-sweet-esc
                  title="Before you delete this setting..."
                  @close="verticalContainerClass"
                  @open="verticalContainerClass"
                >
                  <template slot="default">
                    <div class="text-left">
                      <p class="mb-3">
                        Are you sure you want to delete this kanban setting: <strong>{{ settingToDelete && settingToDelete.viewName }}</strong>?
                      </p>
                    </div>
                  </template>
                  <button
                    slot="button"
                    class="btn btn-link text-secondary"
                    @click.stop="$refs.deleteKanbanSettingModal.close"
                  >
                    Cancel
                  </button>
                  <button
                    slot="button"
                    class="btn btn-link text-danger"
                    @click.stop="deleteKanbanSettingConfirmed"
                  >
                    Delete
                  </button>
                </sweet-modal>
              </Teleport> -->

              <span
                v-if="selectedTickets.length > 0"
                class="mb-n1 d-flex d-inline-block mr-2"
              >
                <button
                  v-if="!!selectedTickets.length && !activeTicketsPresent"
                  class="btn btn-xs btn-outline-primary mb-1 ml-2"
                  @click="openDeleteModal"
                >
                  Delete
                </button>
                <button
                  v-if="selectedTickets.length < tickets.length && viewType !== 'kanban'"
                  class="btn btn-xs btn-outline-primary mb-1 ml-2 text-white"
                  data-tc-btn="bulk select all"
                  @click="selectAllTickets"
                >
                  Select all
                </button>

                <button
                  v-if="selectedTickets.length > 0 && viewType !== 'kanban'"
                  class="btn btn-xs btn-warning no-shadow mb-1 ml-2"
                  data-tc-btn="bulk unselect all"
                  @click="unSelectTickets(null)"
                >
                  Unselect all
                </button>
                <bulk-update
                  :workspaces="workspaceOptions"
                  :selected-tickets="bulkUpdateTickets"
                  :is-disabled="isDisabled"
                  :active-tickets-present="activeTicketsPresent"
                  @show-merge-options="showMergeOptions"
                  @open-archive-modal="openArchiveModal"
                  @open-unarchive-modal="openUnarchiveModal"
                  @open-status-modal="openStatusModal"
                  @open-move-ticket-modal="openMoveTicketModel"
                  @open-assignment-modal="openAssignmentModal"
                  @open-canned-response-modal="openCannedResponseModal"
                />
              </span>

              <div
                v-if="!selectedTickets.length"
                v-tooltip="{ placement: 'top-start', content: 'Toggle filter sets with Quick Views' }"
                class="position-relative align-items-center ml-2.5"
              >
                <quick-filters
                  :options="quickViewOptionsComputed"
                  :class="disableLinks"
                  heading="Quick Views"
                  no-filter-label="All Tickets"
                  @input="quickViewChanged"
                  @updated-options="quickViewUpdated"
                  @fetch-quick-filters="fetchQuickViewOptions"
                />
              </div>
            </div>

            <div class="col-auto d-flex align-items-center justify-content-end my-2 ml-auto">
              <span class="d-inline-flex text-right">
                <sort-dropdown
                  v-if="selectedColumnsHeader"
                  :value="sortDropdownValue"
                  :type="activeSortType"
                  :options="selectedColumnsHeader"
                  is-header-variant
                  @input="sortByChanged"
                />
              </span>

              <span v-tooltip="{ placement: 'bottom-start', content: tooltipMessage }">
                <filter-button
                  v-tooltip.bottom="tooltipMessage"
                  class="align-self-end"
                  :class="disableLinks"
                  :active-filters-count="activeFiltersCount[0]"
                  is-header-variant
                  @toggle-filter="toggleFilterMenu"
                />
              </span>

              <dismissible-container
                ref="filterPopupContainer"
                :container-classes="'dismissible-container'"
                popup
                is-right-drawer
                @close-container="closeFiltersModal"
              >
                <div class="h-100">
                  <div class="dismissible-container__sticky-header pb-2 list-view-header">
                    <div class="row justify-content-end m-0 align-items-start mb-1 mt-0">
                      <div class="col p-0">
                        <h5 class="align-middle text-secondary mb-0 h5--responsive break-word mr-2">
                          Ticket Filters
                          <span class="not-as-small text-muted">
                            (<span class="text-info">{{ activeFiltersCount[0] }}</span>)
                          </span>
                        </h5>
                      </div>
                      <div class="mr-n2">
                        <view-type-toggle
                          class="filter-view-type-toggle position-absolute ml-sm-2 ml-md-3 mt-n0.5 mr-3"
                          :class="{'filter-view-type-toggle--grid': currentFilterComponent === 'helpTicketListFiltersGrid'}"
                          :module-name="`helpdesk_filter_view`"
                          tooltip-modifier="Filters"
                          @view-type="setFilterViewType"
                        />
                        <span
                          class="btn btn-light btn-flat btn-icon-circle btn-icon-circle-sm text-secondary ml-2 d-inline-flex justify-content-center align-items-center"
                          data-tc-close-btn="options"
                          @click.prevent.stop="closeFiltersModal"
                        >
                          <i class="nulodgicon-android-close big" />
                        </span>
                      </div>
                    </div>
                    <div
                      v-if="(currentFilterComponent === 'helpTicketListFiltersGrid') && (activeFiltersCount[0] > 0)"
                      class="d-inline-flex justify-content-start mt-1 w-100"
                    >
                      <show-active-filters
                        dropdown-teleport-selector=".dismissible-container__sticky-header"
                        class="justify-content-start"
                        :have-multiple-companies="haveMultipleCompanies"
                        @update:activeQuickFilterId="activeQuickFilterId = $event"
                        @remove-filter="updateQuickFilter"
                      />
                    </div>
                  </div>

                  <transition name="swap-fast-fade">
                    <help-ticket-list-filters-list
                      v-if="currentFilterComponent === 'helpTicketListFiltersList'"
                      ref="filterPopupList"
                      class="filters-list-view"
                      :values="values"
                      :active-filters-count="activeFiltersCount[0]"
                      @refresh-tickets="refreshTickets"
                      @close-container="closeFiltersModal"
                      @update-filter="updateQuickFilter"
                      @update:activeQuickFilterId="activeQuickFilterId = $event"
                      @clear-all="clearAllTicketFilters"
                      @update-close-outside="setIsDateModalOpen"
                    />
                  </transition>

                  <transition name="swap-fast-fade">
                    <help-ticket-list-filters-grid
                      v-if="currentFilterComponent === 'helpTicketListFiltersGrid'"
                      ref="filterPopupGrid"
                      :values="values"
                      :active-filters-count="activeFiltersCount[0]"
                      @refresh-tickets="refreshTickets"
                      @close-container="closeFiltersModal"
                      @update-filter="updateQuickFilter"
                      @update:activeQuickFilterId="activeQuickFilterId = $event"
                      @clear-all="clearAllTicketFilters"
                      @update-close-outside="setIsDateModalOpen"
                    />
                  </transition>
                </div>
              </dismissible-container>

              <view-type-toggle
                class="ml-sm-2 ml-md-3 mt-0"
                :module-name="`helpdesk`"
                :views="modifiedListToggleViews"
                btn-group-class="border-0 btn-group btn-group-sm shadow-none overflow-hidden"
                is-header-variant
                @view-type="setViewType"
              />
            </div>
          </div>
        </template>

        <template slot="subHeader">
          <div class="row justify-content-between align-items-start mb-n3">
            <div class="col-md-auto order-1 d-flex pr-0 mb-3">
              <div class="d-inline-flex flex-grow-1 search-wrap align-items-center">
                <input
                  ref="searchInput"
                  v-model="searchString"
                  type="text"
                  class="form-control not-as-small pr-5 search-input"
                  placeholder="Search by subject, comment, description, ticket number or asset tag"
                  data-tc-ticket-search
                  @input="updateSearch"
                >
                <i class="nulodgicon-ios-search-strong search-input-icon" />
                <div
                  class="searchclear btn btn-round btn-sm"
                  :class="{'has-text': hasSearchText}"
                >
                  <span
                    class="cross-icon lead text-secondary"
                    @click="clearSearch"
                  >&times;</span>
                </div>
              </div>
            </div>

            <div class="col-12 col-xl order-2 order-xl-1 ml-auto mb-3 align-self-center">
              <show-active-filters
                class="mb-n2 mr-n2"
                :is-hd-index-page="true"
                :have-multiple-companies="haveMultipleCompanies"
                :class="disableLinks"
                @toggle-filter="toggleFilterMenu"
                @update:activeQuickFilterId="activeQuickFilterId = $event"
                @remove-filter="updateQuickFilter"
              />
            </div>

            <div class="col-md-auto order-3 order-md-1 ml-auto mb-3 d-flex align-items-center mt-1">
              <span
                v-if="viewType !== 'kanban' && viewType !== 'splitpane' && totalTicketCount > 25"
                class="small d-inline-flex align-items-center"
              >
                <span class="text-secondary smallest mr-1 text-nowrap">
                  Per Page
                </span>
                <select
                  id="filtersPerPage"
                  class="form-control form-control-sm d-inline-block select-per-page-filter"
                  :input="perPage"
                  :value="perPage"
                  data-tc-filters-per-page
                  @input="changePageSize"
                >
                  <option>25</option>
                  <option>50</option>
                  <option>75</option>
                </select>
              </span>

              <div
                v-if="pageCount > 1 && viewType !== 'kanban'"
                class="ml-3"
              >
                <paginate
                  ref="paginate"
                  class="float-right mb-0"
                  :click-handler="pageSelected"
                  :container-class="'pagination pagination-sm mb-0'"
                  :next-class="'next-item'"
                  :next-link-class="'page-link'"
                  :next-text="'Next'"
                  :page-class="'page-item'"
                  :page-count="pageCount"
                  :page-link-class="'page-link'"
                  :prev-class="'prev-item'"
                  :prev-link-class="'page-link'"
                  :prev-text="'Prev'"
                  :selected="page"
                />
              </div>
            </div>
          </div>
          <div 
            v-if="viewType === 'kanban'"
            class="kanban-topbar"
          >
            <div class="kanban-switcher align-items-center bg-themed-light">
              <div class="text-gray true-small">Organize Board by:</div>
              <kanban-options-select
                id="kanban_option"
                ref="kanban"
                v-model="selectedKanbanOption"
                name="kanban"
                class="border-0 w-fit-content kanban-select"
              />
            </div>
            <i
              v-tooltip="'Removed Columns'"
              class="nulodgicon-cog h5 mb-0 text-info-dark clickable"
              @click="toggleSetting"
            />
          </div>
        </template>

        <contextual-suggestion
          ref="contextualSuggestion"
          slot="grid"
          class="d-flex help-ticket-grid-item mt-0"
          :class="{'slide-in-left': slideClassActive }"
          additional-classes="bg-themed-branded-lighter"
          suggestion-name="ticket_list_priority_suggestion"
          :show-container="helpdeskIndexCanCreateTask && prioritySuggestion"
        >
          <div class="col-sm-12 col-md-7">
            <h6 class="text-cyan-dark not-as-small">
              <i class="genuicon-nav-automation text-cyan not-as-small mr-2" />
              Automate your workflow
            </h6>
            <p class="smallest text-dark mb-1">
              Task automations can help streamline your company's flow.
              Turn ticket priority changes into a
              <a
                href="#"
                @click.prevent="$refs.contextualSuggestion.updateSuggestionCounts('create_count')"
              >new task
              </a>
              and save time.
            </p>
          </div>
        </contextual-suggestion>

        <div
          v-for="ticket in tickets"
          :key="ticket.id"
          slot="grid"
          class="d-flex help-ticket-grid-item"
          @click="toggleDetails"
        >
          <transition name="fade">
            <help-ticket
              :value="ticket"
              :selected-tickets="selectedTickets"
              :show-company-tag="companyOptions && companyOptions.length > 1"
              :msp-flag="mspFlag"
              @child-ticket-opener="setChildQuickViewTicket"
              @refresh-ticket="refreshTickets"
              @select-ticket="selectTicket"
              @input="checkInput"
            />
          </transition>
        </div>

        <template v-if="!ticketLoadingStatus && tickets.length > 0">
          <div
            slot="splitPane"
            class="split-pane bg-themed-box-bg border border-themed-moderate-lighter rounded overflow-hidden"
          >
            <div class="split-ticket-list border-right border-themed-moderate-lighter">
              <div
                v-for="ticket in tickets"
                :key="ticket.id"
                class="help-ticket-grid-item position-relative"
              >
                <transition name="fade">
                  <help-ticket
                    :value="ticket"
                    :is-split-pane="true"
                    :selected-tickets="selectedTickets"
                    :is-opened-ticket="ticket.id == quickViewTicketId"
                    :show-company-tag="companyOptions && companyOptions.length > 1"
                    :msp-flag="mspFlag"
                    @child-ticket-opener="setChildQuickViewTicket"
                    @refresh-ticket="refreshTickets"
                    @select-ticket="selectTicket"
                    @input="checkInput"
                    @open-quick-view="openQuickViewTicket"
                    @close-quick-view="closeQuickView"
                  />
                </transition>
              </div>
            </div>

            <div class="ticket-details ml-0 rounded-right">
              <div v-if="enableQuickView && quickViewTicketId && isTicketPresentInList && viewType === 'splitPane'">
                <div :key="quickViewKey">
                  <help-ticket-show
                    is-splitpane-view
                    :is-quick-view="enableQuickView"
                    @close-quick-view="closeQuickView"
                  />
                </div>
              </div>
              <div
                v-else
                class="d-flex justify-content-center align-items-center h-100 flex-column"
              >
                <div>
                  <div class="d-flex align-items-center">
                    <img
                      class="mr-3"
                      src="https://nulodgic-static-assets.s3.amazonaws.com/images/drawer/drawer-helpdesk.svg"
                      width="48"
                    >
                    <h3 class="text-gray empty-splitpane-text font-weight-normal">
                      No ticket selected.
                    </h3>
                  </div>
                  <div class="d-flex justify-content-center align-items-center h-100">
                    <span class="text-info unread-comment-count rounded-pill small px-2">
                      <strong>{{ newTicketsCount }} new tickets</strong> and <strong>{{ unreadCommentsCount }} unread comments</strong>.
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>

        <template
          v-if="!ticketLoadingStatus"
          slot="kanban"
        >
          <div
            class="kanban-board"
            @scroll="onScroll"
          >
            <div
              v-if="!columns || !Array.isArray(columns[selectedKanbanOption]) || columns[selectedKanbanOption].length === 0"
              class="d-flex w-100 justify-content-center h5 py-4"
            >
              No ticket availble as the relevant columns have been removed.
            </div>
            <template v-else>
              <draggable
                v-model="columns[selectedKanbanOption]"
                class="d-flex kanban-columns"
                group="columns"
                :options="{ animation: 200, handle: '.drag-handle-icon', direction: 'horizontal' }"
                @end="onColumnDragEnd"
              >
                <div
                  v-for="(column, index) in columns[selectedKanbanOption]"
                  :key="column.name"
                  class="kanban-column"
                  :class="{
                    'kanban-column--droppable': column.isDragAllowed && isDragStart,
                    'kanban-column--restricted': !column.isDragAllowed && isDragStart,
                  }"
                >
                  <div class="column-header not-as-small font-weight-bold w-100 py-2 px-3 mb-3 d-flex flex-direction-column drag-handle-icon">
                    <div class="d-flex w-100 flex-direction-row justify-content-between align-items-center">
                      <div 
                        class="d-flex align-items-center"
                        :style="{ 
                          '--column-color': column.color, 
                          '--column-light-color': lightenColor(column.color, 85),
                          '--column-dark-color': darkenColor(column.color, 35),
                        }"
                      >
                        <span class="kanban-column-indicator " />
                        <span class="kanban-column-name truncate">
                          {{ column.name }}
                        </span>
                        <span class="kanban-column-count rounded-circle d-flex justify-content-center align-items-center ml-2">
                          {{ getKanbanColumnTicketCount(column.name) }}
                        </span>
                      </div>
                      <div class="d-flex align-items-center">
                        <i
                          class="genuicon-ellipsis-v btn btn-round btn-link text-dark my-n1 mr-n2.5 clickable"
                          @click.stop="toggleDropdown(index, selectedKanbanOption)"
                        />
                      </div>
                    </div>
                    <div>
                      <label
                        v-if="column.tickets.length > 0 && !mspFlag"
                        class="clickable ml-3 mb-0"
                        @click.stop.prevent="allTicketsCheckbox(column)"
                      >
                        <input
                          type="checkbox"
                          class="d-none"
                        >
                        <i
                          class="nulodgicon-checkmark checkbox"
                          :class="{'checkbox-checked': areAllTicketsSelected(column)}"
                        />
                      </label>
                      <label
                        v-else-if="!mspFlag"
                        class="clickable ml-3 mb-0 opacity-50 pointer-events-none"
                      >
                        <input
                          class="d-none"
                          type="checkbox"
                        >
                        <i
                          class="nulodgicon-checkmark checkbox"
                        />
                      </label>
                    </div>
                    <basic-dropdown
                      class="dropdown-menu not-as-small dropdown-filter d-block"
                      :show-dropdown="column.dropdownVisible"
                      :position="'bottom-right'"
                      :right="224"
                      @on-blur="column.dropdownVisible = false"
                    >
                      <div>
                        <span
                          class="text-secondary action-dropdown-box"
                          @click.stop="moveColumn(index, 'left', selectedKanbanOption)"
                        >
                          <i class="nulodgicon-arrow-left-c white ml-2" />
                          <span>Move Column Left</span>
                        </span>
                        <span
                          class="text-secondary action-dropdown-box"
                          @click.stop="moveColumn(index, 'right', selectedKanbanOption)"
                        >
                          <i class="nulodgicon-arrow-right-c white ml-2" />
                          <span>Move Column Right</span>
                        </span>
                        <span
                          class="text-secondary action-dropdown-box border-bottom"
                          @click.stop="removeColumn(index, selectedKanbanOption)"
                        >
                          <i class="nulodgicon-android-close white ml-2" />
                          <span>Remove Column</span>
                        </span>
                        <span
                          class="text-secondary action-dropdown-box"
                          @click.stop="openMoveColumnModal(index, selectedKanbanOption, 'before')"
                        >
                          <span class="ml-2">Move Column Before</span>
                        </span>
                        <span
                          class="text-secondary action-dropdown-box"
                          @click.stop="openMoveColumnModal(index, selectedKanbanOption, 'after')"
                        >
                          <span class="ml-2">Move Column After</span>
                        </span>
                      </div>
                    </basic-dropdown>
                  </div>

                  <draggable
                    v-model="column.tickets"
                    class="ticket-draggable-list"
                    :group="{
                      name: 'tickets',
                      put: column.isDragAllowed,
                    }"
                    :options="{
                      group: 'tickets',
                      handle: '.drag-handle-ticket',
                      ghostClass: column.isDragAllowed ? 'dragging-ghost' : '',
                      animation: column.isDragAllowed ? 200 : 0,
                      'empty-insert-threshold': 20,
                      touchStartThreshold: 5,
                      disabled: isDragDisabled() && !column.isDragAllowed,
                    }"
                    @end="onTicketMove($event)"
                  >
                    <template v-if="column.tickets.length > 0">
                      <div
                        v-for="ticket in column.tickets"
                        :key="ticket.id"
                        :data-ticket-id="ticket.id"
                        class="d-flex help-ticket-grid-item mb-2 drag-handle-ticket"
                        @dragstart="onDragEnter(ticket)"
                      >
                        <help-ticket
                          card-subject-width
                          :value="ticket"
                          :selected-tickets="selectedTickets"
                          :show-company-tag="companyOptions && companyOptions.length > 1"
                          :msp-flag="mspFlag"
                          @child-ticket-opener="setChildQuickViewTicket"
                          @refresh-ticket="refreshTickets"
                          @select-ticket="selectTicket"
                          @input="checkInput"
                        />
                      </div>
                    </template>
                    <template v-if="isLoadingSkeletons && kanbanTicketsLeft">
                      <div
                        v-for="skeleton in 3"
                        :key="`skeleton-${skeleton}`"
                        class="mb-2"
                        :style="{ width: '100%' }"
                      >
                        <kanban-ticket-skeleton />
                      </div>
                    </template>
                  </draggable>
                </div>
              </draggable>
              <sweet-modal
                ref="confirmCloseStatus"
                v-sweet-esc
                title="Before you close this ticket..."
                @close="abortCloseTicket"
              >
                <template slot="default">
                  <div class="text-center">
                    <h6 class="mb-3">
                      If you close this ticket, all its pending tasks will be marked as completed by today.
                    </h6>
                  </div>
                </template>
                <button
                  slot="button"
                  class="btn btn-link text-secondary mr-2"
                  @click.stop="abortCloseTicket"
                >
                  Cancel
                </button>
                <button
                  slot="button"
                  class="btn btn-danger"
                  @click.stop="closeTicket"
                >
                  Yes, close it.
                </button>
              </sweet-modal>
            </template>
          </div>

          <transition name="modal">
            <move-column-modal
              v-if="isMoveColumnModalVisible"
              ref="moveColumnModal"
              :columns="columns[selectedKanbanOption]"
              :selected-option="selectedKanbanOption"
              :current-index="moveColumnTargetIndex"
              :position="moveColumnPosition"
              @close="isMoveColumnModalVisible = false"
              @move="handleMoveColumn"
            />
          </transition>

          <transition name="modal">
            <removed-columns-modal
              v-if="openSetting"
              ref="removedColumnsModal"
              :user-id="currentUserId"
              :removed-cols="removedColumns"
              :selected-option="selectedKanbanOption"
              :selected-setting="selectedKanbanSetting"
              @close="openSetting = false"
              @addRemovedColumns="updateRemovedColumns"
            />
          </transition>
        </template>

        <template slot="list">
          <thead>
            <table-header-row
              ref="tableHeaderRow"
              :table-header="selectedColumnsHeader"
              :draggable-header-preferences="helpTicketPreferences"
              :active-sort="activeSort"
              :active-sort-direction="activeSortDirection"
              :list-table-layout-module-style-override="listTableLayoutModuleStyle"
              :select-all-value="isCheckBoxChecked"
              sort-by-key="name"
              :is-scroll-allow="isDragStart"
              :show-select-all-checkbox-inline="!mspFlag"
              @select-all="allTicketsCheckbox"
              @change="sortTickets"
              @update-order="updateTicketListColumns"
            />
          </thead>
          <tbody v-if="(!ticketLoadingStatus || preventListTableLoadingBehavior) && tickets.length > 0">
            <transition
              v-for="ticket in tickets"
              :key="`condensed-${ticket.id}`"
              name="fade"
            >
              <help-ticket-condensed
                :selected-columns="selectedColumnsHeader"
                :ticket="ticket"
                :selected-tickets="selectedTickets"
                :is-quick-view-ticket="ticket.id == quickViewTicketId"
                :is-quick-view="enableQuickView"
                @refresh-ticket="refreshTickets"
                @click="toggleDetails"
                @select-ticket="selectTicket"
                @child-ticket-opener="setChildQuickViewTicket"
                @open-quick-view="openQuickViewTicket"
                @input="checkInput"
              />
            </transition>
          </tbody>
          <tbody
            v-else
            class="col-8"
          >
            <tr
              v-for="trIndex in placeholderTicketCount"
              :key="`skeleton-${trIndex}`"
              class="w-100 not-as-small"
            >
              <td
                v-for="tdIndex in placeholderTicketRowCount"
                :key="`skeleton-td-${tdIndex}`"
                class="skeleton-td"
              >
                <div class="skeleton skeleton__item w-50" />
              </td>
            </tr>
          </tbody>
        </template>

        <template slot="additionalContent">
          <div
            v-if="pageCount > 1 && viewType !== 'splitPane' && viewType !== 'kanban'"
            class="d-block"
          >
            <paginate
              ref="paginate"
              class="my-3 px-2 float-right"
              :click-handler="pageSelected"
              :container-class="'pagination pagination-sm'"
              :next-class="'next-item'"
              :next-link-class="'page-link'"
              :next-text="'Next'"
              :page-class="'page-item'"
              :page-count="pageCount"
              :page-link-class="'page-link'"
              :prev-class="'prev-item'"
              :prev-link-class="'page-link'"
              :prev-text="'Prev'"
              :selected="page"
            />
          </div>

          <div v-if="enableQuickView && quickViewTicketId && viewType !== 'splitPane'">
            <div :key="quickViewKey">
              <dismissible-container
                ref="quickViewPopupContainer"
                :container-classes="'dismissible-container dismissible-menu--right--short quick-view-container'"
                :container-styles="{width: '41.25rem !important'}"
                :show-close-button="false"
                is-quick-view
                popup
                is-right-drawer
                no-outside-close
                @close-container="closeQuickView"
              >
                <help-ticket-show
                  :is-quick-view="enableQuickView"
                  @close-quick-view="closeQuickView"
                />
              </dismissible-container>
            </div>
          </div>

          <div
            v-if="!ticketLoadingStatus && tickets.length === 0 && viewType !== 'kanban' && !mspFlag"
            class="text-center"
            style="height: 50vh"
          >
            <h3 
              class="text-secondary mb-2 pt-5"
              data-tc-no-tickets
            >
              No tickets present.
            </h3>
            <div class="text-muted">
              <span v-if="activeFiltersCount[0]">
                Try removing some filters
                <span 
                  v-if="isWriteAny"
                  class="mr-1"
                >
                  or
                </span>
              </span>
              <router-link
                v-if="isWriteAny"
                to="/new"
              >
                <i class="nulodgicon-plus-round mr-n0.5" /> 
                <span :class="{ 'text-uppercase': !activeFiltersCount[0] }">a</span>dd a new ticket.
              </router-link>
            </div>
          </div>
          <div
            v-else-if="!ticketLoadingStatus && tickets.length === 0 && mspFlag"
            class="mb-2 pt-5 text-center"
          >
            <h3 class="text-secondary">
              No tickets present.
            </h3>
            Switch to My Company view to create new tickets
          </div>
        </template>
      </toggle-view>
    </div>

    <merge-option-modal
      ref="mergeOptionModal"
      :selected-tickets="bulkUpdateTickets"
      :company-id="companyId"
      @tickets-merged="ticketsMerged"
    />
    <move-ticket-modal
      ref="moveTicketModal"
      :company-id="companyId"
      :workspaces="workspaceOptions"
      default-option="workspace"
      :selected-tickets="bulkUpdateTickets"
      :is-index-page="true"
      @close="clearSelectedTickets"
      @tickets-merged="ticketsMerged"
    />
    <ticket-archive-modal
      ref="ticketArchiveModal"
      :tickets-arr="bulkUpdateTickets"
      :company-ids="companyIds"
      @archived="clearSelectedTickets"
      @close="clearSelectedTickets"
      @archive-tickets="fetchUpdatedTickets"
    />
    <ticket-unarchive-modal
      ref="ticketUnarchiveModal"
      :tickets-arr="bulkUpdateTickets"
      :company-ids="companyIds"
      @archived="clearSelectedTickets"
      @close="clearSelectedTickets"
      @unarchive-tickets="fetchUpdatedTickets"
    />
    <ticket-delete-modal
      ref="ticketDeleteModal"
      :tickets-arr="bulkUpdateTickets"
      :company-ids="companyIds"
      @archived="clearSelectedTickets"
      @close="clearSelectedTickets"
    />
    <help-ticket-close-modal
      id="helpTicketCloseModal"
      ref="helpTicketCloseModal"
      :object="currentTicket"
      :require-time-spent="isTimeFieldRequired"
      :ticket-data="ticketData"
      @handle-ok="handleCloseModal"
      @filter-data="filterData"
    />
    <bulk-update-status
      ref="statusModal"
      :status-options="statusOptions"
      :selected-tickets="bulkUpdateTickets"
      :company-ids="companyIds"
      @close="clearSelectedTickets"
      @update-status="fetchUpdatedTickets"
    />
    <bulk-update-reassignment
      ref="assignmentModal"
      :selected-tickets="bulkUpdateTickets"
      :company-ids="companyIds"
      @close="clearSelectedTickets"
      @update-assignment="fetchUpdatedTickets"
    />
    <bulk-add-canned-response
      ref="cannedResponseModal"
      :selected-tickets="bulkUpdateTickets"
      :company-ids="companyIds"
      :options="cannedResponseOptions"
      @close="clearSelectedTickets"
    />
  </div>
</template>

<script>
  import http from "common/http";
  import _get from "lodash/get";
  import _map from "lodash/map";
  import { mapMutations, mapGetters, mapActions } from 'vuex';
  import Paginate from 'vuejs-paginate';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import search from 'mixins/search';
  import customForms from 'mixins/custom_forms';
  import tableLayoutStyle from 'mixins/table_layout_style';
  import _debounce from 'lodash/debounce';
  import Pusher from 'common/pusher';
  import inflections from "mixins/inflections";
  import channelCleanup from "mixins/custom_forms/channel_cleanup";
  import sortHelper from 'mixins/sorting_helper';
  import filters from 'mixins/filters';
  import suggestions from 'mixins/automated_tasks/suggestions';
  import permissionsHelper from 'mixins/permissions_helper';
  import strings from 'mixins/string';
  import multiCompany from 'mixins/multi_company';
  import _cloneDeep from 'lodash/cloneDeep';
  import _findIndex from "lodash/findIndex";
  import customFormHelper from "mixins/custom_form_helper";
  import pageSizeHelper from 'mixins/page_size_helper';
  import DismissibleContainer from 'components/shared/dismissible_container.vue';
  import activeSortHelper from 'mixins/active_sort_helper';
  import pushers from 'mixins/pushers';
  import QuickFilters from 'components/shared/quick_filters.vue';
  import mspHelper from 'mixins/msp_helper';
  import companyUser from 'mixins/company_user';
  import mspManagingHelper from 'mixins/msp_managing_helper';
  import colorCalculations from 'mixins/color_calculations';
  import { SweetModal } from 'sweet-modal-vue';
  import draggable from 'vuedraggable';
  import HelpTicket from './help_ticket.vue';
  import MoveColumnModal from './move_column_modal.vue';
  // import kanbanSettingModal from './kanban_setting_modal.vue';
  import KanbanOptionsSelect from '../shared/kanban_options_select.vue';
  import RemovedColumnsModal from './removed-columns-modal.vue';
  import BasicDropdown from '../shared/basic_dropdown.vue';
  import MergeOptionModal from './merge_option_modal.vue';
  import HelpTicketCondensed from './help_ticket_condensed.vue';
  import ViewTypeToggle from '../shared/view_type_toggle.vue';
  import ToggleView from '../shared/toggle_view.vue';
  import ContextualSuggestion from '../shared/contextual_suggestion.vue';
  import FilterButton from '../shared/filter_button.vue';
  import TableHeaderRow from "../shared/list_view_table_header.vue";

  // Can use active_filter_multiple when combining active filters of the same type into one filter
  import SortDropdown from '../shared/sort_dropdown.vue';
  import helpTicketListFiltersList from './help_ticket_list_filters_list.vue';
  import helpTicketListFiltersGrid from './help_ticket_list_filters_grid.vue';
  import ShowActiveFilters from '../shared/show_active_filters.vue';
  import TicketArchiveModal from './help_ticket_archive.vue';
  import MoveTicketModal from './show/move_ticket_modal.vue';
  import TicketDeleteModal from './help_ticket_delete.vue';
  import TicketUnarchiveModal from './help_ticket_unarchive.vue';
  import HelpTicketCloseModal from './help_ticket_close_modal.vue';
  import BulkUpdate from './bulk_update.vue';
  import BulkUpdateStatus from './bulk_update_status.vue';
  import BulkUpdateReassignment from './bulk_update_assignment.vue';
  import BulkAddCannedResponse from './bulk_add_canned_response.vue';
  import HelpTicketShow from './show/general/show.vue';
  import KanbanTicketSkeleton from "./loading_states/kanban_ticket_skeleton.vue";

  const ADDITIONAL_SORT_COLUMNS = ["ticket_number", "comment_count", "created_at", "total_time_spent", "updated_at", "date_closed", "workspace_name", "form_used"];

  export default {
    components: {
      KanbanTicketSkeleton,
      HelpTicket,
      HelpTicketCondensed,
      Paginate,
      PulseLoader,
      SortDropdown,
      ViewTypeToggle,
      ToggleView,
      ContextualSuggestion,
      MergeOptionModal,
      helpTicketListFiltersList,
      helpTicketListFiltersGrid,
      QuickFilters,
      FilterButton,
      TableHeaderRow,
      DismissibleContainer,
      ShowActiveFilters,
      TicketArchiveModal,
      TicketUnarchiveModal,
      TicketDeleteModal,
      HelpTicketCloseModal,
      MoveTicketModal,
      BulkUpdate,
      MoveColumnModal,
      // kanbanSettingModal,
      KanbanOptionsSelect,
      RemovedColumnsModal,
      BasicDropdown,
      SweetModal,
      BulkUpdateStatus,
      BulkUpdateReassignment,
      BulkAddCannedResponse,
      HelpTicketShow,
      draggable,
    },
    mixins: [
      channelCleanup,
      customForms,
      filters,
      inflections,
      permissionsHelper,
      search,
      sortHelper,
      suggestions,
      strings,
      tableLayoutStyle,
      customFormHelper,
      pageSizeHelper,
      multiCompany,
      activeSortHelper,
      mspHelper,
      mspManagingHelper,
      pushers,
      companyUser,
      colorCalculations,
    ],
    data() {
      return {
        isLoadingSkeletons: false,
        paramsHash: null,
        currentTicket: null,
        ticketData: null,
        status: null,
        field: null,
        selectedTickets: [],
        viewType: null,
        listenerUp: false,
        formOptions: null,
        slideClassActive: true,
        searchString: '',
        drag: false,
        values: [],
        companyOptions: null,
        listTableLayoutModuleStyle: "help_tickets",
        isCheckBoxChecked: false,
        quickViewOptions: [],
        activeQuickFilterId: null,
        companyId: null,
        statusOptions: null,
        cannedResponseOptions: null,
        currentFilterComponent: 'helpTicketListFiltersList',
        observer: null,
        quickViewKey: false,
        enableQuickView: false,
        quickViewHeight: 0,
        isVerticalNav: null,
        showPerPageOption: false,
        columns: [],
        isMoveColumnModalVisible: false,
        moveColumnTargetIndex: null,
        moveColumnPosition: 'before',
        openSetting: false,
        selectedKanbanOption: "priority",
        currentMode: "priority",
        allColumns: [],
        removedColumns: {},
        settingToDelete: null,
        fieldOptions: null,
        isDragStart: false,
        kanbanSettingsDropdown: false,
        savedKanbanSettings: [],
        selectedKanbanSetting: {},
        isKanbanSettingModalVisible: false,
        customKanbanSettingName: '',
        closeStatusParams: {},
        listToggleViews: [
          { 
            name: 'List', type: 'list_group', iconClass: 'genuicon-list-no-split',
            subViews: [
              { name: 'List View', type: 'list', iconClass: 'genuicon-list-no-split' },
              { name: 'List with Preview', type: 'splitList', iconClass: 'genuicon-list-with-split' },
            ],
          },
          { name: 'Split Pane', type: 'splitPane', iconClass: 'genuicon-split-pane', subViews: null },
          { name: 'Grid', type: 'grid', iconClass: 'genuicon-grid' },
        ],
        selectedColumns: {},
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['userCompanies', 'workspaceOptions', 'currentCompanyUser', 'isMoveTicketModalOpen']),
      ...mapGetters([
        'tickets',
        'page',
        'perPage',
        'loading',
        'filtersSnapShot',
        'filterParams',
        'helpCenterTickets',
        'statusFilter',
        'createdByFilter',
        'priorityFilter',
        'assignedToFilter',
        'managedAssetFilter',
        'dateFilter',
        'dueSoonFilter',
        'companyFilter',
        'formFilter',
        'currentUserId',
        'searchTerms',
        'pageCount',
        'kanbanTicketsLeft',
        'kanbanColumnTicketsCount',
        'prioritySuggestion',
        'ticketLoadingStatus',
        'preventListTableLoadingBehavior',
        'helpTicketPreferences',
        'recentlyClosedFilter',
        'completedTicketFilter',
        'filterByField',
        'isDateColumn',
        'workspaceFilter',
        'previousWorkspaceId',
        'totalTicketCount',
        'assignmentFilter',
        'filtersData',
        'sourceFilter',
        'closedByDateFilter',
        'currentHelpTicket',
        'timeSpents',
        'quickViewTicketId',
        'updatingMultipleFilters',
        'showClosedTickets',
        'companyUserOptions',
        'displayKanbanView',
        'enableTicketDrafts',
        'previousWorkspaceId',
        'unreadCommentsCount',
      ]),
      modifiedListToggleViews() {
        const views = [...this.listToggleViews];
        if (this.displayKanbanView) {
          if (!views.some(view => view.type === 'Kanban')) {
            views.push({ name: 'Kanban', type: 'kanban', iconClass: 'genuicon-kanban', subViews: null });
          }
        }
        return views;
      },
      ticketSources() {
        return ["Manually Added", "Email", "Slack", "Auto Generated", "Ms Teams", "Splitted", "Mobile App", "Desktop App"];
      },
      newTicketsCount() {
        return this.tickets.filter(ticket => this.isAgent && ticket.isNew).length;
      },
      isAgent() {
        return this.currentCompanyUser?.isWorkspaceAgent;
      },
      hasSearchText() {
        return this.searchString.length;
      },
      showQuickViewTicket() {
        return this.enableQuickView && this.quickViewTicketId;
      },
      pageStart() {
        return Math.min(((this.page * this.perPage) + 1), this.totalTicketCount);
      },
      pageEnd() {
        return Math.min(
          (this.page * this.perPage) + this.perPage,
          this.totalTicketCount
        );
      },
      selectedColumnsHeader() {
        if (!this.helpTicketPreferences) {
          return [];
        }
        return this.helpTicketPreferences.map(preference => ({
          title: this.checkDuplicateName(preference, this.columnsTitles),
          label: preference.description,
          name: preference.fieldName,
          fieldType: preference.fieldType || '',
          sortBy: preference.fieldName,
        }));
      },
      columnsTitles() {
        return this.helpTicketPreferences.map(prefe => prefe.description);
      },

      canWriteTicket() {
        return true;
      },
      activeTicketsPresent() {
        return !!this.selectedTickets.find(ticket => ticket.archived === false);
      },
      activeFiltersCount() {
        let count = 0;
        let filterFieldCount = 0;
        if (this.companyFilter?.length > 0 && this.haveMultipleCompanies) {
          count += this.companyFilter.length;
          filterFieldCount += 1;
        }
        if (this.workspaceFilter?.length > 0 && this.haveMultipleWorkspaces) {
          count += 1;
          filterFieldCount += 1;
        }
        if (this.statusFilter?.length > 0) {
          count += this.statusFilter.length;
          filterFieldCount += 1;
        }
        if (this.priorityFilter?.length > 0) {
          count += this.priorityFilter.length;
          filterFieldCount += 1;
        }
        if (this.assignedToFilter?.length > 0) {
          count += this.assignedToFilter.length;
          if (this.assignedToFilter.find(option => option.id === this.$currentContributorId)) {
            count += 1;
          }
          filterFieldCount += 1;
        }
        if (this.filterByField?.length > 0) {
          count += this.filterByField.length;
          filterFieldCount += 1;
        }
        if (this.createdByFilter?.length > 0) {
          count += this.createdByFilter.length;
          if (this.createdByFilter.find(option => option.id === this.$currentContributorId)) {
            count += 1;
          }
          filterFieldCount += 1;
        }
        if (this.dateFilter) {
          count += 1;
          filterFieldCount += 1;
        }
        if (this.dueSoonFilter) {
          count += 1;
          filterFieldCount += 1;
        }
        if (this.recentlyClosedFilter) {
          count += 1;
          filterFieldCount += 1;
        }
        if (this.completedTicketFilter) {
          count += 1;
          filterFieldCount += 1;
        }
        if (this.formFilter?.length > 0) {
          count += this.formFilter.length;
          filterFieldCount += 1;
        }
        if (this.assignmentFilter) {
          count += 1;
          filterFieldCount += 1;
        }
        if (this.sourceFilter?.length > 0) {
          count += this.sourceFilter.length;
          filterFieldCount += 1;
        }
        if (this.closedByDateFilter?.filterType) {
          count += 1;
          filterFieldCount += 1;
        }
        return [count, filterFieldCount];
      },

      defaultTableHeaders() {
        const headers = this.defTableHeaders;
        if (this.isMultipleCompanies) {
          headers.splice(1, 1);
        }
        return headers;
      },

      statusFilterValue() {
        return _get(this, 'statusFilter.id');
      },

      sortDropdownValue() {
        if (!this.activeSort) {
          return null;
        }
        return `${this.activeSort} ${this.activeSortDirection}`;
      },

      isMultipleCompanies() {
        return this.companyGuids && this.companyGuids.length < 2 && this.defTableHeaders[1].title === "Company";
      },
      assignedToFilterContainsToMe() {
        return this.assignedToFilter.some(f => f.name === this.$currentContributorName);
      },
      assignmentFilterContainsUnassigned() {
        const assignmentFilterContainsUnassigned = this.assignmentFilter?.name === "Unassigned";
        return assignmentFilterContainsUnassigned;
      },
      quickViewOptionsComputed() {
        if (this.quickViewOptions.length) {
          const computedQuickViewOptions = [...this.quickViewOptions];
          computedQuickViewOptions.map((o) => {
            o.active = o.id === this.activeQuickFilterId;
            return o;
          });
          return computedQuickViewOptions;
        }
        return [];
      },
      selectedTicketCompanyId() {
        return this.selectedTickets[0].company.id;
      },
      isDisabled() {
        this.setCompanyId();
        if (!this.sameCompanyTickets) {
          return true;
        }
        return false;
      },
      sameCompanyTickets() {
        return this.selectedTickets.every (val => val.company.id === this.companyId );
      },
      companyIds() {
        return _map(this.selectedTickets, 'company.id');
      },
      haveMultipleCompanies() {
        return this.companyOptions?.length > 1;
      },
      haveMultipleWorkspaces() {
        return this.workspaceOptions?.length > 1;
      },
      placeholderTicketCount() {
        return this.tickets?.length || 3;
      },
      placeholderTicketRowCount() {
        return this.selectedColumnsHeader?.length || 8;
      },
      isTimeFieldRequired() {
        if (this.currentTicket) {
          this.setCurrentHelpTicket(this.currentTicket);
          this.$store.dispatch('fetchTimeSpents', this.currentTicket.id);
        }
        return this.currentTicket?.customForm?.moduleForm?.requireTimeSpentToClose &&
               this.currentTicket?.totalTimeSpent === '0 hr 0 min';
      },
      bulkUpdateTickets() {
        if (this.viewType === 'kanban' && this.selectedTickets.length === this.tickets.length) {
          const visibleTickets = [];

          if (Array.isArray(this.columns[this.selectedKanbanOption])) {
            this.columns[this.selectedKanbanOption].forEach(column => {
              const writable = column.tickets.filter(t => t.canWrite === true);
              visibleTickets.push(...writable);
            });
          }

          return visibleTickets;
        }
        return this.selectedTickets;
      },
      isTicketPresentInList() {
        return this.tickets.some((tkt) => tkt.id === this.quickViewTicketId);
      },
    },
    watch: {
      "$pusher": function setupPusherListeners() {
        this.setupPusherListeners();
      },
      tickets() {
        if (this.viewType === 'splitPane') {
          const isQuickViewTicketPresent = this.tickets.some((tkt) => tkt.id === this.quickViewTicketId);
          if (!isQuickViewTicketPresent) {
            this.closeQuickView();
          }
        } else if (this.viewType === 'kanban') {
          this.getKanbanColumns();
        }
      },
      selectedKanbanSetting(newValue) {
        localStorage.setItem(`selectedKanbanSetting-${this.currentUserId}`, JSON.stringify(newValue));
      },
      selectedKanbanOption(newValue) {
        if (this.viewType === "kanban") {
          this.setPage(0);
          this.setPerPage(7);
          this.setKanbanTicketsLeft(true);
          this.fetchTickets({
            kanbanView: {
              [this.selectedKanbanOption]: { companyFilter: this.companyFilter, workspaceFilter: this.workspaceFilter },
            },
          });
          localStorage.setItem(`selectedKanbanOption-${this.currentUserId}`, JSON.stringify(newValue));
        }
      },
      selectedColumnsHeader() {
        let sortFilter = this.selectedColumnsHeader.find(opt => opt.name === this.activeSort && opt.fieldType === this.activeSortType);
        if (!sortFilter) sortFilter = this.selectedColumnsHeader.find(opt => opt.name === 'priority' && opt.fieldType === 'priority');
        if (sortFilter) {
          this.activeSort = sortFilter.name;
          this.activeSortType = sortFilter.fieldType;
        } else {
          this.activeSort = 'ticket_number';
          this.activeSortType = '';
        }
      },
      ticketLoadingStatus() {
        if (!this.ticketLoadingStatus) {
          this.$nextTick(() => {
            this.setQuickTicketHeight();
            this.observeListSize();
            if (this.$route.query.selectedTicket !== `${this.quickViewTicketId}`) {
              this.setQuickViewFromUrl();
            }
          });
        }
      },
    },
    mounted() {
      this.$store.dispatch("fetchAllDrafts");
      const savedSetting = localStorage.getItem(`selectedKanbanSetting-${this.currentUserId}`);
      const savedOption = localStorage.getItem(`selectedKanbanOption-${this.currentUserId}`);
      if (savedSetting) {
        this.selectedKanbanSetting = JSON.parse(savedSetting);
      }
      if (savedOption) {
        this.selectedKanbanOption = JSON.parse(savedOption);
      }
    },
    destroyed() {
      if (this.observer) {
        this.observer.disconnect();
      }
    },
    updated() {
      this.$nextTick(() => {
        const ticketNumberHeaderElement = this.$refs.tableHeaderRow?.$refs['Ticket Number'];
        const statusHeaderElement = this.$refs.tableHeaderRow?.$refs.Status;

        if (ticketNumberHeaderElement) {
          this.safelySetColumnWidth(ticketNumberHeaderElement, this.setTicketNumberColumnWidth, true);
          this.observeColumnWidth(ticketNumberHeaderElement, this.setTicketNumberColumnWidth, true);
        }
        if (statusHeaderElement) {
          this.safelySetColumnWidth(statusHeaderElement, this.setStatusColumnWidth);
          this.observeColumnWidth(statusHeaderElement, this.setStatusColumnWidth);
        }
      });
    },
    created() {
      // prevent page content flashing by telling the content that we are planning to load tickets soon
      this.setTicketLoadingStatus(true);
      this.setFilterComponent();

      this.setCurrentUserId(this.$currentUserId);
      if (Object.keys(this.$route.query).length > 0) {
        this.resetTicketFilters();
      }

      document.addEventListener('toggleVerticalNav', this.setIsVerticalFromEvent.bind(this));
      this.setIsVerticalFromCookie();
    },
    beforeDestroy() {
      this.unbindAll();
      this.paramsHash = null;
    },
    methods: {
      ...mapMutations('GlobalStore', ['setUserCompanies']),
      ...mapMutations([
        'setCustomDateFilter',
        'setFiltersSnapShot',
        'setAssignedToFilter',
        'setCompanyFilter',
        'setCurrentHelpTicket',
        'setCustomFormFilter',
        'setHelpTicketPreferences',
        'setTicketLoadingStatus',
        'setManagedAssetFilter',
        'setPage',
        'setPerPage',
        'setKanbanTicketsLeft',
        'setPriorityFilter',
        'setStatusFilter',
        'setSearchTerms',
        'setSortColumn',
        'setSortDirection',
        'setSortType',
        'setCreatedByFilter',
        'setFilterByField',
        'setDateColumn',
        'setMyTickets',
        'setWorkspaceFilter',
        'setPreviousWorkspaceId',
        'setAssignmentFilter',
        'setFiltersData',
        'setCurrentUserId',
        'setDateFilter',
        'setDueSoonFilter',
        'setRecentlyClosedFilter',
        'setCompletedTicketFilter',
        'setClosedByDateFilter',
        'setSourceFilter',
        'setTicketNumberColumnWidth',
        'setStatusColumnWidth',
        'setLoadingTicket',
        'setQuickViewTicketId',
        'setCurrentWorkspace',
        'setCurrentCompany',
        'setSlaFilter',
      ]),
      ...mapActions([
        'fetchTicketListColumns',
        'updateTicketListColumns',
        'createWindowSession',
        'fetchTickets',
      ]),
      ...mapActions('GlobalStore', ['fetchCurrentCompanyUser']),

      onWorkspaceChange() {
        if (this.mspFlag) {
          const $workspaceMenuToggle = document.querySelector('#workspace_menu_toggle');
          if ($workspaceMenuToggle) {
            $workspaceMenuToggle.classList.remove('workspace-drawer');
            $workspaceMenuToggle.classList.add('hidden');
          }
        }
        if (!this.mspFlag && !this.$route.query?.isMsp) {
          const managementViewToggle = document.querySelector('#management_view');
          const myCompanyViewToggle = document.querySelector('#my_company_view');
          managementViewToggle?.classList.remove('active-view');
          myCompanyViewToggle?.classList.add('active-view');
        }
        this.managementViewEvents();
        this.setQuickViewTicketId(null);
        this.showFirstTimeHelpdesk('index');
        if (this.previousWorkspaceId == null) {
          this.setPreviousWorkspaceId(getWorkspaceFromStorage().id);
        }
        this.unbindAll();
        if (Object.keys(this.$route.query).length > 0) {
          this.setFiltersData({ resetData: true });
        } else {
          this.setFiltersData({ clearAll: true });
        }
        if (!this.$route.query?.isMsp) {
          this.initializeCompanyAndWorkspace();
        }
        this.checkFilterParams();
        this.createWindowSession();
        if (this.userCompanies?.length) {
          this.companyOptions = this.userCompanies;
          this.setupMultiCompanyWorkspacesChannels();
        } else {
          this.fetchCompanyOptions();
        }

        this.searchString = this.searchTerms;
        this.setupWorkspacePusherListeners();
        if (!this.workspaceOptions?.length) {
          this.$store.dispatch('GlobalStore/fetchWorkspaces');
        }

        this.setupPusherListeners();
        if ($workspace && $workspace.id !== this.previousWorkspaceId) {
          this.setPage(0);
          this.setPreviousWorkspaceId($workspace.id);
        };
        if (this.$route.query?.isMsp) {
          this.refreshTickets();
        }
        this.destroyCookieValue();
        this.fetchTicketList();
        this.fetchQuickViewOptions();
        this.checkPerPage("help-tickets", this.setPerPage);
        this.tableHeaders = this.defaultTableHeaders;
        if (!this.currentCompanyUser?.user) {
          this.fetchCurrentCompanyUser({ fetch_permission: false });
        }
        this.closeFiltersModal();

        // Close filter popup
        this.$refs.filterPopupContainer?.forceClose();
      },
      fetchMspData() {
        this.refreshTickets();
      },
      applyDateRangeFilter(start, end) {
        const startDate = moment(start, 'YYYY-MM-DD').startOf('day');
        const endDate = moment(end, 'YYYY-MM-DD').endOf('day');
        this.updateQuickFilter({
          filter: 'setCreatedByDateFilter',
          item: {
            filterType: {
              startDate: startDate.format(),
              endDate: endDate.format(),
              filter: 'custom_date',
            },
          },
        });
      },
      observeListSize() {
        const element = this.$refs.listViewTable?.$el;
        this.observer = new ResizeObserver(entries => {
          entries.forEach(() => {
            this.setQuickTicketHeight();
          });
        });
        if (element) {
          this.observer.observe(element);
        }
      },
      debounceLoadMoreTickets: _debounce(function debounceLoadMoreTickets() {
        if (this.kanbanTicketsLeft) {
          this.isLoadingSkeletons = true;
          this.setPage(this.page + 1);
          this.setPerPage(7);
          this.fetchTickets({
            disableLoading: true,
            kanbanView: {
              [this.selectedKanbanOption]: { companyFilter: this.companyFilter, workspaceFilter: this.workspaceFilter },
            },
          });
        }
      }, 200),
      onScroll(event) {
        const { scrollHeight, scrollTop, clientHeight } = event.target;

        const threshold = 300;

        const nearBottom = scrollHeight - scrollTop - clientHeight <= threshold;

        if (nearBottom) {
          this.debounceLoadMoreTickets();
        }
      },
      toggleSetting() {
        this.openSetting = !this.openSetting;
      },
      handleMoveColumn(payload) {
        const { targetName, position, selectedOption } = payload;

        const columns = this.columns[selectedOption];
        const currentColumn = columns[this.moveColumnTargetIndex];
        const targetIndex = columns.findIndex(col => col.name === targetName);

        const newTargetIndex = position === 'after' ? targetIndex + 1 : targetIndex;

        columns.splice(newTargetIndex, 0, currentColumn);

        if (this.moveColumnTargetIndex > targetIndex) {
          columns.splice(this.moveColumnTargetIndex + 1, 1);
        } else {
          columns.splice(this.moveColumnTargetIndex, 1);
        }

        this.$set(this.columns, selectedOption, [...columns]);

        this.isMoveColumnModalVisible = false;
        this.moveColumnTargetIndex = null;
        this.moveColumnPosition = 'before';
      },
      toggleDropdown(index, selectedOption) {
        const column = this.columns[selectedOption][index];

        if (column) {
          this.$set(this.columns[selectedOption], index, {
            ...column,
            dropdownVisible: !column.dropdownVisible,
          });
        }
      },
      moveColumn(index, direction, selectedOption) {
        this.toggleDropdown(index, selectedOption);
        const columns = this.columns[selectedOption];

        if (!columns || !Array.isArray(columns)) { return; }

        const newColumns = [...columns];

        if (direction === 'left' && index > 0) {
          const temp = newColumns[index];
          newColumns[index] = newColumns[index - 1];
          newColumns[index - 1] = temp;
          this.emitSuccess(`Column has been moved ${direction}`);
        }

        if (direction === 'right' && index < newColumns.length - 1) {
          const temp = newColumns[index];
          newColumns[index] = newColumns[index + 1];
          newColumns[index + 1] = temp;
          this.emitSuccess(`Column has been moved ${direction}`);
        }

        this.$set(this.columns, selectedOption, newColumns);
        this.saveColumnArrangement();
      },
      removeColumn(index, selectedOption) {
        this.toggleDropdown(index, selectedOption);

        const columns = this.columns[selectedOption];
        if (columns && Array.isArray(columns)) {
          const removedColumn = columns.splice(index, 1)[0];

          const savedRemovedColumns = this.removedColumns;

          if (!savedRemovedColumns[selectedOption]) {
            savedRemovedColumns[selectedOption] = [];
          }

          savedRemovedColumns[selectedOption] = [...savedRemovedColumns[selectedOption], removedColumn.name];
          this.removedColumns = savedRemovedColumns;
          this.saveColumnArrangement();
          this.emitSuccess(`Column ${removedColumn.name} has been removed`);
        }
      },
      onColumnDragEnd() {
        this.saveColumnArrangement();
        this.emitSuccess('Column arrangement has been saved');
      },
      saveColumnArrangement() {
        const columnsOrder = {};

        Object.keys(this.columns).forEach((type) => {
          columnsOrder[type] = this.columns[type].map((column) => column.name);
        });

        const userId = this.currentUserId;
        const { firstName } = this.currentCompanyUser.user;

        const viewName = this.customKanbanSettingName === '' ? `${firstName}'s Saved View` : this.customKanbanSettingName.trim();

        const savedSettings = {
          columnsOrder,
          removed: this.removedColumns || {},
          viewName,
        };

        const existingSettings = JSON.parse(localStorage.getItem(`kanbanSettings-${userId}`)) || [];
        existingSettings.push(savedSettings);

        this.savedKanbanSettings = existingSettings;
        this.selectedKanbanSetting = savedSettings;

        localStorage.setItem(`kanbanSettings-${userId}`, JSON.stringify(existingSettings));
      },
      updateRemovedColumns(columns) {
        this.removedColumns = columns || [];
        this.saveColumnArrangement();
        this.loadColumnArrangement(true);
      },
      loadColumnArrangement(columnsUpdated = false) {
        let savedColumnsOrder = [];

        if (this.savedKanbanSettings.length === 0) {
          savedColumnsOrder = JSON.parse(localStorage.getItem(`kanbanSettings-${this.currentUserId}`));
          if (!Array.isArray(savedColumnsOrder) || savedColumnsOrder.length === 0) {
            this.selectedKanbanSetting = { viewName: 'No saved setting' };
          } else {
            this.savedKanbanSettings = savedColumnsOrder;
            if (this.selectedKanbanSetting?.viewName) {
              const matchingSetting = this.savedKanbanSettings.find(
                setting => setting.viewName === this.selectedKanbanSetting.viewName
              );

              if (!matchingSetting) {
                [this.selectedKanbanSetting] = savedColumnsOrder;
              }
            } else {
              [this.selectedKanbanSetting] = savedColumnsOrder;
            }
          }
        }

        this.columns = _cloneDeep(this.allColumns);

        if (!columnsUpdated) {
          this.removedColumns = this.selectedKanbanSetting.removed || {};
        }

        if (this.selectedKanbanSetting?.columnsOrder) {
          Object.keys(this.selectedKanbanSetting.columnsOrder).forEach((type) => {
            if (this.columns[type] && Array.isArray(this.columns[type])) {
              const savedOrder = this.selectedKanbanSetting.columnsOrder[type];
              const currentColumns = this.columns[type];

              const orderedColumns = savedOrder
                .map(name => currentColumns.find(col => col.name === name))
                .filter(col => col !== undefined);

              const remainingColumns = currentColumns.filter(col => !savedOrder.includes(col.name));

              let updatedColumns = [...orderedColumns, ...remainingColumns];
              updatedColumns = updatedColumns?.filter((col) => !this.removedColumns[type]?.includes(col.name));

              this.$set(this.columns, type, updatedColumns);
            }
          });
        } else if (this.removedColumns) {
          Object.keys(this.columns).forEach((type) => {
            const updatedColumns = this.columns[type]?.filter((col) => !this.removedColumns[type]?.includes(col.name));
            this.$set(this.columns, type, updatedColumns);
          });
        }
        if (this.isLoadingSkeletons) {
          this.isLoadingSkeletons = false;
        }
      },
      async getKanbanColumns() {
        try {
          const response = await http.get('/kanban_columns.json', {
            params: { companyFilter: this.companyFilter, workspaceFilter: this.workspaceFilter },
          });

          const { columns } = response.data;

          const groupedTickets = this.tickets.reduce((acc, ticket) => {
            ['status', 'priority'].forEach((field) => {
              const fieldValue = ticket.fields[field]?.value || 'Unspecified';
              const fieldColor = ticket.fields[field]?.color || '#ccc';

              acc[field] = acc[field] || {};
              if (!acc[field][fieldValue]) {
                acc[field][fieldValue] = {
                  name: fieldValue,
                  color: fieldColor,
                  tickets: [],
                  dropdownVisible: false,
                };
              }

              acc[field][fieldValue].tickets.push(ticket);
            });

            acc.sla = acc.sla || {};

            const slaStatus = this.getSlaStatus(ticket);
            if (slaStatus) {
              if (!acc.sla[slaStatus]) {
                acc.sla[slaStatus] = {
                  name: slaStatus,
                  color: '#000000',
                  tickets: [],
                  dropdownVisible: false,
                };
              }
              acc.sla[slaStatus].tickets.push(ticket);
            }

            acc['recent timeframe'] = acc['recent timeframe'] || {};

            const timeframes = this.getTimeframe(ticket);
            timeframes.forEach((timeframe) => {
              if (!acc['recent timeframe'][timeframe]) {
                acc['recent timeframe'][timeframe] = {
                  name: timeframe,
                  color: '#000000',
                  tickets: [],
                  dropdownVisible: false,
                  isDragAllowed: true,
                };
              }
              acc['recent timeframe'][timeframe].tickets.push(ticket);
            });

            return acc;
          }, {});

          const formattedColumns = Object.keys(columns).reduce((acc, key) => {
            const columnOptions = columns[key];
            acc[key] = columnOptions.map((col) => ({
              ...col,
              tickets: (groupedTickets[key]?.[col.name]?.tickets) || [],
              dropdownVisible: false,
              isDragAllowed: true,
            }));
            return acc;
          }, {});

          this.allColumns = formattedColumns;
          this.loadColumnArrangement();
        } catch (error) {
          this.emitError('Error fetching Kanban columns. Please try again.');
        }
      },
      getSlaStatus(ticket) {
        if (!ticket.resolutionTime || ticket.isClosed) { return 'others'; }

        const resolutionTime = new Date(ticket.resolutionTime);
        const now = new Date();

        const isSameDay = (date1, date2) =>
          date1.getFullYear() === date2.getFullYear() &&
          date1.getMonth() === date2.getMonth() &&
          date1.getDate() === date2.getDate();

        const isTomorrow = (date) => {
          const tomorrow = new Date(now);
          tomorrow.setDate(now.getDate() + 1);
          return isSameDay(date, tomorrow);
        };

        if (resolutionTime < now) {
          return 'overdue';
        } else if (isSameDay(resolutionTime, now)) {
          return 'due today';
        } else if (isTomorrow(resolutionTime)) {
          return 'due tomorrow';
        }

        return null;
      },
      onDragEnter(ticket) {
        this.isDragStart = true;
        this.fetchFieldOptions(ticket.customFormId, ticket.fields[this.selectedKanbanOption].fieldName, ticket.company.id);
      },
      fetchFieldOptions(formId, name, companyId) {
        const company = `&company_id=${companyId}`;
        http
          .get(`/custom_form_fields/search.json?custom_form_id=${formId}&name=${name}${company}`)
          .then((res) => {
            this.fieldOptions = res.data;
            this.columns[this.selectedKanbanOption].forEach((col) => {
              col.isDragAllowed = res.data.options.some(opt => opt.name === col.name);
            });
          })
          .catch(() => {
            this.emitError("Sorry, there was an error loading field options.");
          });
      },
      getTimeframe(ticket) {
        const created = new Date(ticket.createdAt);
        const closed = ticket.closedAt ? new Date(ticket.closedAt) : null;
        const resolutionTime = ticket.resolutionTime ? new Date(ticket.resolutionTime) : null;

        const now = new Date();

        const oneDay = 24 * 60 * 60 * 1000;
        const diffCreated = Math.floor((now - created) / oneDay);
        const diffClosed = closed ? Math.floor((now - closed) / oneDay) : null;

        const timeframes = [];
        const twoDaysFromNow = new Date(now.getTime() + 2 * oneDay);

        const isRecentlyClosed = closed ? (diffClosed <= 3) : false;
        if (isRecentlyClosed) timeframes.push('recently closed');

        const isCreatedToday = created.toDateString() === now.toDateString();
        if (isCreatedToday) timeframes.push('created today');

        const yesterday = new Date(now);
        yesterday.setDate(now.getDate() - 1);
        const isCreatedYesterday = created.toDateString() === yesterday.toDateString();
        if (isCreatedYesterday) timeframes.push('created yesterday');

        const isCreatedThisWeek = diffCreated >= 0 && diffCreated <= 7;
        if (isCreatedThisWeek) timeframes.push('created this week');

        const isAlmostDue = (
          !ticket.isClosed &&
          resolutionTime &&
          resolutionTime > now &&
          resolutionTime <= twoDaysFromNow
        );
        if (isAlmostDue) timeframes.push('almost due');

        return timeframes;
      },
      toggleKanbanSetting() {
        this.kanbanSettingsDropdown = !this.kanbanSettingsDropdown;
      },
      openMoveColumnModal(index, selectedOption, position = 'before') {
        this.toggleDropdown(index, selectedOption);

        this.moveColumnTargetIndex = index;
        this.moveColumnPosition = position;
        this.isMoveColumnModalVisible = true;

        this.$nextTick(() => {
          this.$refs.moveColumnModal?.openModal();
        });
      },
      isDragDisabled() {
        return !['status', 'priority'].includes(this.selectedKanbanOption);
      },
      async onTicketMove(evt) {
        this.isDragStart = false;
        const ticketElement = evt.item.closest('.help-ticket-grid-item');
        if (!ticketElement) {
          this.emitError(`Sorry, ticket is not placed correctly in the column`);
          return;
        }
        const { ticketId } = ticketElement.dataset;
        const destinationColumnName = this.columns[this.selectedKanbanOption].find(column =>
        column.tickets.some(ticket => ticket.id.toString() === ticketId)
        ).name;
        const ticket = this.columns[this.selectedKanbanOption]
          .flatMap(column => column.tickets)
          .find(tkt => tkt.id.toString() === ticketId);

        const value = this.selectedKanbanOption === "status" ? destinationColumnName : destinationColumnName.toLowerCase();
        if (!this.fieldOptions || !Array.isArray(this.fieldOptions.options)) {
          this.emitError("Sorry, there was an error loading field options.");
          return;
        }

        const validValue = this.fieldOptions.options.some(opt => opt.name === value);
        if (validValue) {
          if (this.selectedKanbanOption === "status" && destinationColumnName === 'Closed') {
            this.loadUncompletedTasks(ticketId, ticket, destinationColumnName);
          } else {
            this.updateFieldValue(ticketId, ticket, destinationColumnName);
          }
        } else {
          this.emitError(`Sorry, this option ${destinationColumnName} doesn't exist in this ticket's custom form`);
          this.refreshTickets();
        }
      },
      updateFieldValue(ticketId, ticket, destinationColumnName) {
        this.addFormValue(ticketId, {
          custom_form_field_id: ticket.fields[this.selectedKanbanOption].fieldId,
          value: this.selectedKanbanOption === "status" ? destinationColumnName : destinationColumnName.toLowerCase(),
          name: ticket.fields[this.selectedKanbanOption].fieldName,
          company_id: ticket.company.id,
          label: this.capitalize(this.selectedKanbanOption),
        });
      },
      closeTicket() {
        this.$refs.confirmCloseStatus.close();
        const { ticketId, ticket, destinationColumnName } = this.closeStatusParams;
        this.updateFieldValue(ticketId, ticket, destinationColumnName);
      },
      abortCloseTicket() {
        if (this.$refs.confirmCloseStatus.isOpen) {
          this.$refs.confirmCloseStatus.close();
          this.refreshTickets();
        }
      },
      loadUncompletedTasks(ticketId, ticket, destinationColumnName) {
        const params = { uncompleted: true };
        http
          .get(`/tickets/${ticketId}/project_tasks.json`, { params })
          .then((res) => {
            if (res.data.tasks.length > 0) {
              this.closeStatusParams = { ticketId, ticket, destinationColumnName };
              this.$refs.confirmCloseStatus.open();
            } else {
              this.updateFieldValue(ticketId, ticket, destinationColumnName);
            }
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading uncompleted tasks`);
          });
      },
      openSaveSettingModal() {
        this.isKanbanSettingModalVisible = true;
      },
      openKanbanSettingDropdown() {
        if (this.savedKanbanSettings.length > 0) {
          this.kanbanSettingsDropdown = true;
        }
      },
      saveKanbanSetting(newName) {
        this.customKanbanSettingName = newName;
        this.isKanbanSettingModalVisible = false;
        this.saveColumnArrangement();
      },
      changeKanbanBoard(setting) {
        this.selectedKanbanSetting = setting;
        this.kanbanSettingsDropdown = false;
        this.removedColumns = setting.removed;
        this.loadColumnArrangement();
      },
      confirmDeleteKanbanSetting(setting) {
        this.settingToDelete = setting;
        this.$refs.deleteKanbanSettingModal.open();
      },
      deleteKanbanSettingConfirmed() {
        const existingSettings = JSON.parse(localStorage.getItem(`kanbanSettings-${this.currentUserId}`)) || [];
        const updatedSettings = existingSettings.filter((s) => s.viewName !== this.settingToDelete.viewName);
        localStorage.setItem(`kanbanSettings-${this.currentUserId}`, JSON.stringify(updatedSettings));

        this.kanbanSettingsDropdown = false;
        this.savedKanbanSettings = updatedSettings;
        this.selectedKanbanSetting = updatedSettings[0] || { viewName: 'No saved setting' };

        if (updatedSettings.length === 0) {
          this.removedColumns = [];
        }

        this.emitSuccess(`${this.settingToDelete.viewName} setting has been deleted successfully`);
        this.$refs.deleteKanbanSettingModal.close();
        this.loadColumnArrangement(true);
      },
      setQuickViewFromUrl() {
        if (this.$route.query.selectedTicket) {
          this.openQuickViewTicket(this.$route.query.selectedTicket);
        }
      },
      fetchQuickViewOptions() {
        http
          .get('/quick_view_filters.json')
          .then((res) => {
            this.quickViewOptions = res.data.filtersData;
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading quick view filters options.`);
          });
      },
      destroyCookieValue() {
        const cookieValue = this.getCookieValue('help-tickets-per-page');
        if (cookieValue && cookieValue === '100') {
          this.setPerPageInCookie("help-tickets", '25');
        }
      },
      setQuickTicketHeight() {
        if (!this.$refs.listViewTable?.$el) {
          return;
        }
        if (this.$refs.listViewTable.$el.clientHeight < 400) {
          this.quickViewHeight = 600;
        } else {
          this.quickViewHeight = this.$refs.listViewTable.$el.clientHeight + 126;
        }
      },
      setCompanyId() {
        this.companyId = this.selectedTickets[0].company.id;
      },
      openQuickViewTicket(ticketId) {
        this.setQuickViewTicketId(ticketId);
        this.updateUrlWithTicketId(ticketId);
        this.setQuickViewTicket();
      },
      setQuickViewTicket() {
        this.setCurrentCompany(null);
        this.setCurrentWorkspace(null);
        this.setLoadingTicket(true);
        this.quickViewKey = !this.quickViewKey;
        this.$nextTick(() => {
          this.$refs.quickViewPopupContainer?.toggleOpen();
        });
      },
      updateUrlWithTicketId(ticketId) {
        if (!this.urlHasTicketId(ticketId)) {
          this.$router.push({ query: { selectedTicket: ticketId } });
        }
      },
      urlHasTicketId(ticketId) {
        return this.$route.query.selectedTicket && this.$route.query.selectedTicket === ticketId;
      },
      closeQuickView() {
        this.setCurrentCompany(null);
        this.setCurrentWorkspace(null);
        this.setQuickViewTicketId(null);
        const targetRoute = this.isOnlyBasicRead ? '/end_user_tickets' : '/';
        if (this.$route.path !== targetRoute || Object.keys(this.$route.query).length) {
          this.$router.push(targetRoute);
        }
        // Triggering a resize event to call the recalculateTableWidth
        window.dispatchEvent(new Event('resize'));
        this.$nextTick(() => {
          this.$refs.quickViewPopupContainer?.forceClose();
        });
      },
      setFilterComponent() {
        const urlParams = new URLSearchParams(window.location.search);
        const filterVersion = urlParams.get('filter_version');

        this.currentFilterComponent = filterVersion === '1' ? 'helpTicketListFiltersGrid' : 'helpTicketListFiltersList';
      },
      setFilterViewType(viewType) {
        this.currentFilterComponent = viewType === 'grid' ? 'helpTicketListFiltersGrid' : 'helpTicketListFiltersList';
        this.fetchCurrentFilterComponentOptionsWhenOpen();
      },
      fetchCurrentFilterComponentOptionsWhenOpen() {
        this.$nextTick(() => {
          if (this.$refs.filterPopupContainer.isOpen) {
            const currentFilterRef = this.currentFilterComponent === 'helpTicketListFiltersGrid' ? this.$refs.filterPopupGrid : this.$refs.filterPopupList;
            currentFilterRef.ensureFilterOptions();
          }
        });
      },
      safelySetColumnWidth(element, setter, ignorePadding) {
        if (element && element[0] && typeof element[0].style !== 'undefined') {
          this.setElementWidth(element[0], setter, ignorePadding);
        }
      },
      observeColumnWidth(elements, setter, ignorePadding) {
        if (elements && elements.length > 0) {
          const element = elements[0];

          this.observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
              if (mutation.type === "attributes" && mutation.attributeName === "style") {
                this.setElementWidth(element, setter, ignorePadding);
              }
            });
          });

          this.observer.observe(element, { attributes: true });
        }
      },
      // There is no check here that the element exists and is a node with width already set
      // so it is not reccommended to call this method directly.
      // See: safelySetColumnWidth() and observeColumnWidth().
      setElementWidth(element, setter, ignorePadding = false) {
        if (ignorePadding) {
          const computedStyle = window.getComputedStyle(element);
          const { paddingLeft } = computedStyle;
          const { paddingRight } = computedStyle;
          const width = `${parseInt(element.style.width, 10) - parseInt(paddingLeft, 10) - parseInt(paddingRight, 10)}px`;
          setter(width);
        } else {
          setter(element.style.width);
        }
      },
      fetchCompanyOptions() {
        const url = "/company_options.json";
        http
          .get(url, { params: { company_module: 'HelpTicket' } })
          .then((res) => {
            this.companyOptions = res.data.filter(c => c.subdomain !== 'sample');
            this.setUserCompanies(this.companyOptions);
            this.setupMultiCompanyWorkspacesChannels();
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading company options.`);
          });
      },
      fetchTicketList() {
        this.fetchTicketListColumns().then(() => {
          this.checkSortChanges();
        });
      },
      checkSortChanges() {
        const cookieSortItem = this.getCookieValue('helpdesk-sort-item');
        const cookieSortDirection = this.getCookieValue('helpdesk-sort-direction');
        const cookieSortType = this.getCookieValue('helpdesk-sort-type');
        const preferences = this.helpTicketPreferences.map((n) => n.fieldName);
        const activeSort = JSON.parse(localStorage.getItem('active_sort'));

        if (this.sortPresentInCookie() && cookieSortItem && 
            cookieSortDirection && preferences.includes(cookieSortItem)) 
        {
          this.activeSort = cookieSortItem;
          this.activeSortDirection = cookieSortDirection;
          this.activeSortType = ADDITIONAL_SORT_COLUMNS.includes(this.activeSort) ? '' : cookieSortType;
        } else if (preferences.includes(activeSort)) {
          this.activeSort = activeSort;
          if (this.activeSort && !ADDITIONAL_SORT_COLUMNS.includes(this.activeSort)) {
            this.activeSortType = JSON.parse(localStorage.getItem('active_sort_type'));
          }
          if (!this.activeSort) {
            this.activeSort = 'priority';
            this.activeSortType = 'priority';
          }
          this.activeSortDirection = JSON.parse(localStorage.getItem('active_sort_direction'));
          if (!this.activeSortDirection) {
            this.activeSortDirection = 'desc';
          }
        }
        this.setSortColumn(this.activeSort);
        this.setSortDirection(this.activeSortDirection);
        this.setSortType(this.activeSortType);
        this.refreshTickets();
      },
      initializeCompanyAndWorkspace() {
        let company = null;
        let workspace = null;
        let companyJson = null;
        let workspaceJson = null;
        this.resetTicketFilters();
        const storage = window.sessionStorage;
        companyJson = storage.getItem("companyFilter");
        if (companyJson) {
          company = JSON.parse(companyJson);
        }
        workspaceJson = storage.getItem("workspaceFilter");
        if (workspaceJson) {
          workspace = JSON.parse(workspaceJson);
        }
        if (!companyJson || ['null', '[]'].includes(companyJson)) {
          company = getCompanyFromStorage();
        }
        if (!workspaceJson || ['null', '[]'].includes(workspaceJson)) {
          workspace = getWorkspaceFromStorage();
        }
        this.setCurrentWorkspaceAndCompanyFilter();
        if (this.checkFiltersPresent() && Object.keys(this.$route.query).filter(key => key !== 'selectedTicket').length === 0) {
          this.applyFilters();
        } else {
          company = Array.isArray(company) ? company[0] : company;
          workspace = Array.isArray(workspace) ? workspace[0] : workspace;
          this.setCompanyFilter(company);
          this.setWorkspaceFilter(workspace);
          if (!this.checkFiltersPresent()) {
            this.setActiveFilters();
          }
          this.setFiltersData({ filter: "setCompanyFilter", item: company });
          this.setFiltersData({ filter: "setWorkspaceFilter", item: workspace });
        }
      },
      toolTipText(filter) {
        if (!filter || filter.length === 0) {
          return 'All';
        }
        const allFilters = filter.map(item => item.name);
        return allFilters.toString().replace(/,/g, ", ");
      },
      arrowClass(header) {
        let classStr = "";
        if (this.activeSort !== header.sortBy || this.activeSortType !== header.fieldType) {
          classStr += "color-hidden";
        }
        if (this.activeSortDirection === 'asc') {
          classStr += 'nulodgicon-arrow-up-b';
        } else {
          classStr += 'nulodgicon-arrow-down-b';
        }
        return classStr;
      },
      setupPusherListeners() {
        Pusher.then(() => {
          if (this.$pusher && !this.listenerUp) {
            const workspace = getWorkspaceFromStorage();

            this.listenerUp = true;
            if (workspace) {
              this.bindChannel(workspace);
              return;
            }

            const url = `/workspace_options.json`;
            http
              .get(url, { params: { privilege_name: 'HelpTicket' }})
              .then(res => {
                res.data.forEach(w => this.bindChannel(w));
              })
              .catch(() => {
                this.emitError(`Sorry, there was an issue fetching workspaces.  Please refresh the page and try again`);
              });
          }
        });
      },
      resetKanbanAndFetchTickets() {
        this.setPage(0);
        this.setPerPage(7);
        this.setKanbanTicketsLeft(true);
        this.fetchTickets({
          kanbanView: {
            [this.selectedKanbanOption]: { companyFilter: this.companyFilter, workspaceFilter: this.workspaceFilter },
          },
        });
      },

      bindChannel(workspace) {
        const channel = this.$pusher.subscribe(`workspace=${workspace.id}`);

        channel.bind("ticket", () => {
          if (!(this.showQuickViewTicket && this.isMoveTicketModalOpen)) {
            const closedFilter = this.filtersData.some(obj => obj.item.label === "Status" && obj.item.name === "Closed");
            const disableLoading = this.status === 'Closed' && closedFilter === false;
            if (this.viewType === "kanban") {
              this.resetKanbanAndFetchTickets();
            } else {
              this.fetchTickets({
                preventListTableLoadingBehavior: true,
                disableLoading,
              });
            }
          }
        }, this);

        channel.bind("bulk-tickets-deleted", () => {
          if (this.viewType === "kanban") {
            this.resetKanbanAndFetchTickets();
          } else {
            this.fetchTickets();
          }
          this.emitSuccess('Successfully deleted tickets.');
        }, this);

        channel.bind("bulk-tickets-moved", data => {
          if (this.viewType === "kanban") {
            this.resetKanbanAndFetchTickets();
          } else {
            this.fetchTickets();
          }
          this.$refs.moveTicketModal?.close();
          data.sucess ? this.emitSuccess(data.message) : this.emitError(data.message);
        }, this);

        channel.bind("bulk-tickets-assignment", data => {
          if (this.viewType === "kanban") {
            this.resetKanbanAndFetchTickets();
          } else {
            this.fetchTickets();
          }
          this.clearSelectedTickets();
          this.$refs.assignmentModal?.closeModal();
          data.success ? this.emitSuccess(data.message) : this.emitError(data.message);
        }, this);

        channel.bind(`permissions-updated`, data => {
          if (data.contributor_id === this.$currentContributorId) {
            this.emitWarning("Your workspace permissions have been updated. You will be redirected to the tickets page in 3 seconds...");
            setTimeout(() => {
              window.location.href = '/help_tickets';
            }, 3000);
          }
        }, this);
      },
      fetchUpdatedTickets() {
        if (this.viewType === "kanban") {
          this.resetKanbanAndFetchTickets();
        } else {
          this.fetchTickets();
        }
      },
      clearCompanyOptions() {
        this.companyOptions = null;
      },
      sortByChanged(option) {
        const value = option.direction;
        const { type } = option;
        if (!value) {
          this.activeSort = null;
          this.activeSortDirection = null;
          this.activeSortType = null;
        } else {
          const values = value.split(" ");
          [this.activeSort] = values;
          this.checkDateColumn(this.activeSort);
          [this.activeSortDirection] = values.slice(1);
          this.activeSortType = type;
          // Leaving this here vs after the if/else because if no
          //  activeSort is set it should still default to priority vs nothing
          this.setActiveSortInCookie("helpdesk", this.activeSort, this.activeSortDirection, this.activeSortType);
          this.setlocalStorage({activeSort: this.activeSort, activeSortDirection: this.activeSortDirection, activeSortType: this.activeSortType});
        }
        this.setSortColumn(this.activeSort);
        this.setSortDirection(this.activeSortDirection);
        this.setSortType(this.activeSortType);
        this.refreshTickets();
      },
      sortTickets(header) {
        this.checkDateColumn(header.sortBy);
        const { sortBy } = header;
        this.sortItem(header);
        localStorage.setItem('active_sort', JSON.stringify(sortBy));
        localStorage.setItem('active_sort_direction', JSON.stringify(this.activeSortDirection));
        localStorage.setItem('active_sort_type', JSON.stringify(this.activeSortType));
        this.setActiveSortInCookie("helpdesk", sortBy, this.activeSortDirection, this.activeSortType);
        this.setSortColumn(this.activeSort);
        this.setSortDirection(this.activeSortDirection);
        this.setSortType(this.activeSortType);
        this.refreshTickets();
      },
      quickViewUpdated(filtersData) {
        this.quickViewOptions = filtersData;
      },
      getKanbanColumnTicketCount(name) {
        return this.kanbanColumnTicketsCount[name.toLowerCase()] ||
          this.kanbanColumnTicketsCount[Object.keys(this.kanbanColumnTicketsCount).find(key => key.toLowerCase() === name.toLowerCase())] || 0;
      },
      quickViewChanged(value) {
        let changedViewIsActive = false;
        const isAnyQuickFilterActive = this.quickViewOptions.some((filter) => filter.active);

        const quickFilterName = value?.quickFilterName;
        const quickViewid = value?.data?.id;
        this.activeQuickFilterId = quickViewid;
        this.quickViewOptions.forEach((option, i) => {
          if (option.id === quickViewid) {
            changedViewIsActive = !this.quickViewOptions[i].active;
          }
        });

        if (changedViewIsActive) {
          if (!isAnyQuickFilterActive) {
            this.setFiltersSnapShot(this.getActiveFilters());
          }
          this.setFiltersData({ resetData: true });
          this.resetTicketFilters();

          if (quickFilterName === "My Active Tickets") {
            this.clearWorkspaceFilter();
            this.setAssignedToMeFilter();
            this.setActiveFilters();
          }
          if (quickFilterName === "Drafts" && this.enableTicketDrafts) {
            this.clearWorkspaceFilter();
            this.setDraftsFilters();
          }
          if (quickFilterName === "Unassigned") {
            this.clearWorkspaceFilter();
            this.toggleUnassignedFilters();
            this.setActiveFilters();
          }
          if (quickFilterName === "All Active Tickets") {
            this.clearWorkspaceFilter();
            this.setActiveFilters();
          }
          if (quickFilterName === "Closed") {
            this.clearWorkspaceFilter();
            this.setClosedFilters();
          }
          if (value.data.filters) {
            this.clearWorkspaceFilter();
            this.setCustomQuickView(value.data);
          }
        } else {
          this.activeQuickFilterId = null;
          this.removeQuickAndApplyPrevFilters();
        }
        this.refreshTickets();
      },
      setCustomQuickView(data) {
        const { location } = this.$route.query;
        data.filters.forEach((filter) => {
          const removePrevFilters = false;
          [filter.values].flat().forEach((value) => {
            const setFilter = this[filter.methodName];
            let obj = null;
            if (filter.label === 'Timeframe') {
              if (value.filterType === 'custom_date') {
                obj = { value: value.value, filterType: { startDate: value.startDate, endDate: value.endDate, filter: value.filterType, name: value.name }};
              } else {
                obj = { name: value.name, value: value.value, filterType: value.filterType, removePrefilter: removePrevFilters, location };
              }
            } else if (filter.label === 'Workspace') {
              obj = value;
            } else {
              obj = { id: value.id, name: value.name, label: filter.label, removePrefilter: removePrevFilters, location };
            }
            setFilter(obj);
            this.setFiltersData({ filter: filter.methodName, item: obj });
          });
        });
      },
      removeQuickAndApplyPrevFilters() {
        this.resetTicketFilters();
        this.setFiltersData({ resetData: true });
        this.setFiltersToLocalStorage();
        this.applyFilters();
      },
      setFiltersToLocalStorage() {
        localStorage.setItem(`user ${this.currentUserId}-workspace ${getWorkspaceFromStorage().id}`, this.filtersSnapShot);
      },
      setCurrentWorkspaceAndCompanyFilter() {
        // essentially sets the current workspace and company as default filters only "on workspace change" 
        const workspaceToSave = getWorkspaceFromStorage();
        const companyToSave = getCompanyFromStorage();

        if (workspaceToSave.id === this.previousWorkspaceId) return;

        const activeFilters = JSON.parse(localStorage.getItem(`user ${this.currentUserId}-workspace ${workspaceToSave.id}`));

        const workspaceFilter = { filter: "setWorkspaceFilter", item: workspaceToSave };
        const companyFilter = { filter: "setCompanyFilter", item: companyToSave };

        let filtersToSave = [];

        if (activeFilters?.length) {
          const activeFiltersToSave = activeFilters.filter( filter => 
            filter.filter !== 'setCompanyFilter' && filter.filter !== 'setWorkspaceFilter'
          );

          filtersToSave = [...activeFiltersToSave, companyFilter, workspaceFilter];
        } else {
          filtersToSave = [companyFilter, workspaceFilter];
        }
        if (filtersToSave.length === 0) return;

        localStorage.setItem(`user ${this.currentUserId}-workspace ${workspaceToSave.id}`, JSON.stringify(filtersToSave));
      },
      setAssignedToMeFilter() {
        this.setAssignedToFilter({ id: this.$currentContributorId, name: this.$currentContributorName, label: "Assigned to" });
        this.setMyTickets(true);
        this.setFiltersData({ filter: 'setAssignedToFilter', item: { id: this.$currentContributorId, name: this.$currentContributorName, label: "Assigned to" }});
        this.setFiltersData({ filter: 'setMyTickets', item: true });
      },
      clearMyTicketsFilters() {
        this.setAssignedToFilter([]);
        this.setStatusFilter({removePrefilter: true });
      },
      clearWorkspaceFilter() {
        this.updateQuickFilter({ filter: 'setWorkspaceFilter', item: null });
      },
      setActiveFilters() {
        this.setFiltersData({ filter: "setStatusFilter", item: { id: 'Active', name: 'Active', label: "Status", removePrefilter: true }});
        this.setStatusFilter({ id: 'Active', name: 'Active', label: "Status", removePrefilter: true });
      },
      setDraftsFilters() {
        this.setFiltersData({ filter: "setStatusFilter", item: { id: 'Drafts', name: 'Drafts', label: "Status", removePrefilter: true }});
        this.setStatusFilter({ id: 'Drafts', name: 'Drafts', label: "Status", removePrefilter: true });
      },
      setClosedFilters() {
        this.setFiltersData({ filter: "setStatusFilter", item: { id: 'Closed', name: 'Closed', label: "Status", removePrefilter: true }});
        this.setStatusFilter({ id: 'Closed', name: 'Closed', label: "Status", removePrefilter: true });
      },
      toggleUnassignedFilters() {
        this.setFiltersData({ filter: 'setAssignmentFilter', item: { id: "Unassigned", name: "Unassigned", label: "Assignment" }});
        this.setAssignmentFilter({ id: "Unassigned", name: "Unassigned", label: "Assignment" });
      },
      setViewType(type) {
        if (type === 'kanban' || this.viewType === 'kanban') {
          if (type === "kanban") {
            this.setPage(0);
            this.setPerPage(7);
            this.setKanbanTicketsLeft(true);
            this.fetchTickets({
              kanbanView: {
                [this.selectedKanbanOption]: { companyFilter: this.companyFilter, workspaceFilter: this.workspaceFilter },
              },
            });
          } else {
            this.setPage(0);
            this.setPerPage(25);
            this.fetchTickets();
          }
        }
        this.viewType = type;
        this.enableQuickView = this.viewType === 'splitList' || this.viewType === 'splitPane';
        this.setQuickViewTicketId(null);
      },
      toggleFilterMenu() {
        this.$refs.filterPopupContainer.toggleOpen();
        this.fetchCurrentFilterComponentOptionsWhenOpen();
      },
      closeFiltersModal() {
        this.$refs.filterPopupGrid?.onBlur();
        this.$refs.filterPopupContainer?.forceClose();
      },
      selectTicket(ticket) {
        if (ticket.canWrite || ticket.fields.createdBy.some(creator => creator.id === this.$currentContributorId)) {
          const idx = this.selectedTickets.findIndex(selectedTicket => selectedTicket.id === ticket.id);
          if (idx > -1) {
            this.selectedTickets.splice(idx, 1);
          } else {
            this.selectedTickets.push(ticket);
          }
        }
        this.areAllTicketsSelected();
      },
      openArchiveModal() {
        this.$refs.ticketArchiveModal.helpCenterArchiveModal();
      },
      openUnarchiveModal() {
        this.$refs.ticketUnarchiveModal.helpCenterUnarchiveModal();
      },
      openDeleteModal() {
        this.$refs.ticketDeleteModal.helpCenterDeleteModal();
      },
      openMoveTicketModel() {
        if (!this.workspaceOptions?.length) {
          this.$store.dispatch('GlobalStore/fetchWorkspaces');
        }
        this.$refs.moveTicketModal.open();
      },
      openAssignmentModal() {
        this.$refs.assignmentModal.open();
      },
      openCannedResponseModal() {
        this.fetchCannedResponses();
        this.$refs.cannedResponseModal.open();
      },
      openStatusModal() {
        this.fetchStatusOptions();
        this.$refs.statusModal.open();
      },
      clearSelectedTickets() {
        this.isCheckBoxChecked = false;
        this.selectedTickets = [];
      },
      selectAllTickets(column = null) {
        if (this.viewType === 'kanban') {
          if (column) {
            const writable = column.tickets.filter(t => t.canWrite);
            const existingIds = new Set(this.selectedTickets.map(t => t.id));
            const newTickets = writable.filter(t => !existingIds.has(t.id));
            
            this.selectedTickets = [...this.selectedTickets, ...newTickets];
            this.$set(this.selectedColumns, column.name, true);
          } else {
            const visibleTickets = [];
  
            this.columns[this.selectedKanbanOption].forEach(col => {
              const writable = col.tickets.filter(t => t.canWrite === true);
              visibleTickets.push(...writable);
            });
  
            this.selectedTickets = visibleTickets;
          }
        } else {
          this.selectedTickets = this.tickets.filter(({ canWrite }) => canWrite === true);
        }
        if (this.selectedTickets.length === this.tickets.length) this.isCheckBoxChecked = true;
      },
      allTicketsCheckbox(column = null) {
        if (column) {
          if (this.selectedColumns[column.name] && this.areAllTicketsSelected(column)) {
            this.unSelectTickets(column);
          } else {
            this.selectAllTickets(column);
          }
        } else {
          this.isCheckBoxChecked ? this.unSelectTickets() : this.selectAllTickets();
        }
      },
      areAllTicketsSelected(column = null) {
        if (column) {
          const writable = column.tickets.filter(t => t.canWrite);
          return writable.length && writable.every(t => this.selectedTickets.some(st => st.id === t.id));
        }
        if (this.selectedTickets.length === this.tickets.length) {
          this.isCheckBoxChecked = true;
        } else {
          this.isCheckBoxChecked = false;
        }
        return this.isCheckBoxChecked;
      },
      unSelectTickets(column = null) {
        if (column) {
          this.selectedTickets = this.selectedTickets.filter(
            t => !column.tickets.some(ct => ct.id === t.id)
          );
          this.$set(this.selectedColumns, column.name, false);
        } else {          
          this.isCheckBoxChecked = false;
          this.selectedTickets = [];
        }
      },
      ticketsMerged() {
        this.isCheckBoxChecked = false;
        this.selectedTickets = [];
        this.setPage(0);
        this.refreshTickets();
      },
      checkDateColumn(column) {
        if (column !== "created_at") {
          const result = this.selectedColumnsHeader.find(col => col.name === column && col.fieldType === "date");
          if (result) {
            this.setDateColumn(true);
          } else {
            this.setDateColumn(false);
          }
        } else {
          this.setDateColumn(false);
        }
      },
      checkFilterParams() {
        const managedAssetId = this.$route.query.managed_asset_id;
        const managedAssetName = this.$route.query.managed_asset_name;
        const { priority, location, assignment, sourceId } = this.$route.query;
        const source = this.$route.query.source?.replaceAll('-', ' ');
        const myTickets = this.$route.query.my_tickets;
        const assignedToId = this.$route.query.assigned_to_id;
        const assignedToName = this.$route.query.assigned_to_name;
        const customFormId = this.$route.query.custom_form_id;
        const customFormName = this.$route.query.custom_form_name;
        const createdById = this.$route.query.created_by_id;
        const createdByName = this.$route.query.created_by_name;
        const last90Days = this.$route.query.last_90_days;
        const { startDate, endDate } = this.$route.query;
        const statusParam = this.$route.query.status;

        if (statusParam) { 
          this.applyStatusFilters(statusParam , location);
        }
        if (last90Days) {
          this.setDateFilter({name: 'Last 90 Days', value: 3, filterType: 'months' ,removePrefilter: true, location });
          this.setFiltersData({ filter: 'setDateFilter', item: { filterType : "months", name: "Last 3 months", value: 3 }});
          this.applyStatusFilters(statusParam , location);
        }
        if (startDate && endDate) { 
          this.applyDateRangeFilter(startDate, endDate);
        }

        if (managedAssetId && managedAssetName) {
          this.setFilterByField({ id: managedAssetId, name: `Asset is ${managedAssetName}` });
          this.setFiltersData({ filter: 'setFilterByField', item: { id: managedAssetId, name: `Asset is ${managedAssetName}` }});
        }
        if (priority) {
          this.setPriorityFilter({ id: priority, name: this.titleize(priority), label: "Priority", location });
          this.setFiltersData({ filter: 'setPriorityFilter', item: { id: priority, name: this.titleize(priority), label: "Priority", location }});
        }
        if (source) {
          const id = sourceId || this.ticketSources.indexOf(source);
          const data = { id, name: this.titleize(source), label: 'Source', removePrefilter: true };
          this.setSourceFilter(data);
          this.setFiltersData({ filter: 'setSourceFilter', item: data });
        }
        if (myTickets) {
          this.setAssignedToFilter({ id: this.$currentContributorId, name: this.$currentContributorName, label: "Assigned to", location });
          this.setMyTickets(true);
          this.setFiltersData({ filter: 'setAssignedToFilter', item: { id: this.$currentContributorId, name: this.$currentContributorName, label: "Assigned to", location }});
          this.setFiltersData({ filter: 'setMyTickets', item: true });
        }
        if (assignedToId && assignedToName) {
          this.setAssignedToFilter({ id: assignedToId, name: assignedToName, label: "Assigned to", location });
          this.setFiltersData({ filter: 'setAssignedToFilter', item: { id: assignedToId, name: assignedToName, label: "Assigned to", location }});
        }
        if (createdById && createdByName) {
          this.setCreatedByFilter({ id: createdById, name: createdByName, label: "Created By", location });
          this.setFiltersData({ filter: 'setCreatedByFilter', item: { id: createdById, name: createdByName, label: "Created By", location }});
        }
        if (customFormId && customFormName) {
          this.setCustomFormFilter({ id: customFormId, name: customFormName, label: "Form", removePrefilter: true, location });
          this.setFiltersData({ filter: 'setCustomFormFilter', item: { id: customFormId, name: customFormName, label: "Form", removePrefilter: true, location }});
        }
        if (assignment) {
          this.setAssignmentFilter({ id: assignment, name: assignment, label: "Assignment" });
          this.setFiltersData({ filter: 'setAssignmentFilter', item: { id: assignment, name: assignment, label: "Assignment" }});
        }
      },
      applyStatusFilters(statuses ,location)  {
        const statusList = Array.isArray(statuses) ? statuses : [statuses?.replaceAll('-', ' ')].filter(Boolean);

        statusList.forEach((status, index) => {
          const item = { id: status, name: this.titleize(status), label: "Status", removePrefilter: index === 0, location };
          this.setStatusFilter(item);
          this.setFiltersData({ filter: 'setStatusFilter', item });
        });
      },
      close() {
        if (this.$refs.tickets) {
          this.$refs.tickets.forEach(ticket => ticket.close());
        }
      },
      saveFiltersToStorage() {
        const storage = window.sessionStorage;
        if (this.companyFilter) {
          storage.setItem("companyFilter", JSON.stringify(this.companyFilter));
        }
        if (this.workspaceFilter) {
          storage.setItem("workspaceFilter", JSON.stringify(this.workspaceFilter));
        }
      },
      // We're going to add a debounce here to help alleviate the load on the server
      refreshTickets() {
        if (this.mspFlag) {
          const companies = JSON.parse(sessionStorage.getItem('selectedCompanies'));
          const params = {
            mspFlag: true,
            companiesIds: companies,
            showClosed: this.showClosedTickets,
          };

          if (this.viewType === "kanban") {
            this.setPage(0);
            this.setPerPage(7);
            params.kanbanView = {
              [this.selectedKanbanOption]: { companyFilter: this.companyFilter, workspaceFilter: this.workspaceFilter },
            };
          }

          this.fetchTickets(params);
          return;
        }
        const jsonData = JSON.stringify(this.filterParams);
        if (this.paramsHash && jsonData === this.paramsHash && !('msp_flag' in this.filterParams)) {
          return;
        }
        this.paramsHash = jsonData;
        this.saveFiltersToStorage();
        this.clearSelectedTickets();
        if (this.viewType === "kanban") {
          this.setPerPage(7);
          this.fetchTickets({
            kanbanView: {
              [this.selectedKanbanOption]: { companyFilter: this.companyFilter, workspaceFilter: this.workspaceFilter },
            },
          });
        } else {
          this.fetchTickets();
        }
      },
      showMergeOptions() {
        this.$refs.mergeOptionModal.open();
      },
      pageSelected(p) {
        this.setPage(p - 1);
        this.close();
        this.fetchTickets();
      },
      toggleDetails(event) {
        const e = event;
        if (e.ticket === null) {
          return;
        }
        const ticketToUpdate = e.ticket;
        ticketToUpdate.showDetails = !ticketToUpdate.showDetails;
        this.tickets.forEach((ticket) => {
          if (ticket.id === ticketToUpdate.id) {
            if (ticket.showDetails) {
              this.helpCenterTickets.push(ticketToUpdate.id);
            } else {
              const idx = this.helpCenterTickets.indexOf(ticketToUpdate.id);
              this.helpCenterTickets.splice(idx, 1);
            }
          }
        });
        this.refreshTickets();
      },

      changePageSize(e) {
        this.setPerPage(e.currentTarget.value);
        this.setPage(0);
        this.setPerPageInCookie("help-tickets", this.perPage);
        this.fetchTickets();
      },
      updateSearch: _debounce(
        function updateSearchFunction() {
          // Because we are debouncing the input, we have to get the full value
          // from the input field.  Small price...
          this.setTicketLoadingStatus(true);
          this.setSearchTerms(this.searchString);
          this.setPage(0);
          this.refreshTickets();
        },
        1000
      ),
      clearSearch(){
        this.setTicketLoadingStatus(true);
        this.setSearchTerms('');
        this.searchString = '';
        this.setPage(0);
        this.refreshTickets();
      },
      capitalize(str) {
        if (!str) return '';
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
      },
      setlocalStorage(activeSortValue) {
        if (activeSortValue.activeSort) {
          localStorage.setItem("active_sort", JSON.stringify(activeSortValue.activeSort));
        }
        if (activeSortValue.activeSortDirection) {
          localStorage.setItem("active_sort_direction",JSON.stringify(activeSortValue.activeSortDirection));
        }
        if (activeSortValue.activeSortType) {
          localStorage.setItem("active_sort_type",JSON.stringify(activeSortValue.activeSortType));
        }
      },
      updateQuickFilter(data) {
        const updatedData = { ...data };

        if (updatedData.item && updatedData.item.removePrefilter !== undefined) {
          updatedData.item.removePrefilter = false;
        }
        this.$store.commit('setFiltersData', updatedData);
        this.$store.commit(updatedData.filter, updatedData.item);
        this.setPage(0);
        this.setKanbanTicketsLeft(true);
        this.setFilterValue(updatedData);
        // Prevents sending multiple server requests when updating multiple filters simultaneously
        if (!this.updatingMultipleFilters && !this.activeQuickFilterId) {
          this.refreshTickets();
        }
      },
      checkInput(params) {
        const updatedParams = { ...params };

        this.status = updatedParams.status;
        this.field = updatedParams.field;
        updatedParams.preventListTableLoadingBehavior = true;

        if (updatedParams.status === "Closed") {
          this.fetchTicket(updatedParams);
        } else {
          this.updateStatus(updatedParams.object.id);
        }
      },
      updateStatus(currentTicketId) {
        const ticketIndex = _findIndex(this.tickets, (item) => item.id === currentTicketId);

        if (ticketIndex > -1) {
          this.addFormValue(currentTicketId, {
            custom_form_field_id: this.tickets[ticketIndex].fields.status.fieldId,
            value: this.status,
            name: this.tickets[ticketIndex].fields.status.name,
            company_id: this.tickets[ticketIndex].company.id,
          });
        }
      },
      fetchTicket(params) {
        const { id } = params.object;
        const company = { company_id: params.object.company.id };

        return http
          .get(`/help_tickets/${id}.json`, { params: company })
          .then((res) => {
            this.currentTicket = res.data;
            this.filterData(res.data);
          })
          .catch((error) => {
            this.emitError(_get(error, "response.data.message", ""));
          });
      },
      fetchStatusOptions() {
        const customFormIds = this.selectedTickets.map(ticket => ticket.customFormId);
        http
          .put('/bulk_help_tickets/status_options.json', { custom_form_ids: customFormIds })
          .then(res => {
            this.statusOptions = res.data.options;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error fetch status options.');
          });
      },
      fetchCannedResponses() {
        http
          .put('/bulk_help_tickets/canned_responses.json')
          .then(res => {
            this.cannedResponseOptions = res.data.options;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error fetch status options.');
          });
      },
      filterData(helpTicket) {
        const requiredForCloseAndEmpty = [];

        helpTicket.customForm.formFields.forEach((formField) => {
          if (formField.requiredToClose) {
            const fieldValues = helpTicket.values.filter((value) => value.customFormFieldId === formField.id);
            if (fieldValues.length === 0) {
              requiredForCloseAndEmpty.push(formField);
            }
          }
        });

        this.ticketData = _cloneDeep(helpTicket);
        this.ticketData.customForm.formFields = requiredForCloseAndEmpty;
        if (this.ticketData.customForm.formFields.length > 0 || this.isTimeFieldRequired) {
          this.$refs.helpTicketCloseModal.open();
        } else {
          this.updateStatus(this.currentTicket.id);
        }
        this.setTicketLoadingStatus(false);
      },
      handleCloseModal() {
        const params = { object: this.currentTicket, status: this.status, field: this.field };

        this.fetchTicket(params).then(() => {
          if (this.ticketData.customForm.formFields.length === 0 && !this.isTimeFieldRequired) {
            this.$refs.helpTicketCloseModal.close();
            this.updateStatus(this.currentTicket.id);
          } else {
            this.emitError("The fields which are required to close the ticket cannot be empty.");
          }
        });
      },
      updateValues(filter) {
        const valueIndex = this.values.findIndex(value => value.id === filter.id && value.name === filter.name);
        if (valueIndex === -1) {
          this.values.push(filter);
        } else {
          this.values.splice(valueIndex, 1);
        }
      },
      resetTicketFilters() {
        if (this.companyFilter.length > 0) {
          this.setCompanyFilter({ removePrefilter: true });
        }
        if (this.workspaceFilter.length > 0) {
          this.setWorkspaceFilter({ removePrefilter: true });
        }
        this.setSlaFilter({ clearAll: true });
        this.setDueSoonFilter(null);
        this.setDateFilter(null);
        this.setRecentlyClosedFilter(null);
        this.setCompletedTicketFilter(null);
        this.setClosedByDateFilter(null);
        if (this.assignmentFilter) {
          this.setAssignmentFilter(this.assignmentFilter);
        }
        this.setFilterByField([]);
        if (this.priorityFilter.length > 0) {
          this.setPriorityFilter(false);
          // this.setPriorityFilter({ id: this.priorityFilter[0].id, name: this.titleize(this.priorityFilter[0].name), label: "Priority" });
        }
        this.setAssignedToFilter([]);
        this.setCreatedByFilter([]);
        this.setMyTickets(false);
        this.setCustomFormFilter([]);
        if (this.statusFilter.length > 0) {
          this.setStatusFilter({removePrefilter: true });
        }
        if (this.sourceFilter.length > 0) {
          this.setSourceFilter({ removePrefilter: true });
        }
      },
      clearAllTicketFilters() {
        this.setFiltersData({ resetData: true });
        this.resetTicketFilters();
        this.clearWorkspaceFilter();
        this.refreshTickets();
      },
      setChildQuickViewTicket(ticket) {
        if (this.enableQuickView) {
          this.setCurrentWorkspace(ticket.workspaceId);
          this.setCurrentCompany(ticket.company.id);
          this.setQuickViewTicketId(ticket.id);
        }
        this.childCompanyTicketOpener(ticket);
      },
      childCompanyTicketOpener(ticket) {
        if (this.enableQuickView) {
          this.setLoadingTicket(true);
          this.quickViewKey = !this.quickViewKey;
          if (this.viewType === 'splitPane') {
            this.updateUrlWithTicketId(ticket.id);
          } else {
            this.$nextTick(() => {
              this.$refs.quickViewPopupContainer?.toggleOpen();
            });
          }
        } else {
          const url = `/user_accesses.json?company_id=${ticket.company.id}&redirect_route=/help_tickets/${ticket.id}?workspace_id=${ticket.workspaceId}parent`;
          http.get(url)
            .then((res) => {
              window.open(res.data.url, '_blank');
            });
        }
      },
      checkFiltersPresent() {
        const activeFilters = localStorage.getItem(`user ${this.currentUserId}-workspace ${getWorkspaceFromStorage().id}`);
        return activeFilters != null;
      },
      getActiveFilters() {
        return localStorage.getItem(`user ${this.currentUserId}-workspace ${getWorkspaceFromStorage().id}`);
      },
      applyFilters() {
        let activeFilters = this.getActiveFilters();
        if (activeFilters) {
          activeFilters = JSON.parse(activeFilters);
          for (let idx = 0; idx < activeFilters.length; idx += 1) {
            this.setFilterValue(activeFilters[idx]);
            this.setFiltersData(activeFilters[idx]);
            this.$store.commit(activeFilters[idx].filter, activeFilters[idx].item);
          }
        }
      },
      setFilterValue(data) {
        if (data.item && data.item.name) {
          let { name } = data.item;
          if (data.item.label) {
            name = `${data.item.label} ${name}`;
          }
          this.updateValues({ id: data.item.id, name });
        }
      },
      checkFieldType(field) {
        if (field.fieldType && field.fieldType === 'people_list') {
          return 'peopleList';
        }
        return field.title;
      },
      setupMultiCompanyWorkspacesChannels() {
        const childCompanies = this.companyOptions.filter((c) => c.resellerCompanyId === Number(this.$currentCompanyId));
        for (let idx = 0; idx < childCompanies.length; idx += 1) {
          childCompanies[idx].workspaceIds.forEach(w => this.bindChannel({ id: w }));
        }
      },
      setIsVerticalFromEvent(e) {
        const isVerticalNav = e ? e.detail : undefined;
        this.isVerticalNav = isVerticalNav;
      },
      setIsVerticalFromCookie() {
        const cookieData = document.cookie.split('; ').find(row => row.startsWith('nav-theme'));
        this.isVerticalNav = cookieData?.split('=')[1] === 'vertical';
      },
      setIsDateModalOpen(val) {
        this.$refs.filterPopupContainer.setIsDateModalOpen(val);
      },
    },
  };
</script>

<style scoped lang="scss">

// You can't mix units in Sass, so we'll try to preemptively do as much of the calc as possible
$main-offset-width: ($drawer-collapse-width + ($container-padding-lg * 2));
$xl-grid-item: 32rem;
$xxl-grid-item: 42rem;
$min-grid-item-width: 24rem;
$padding-box: 1.25;
$one-third-percent: 33.33%;
$min-cols: 3;
$gapless-xl-width: $main-offset-width + ($min-cols * $xl-grid-item);
$gapless-xxl-width: $main-offset-width + ($min-cols * $xxl-grid-item);
$gap: 1rem;
$gap-width: ($min-cols - 1) * $gap;
$kanban-gap: 1.5rem;

.helpesk-tickets-wrap :deep(.help-ticket-grid) {
  display: grid;
  gap: #{$gap};
  grid-template-columns: repeat(auto-fit, minmax(#{$min-grid-item-width}, 1fr));

  @media only screen and ($max: map-get($grid-breakpoints, 'lg')) {
    grid-template-columns: repeat(auto-fit, minmax(#{$min-grid-item-width}, 1fr)) !important;
  }

  &.few-items {
    grid-template-columns: repeat(auto-fit, minmax(#{$min-grid-item-width}, calc(#{$one-third-percent} - #{$padding-box}rem)));

    .content-wide-theme & {
      @media (min-width: calc(#{$gapless-xl-width} + #{$gap-width})) {
        grid-template-columns: repeat(auto-fit, minmax(#{$xl-grid-item}, #{$xl-grid-item}));
      }

      @media (min-width: calc(#{$gapless-xxl-width} + #{$gap-width})) {
        grid-template-columns: repeat(auto-fit, minmax(#{$xxl-grid-item}, #{$xxl-grid-item}));
      }
    }
  }
}

.help-ticket-splitpane {
  width: 30% !important;
}

.empty-splitpane-text {
  font-size: larger;
}

.btn-pill {
  @media ($max: $medium) {
    font-size: 0.7rem;
    line-height: 0.4125rem;
    padding: 0.3125rem 0.425rem .375rem;
  }
}

:deep(.dropdown-menu) {
  z-index: 102;
}

.force-bottom-dropdown {
  :deep(.dropdown-menu) {
    transform: translate3d(0, 140px, 0) !important;
  }
}

.filter-count {
  min-width: 16px;
  min-height: 16px;
  line-height: 10px;
}

:deep(.dismissible-container) {
  max-width: 550px;
  background-color: white !important;
}

.draggable {
  cursor: unset;
}

.helpesk-tickets-wrap :deep(.simplebar-content-wrapper) {
  height: 100% !important;
}

.checkbox-checked {
  background-color: $themed-link;
  border-color: $themed-link;
}

.search-input {
  max-width: 100%;
  width: 31rem;
}

.search-input--split {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  border-left: none;
}

.list-heading-filter-row {
  flex-direction: row-reverse;
  gap: 1.5rem;
}

.filter-view-type-toggle {
  right: 3rem;
  top: 0.75rem;
  z-index: 1;
}

.list-with-quick-view {
  padding-right: 32.5rem;
}

.filters-list-view {
  height: calc(100% - 4rem);
}

.dragging-ghost {
  opacity: 0.6;
  background: #f0f0f0;
  border: 0.063rem dashed #ccc;
  cursor: grabbing;
  will-change: transform;
  transition: all 0.2s ease;
}

.ticket-draggable-list {
  background-color: $themed-lighter;
  border-radius: 0.5rem;
  height: 100%;
  margin-bottom: 1.5rem;
  padding: 0.5rem;
}

.column-header {
  max-height: 3rem;
  border-radius: 50rem;
  background-color: $themed-lighter;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  transition: $transition-base;
  transition-property: background;
  z-index: 99;
  cursor: grab;
  
  &:hover {
    background-color: $themed-very-fair;
  }

  &:before {
    background-color: $themed-light;
    content: "";
    inset: -3rem -0.5rem 50%;
    position: absolute;
    z-index: -2;
  }

  &:after {
    background-color: $themed-lighter;
    box-shadow: $shadow-container-box-inner-heading;
    border-radius: 50rem;
    content: "";
    inset: 0;
    position: absolute;
    z-index: -1;
  }
}

.kanban-column-indicator {
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  margin-right: 0.25rem;
  background-color: var(--column-color);
}

.kanban-column-name {
  max-width: 17rem;
  color: var(--column-dark-color);
  text-transform: capitalize;
}

.kanban-column-count {
  color: var(--column-dark-color);
  background-color: var(--column-light-color);
  height: 1.3125rem;
  width: 1.3125rem;
}

.unread-comment-count {
  background-color: #dcf1f4;
}

.save-icon {
  color: #d7dce4;
  font-size: 1rem !important;
}

.save-icon-background {
  background-color: #3b5e88;
}

.skeleton-td {
  height: 65px;
}

.split-pane {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.split-ticket-list,
.ticket-details {
  overflow-x: hidden;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    transition: opacity 0s linear;
    width: 0.5rem;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: $themed-very-muted;
    border-radius: 0.25rem;
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

.split-ticket-list {
  flex: 1;
}

.ticket-details {
  flex: 2;
  padding: 1rem;
}

.action-dropdown-box {
  display: block;
  padding: 0.25rem 0rem;
  cursor: pointer;
  color: #000;
  padding-left: 0.5rem;
  padding-right: 0.9rem;
}

.action-dropdown-box:hover {
  background-color: var(--themed-light);
  color: var(--themed-base);
}

.kanban-column {
  display: flex;
  flex: 1;
  flex-direction: column;
  max-width: 32rem;
  min-width: 25rem;
  position: relative;
  transition: $transition-base;
  transition-property: transform;
}

.kanban-column--droppable {
  background-color: rgba(46, 204, 113, 0.15);
  border: 2px dashed rgba(46, 204, 113, 0.5);
  transition: background-color 0.2s ease-in-out;
}

.kanban-column--restricted {
  background-color: rgba(231, 76, 60, 0.15);
  border: 2px dashed rgba(231, 76, 60, 0.5);
  transition: background-color 0.2s ease-in-out;
}

.kanban-columns {
  gap: $kanban-gap;
  height: fit-content;
}

.drag-handle {
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

.kanban-topbar {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: -2rem;
  margin-top: map-get($spacers, 1);
  padding: 0 0.5rem;
  position: relative;
}

.kanban-switcher {
  display: flex;
  justify-content: flex-start;
}

.kanban-select {
  background-color: transparent;
}

.searchclear {
  cursor: pointer;
  position: absolute;
  right: 0.6rem;

  .cross-icon {
    display: none;
  }

  &.has-text {
    .cross-icon {
      display: block;
    }
  }
}

.list-view-header {
  position: sticky;
  top: -1.75rem;
  width: calc(100% + 3rem);
}

.loader-and-counter-wrap {
  min-width: 8rem; // Helps prevent the jumping around of the quick filters
}

// TODO: For some reason this isn't working as intended on the switch away from hover.
.split-ticket-list .help-ticket-grid-item {
  box-shadow: none;
  transition: $transition-base, z-index 0.3s cubic-bezier(0,1,1,0);
  z-index: 1;

  &:hover {
    box-shadow: $shadow-hover;
    transition: $transition-base, z-index 0s cubic-bezier(0,1,1,0);
    z-index: 2;

    :deep(.split-pane-box:not(.box--selected)) {
      background-color: $themed-lighter;
      box-shadow: inset -1px 0 0 $themed-moderate-light;
    }
  }
}
</style>
