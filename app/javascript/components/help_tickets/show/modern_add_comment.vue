<template>
  <div :class="{ 'px-4 pt-3 border-bottom': isEditing }">
    <div
      v-show="timeUsage"
      class="form-group"
    >
      <div>
        <div :class="{ 'row': !isQuickView }">
          <div
            class="col sm-3 md-6"
            :class="{ 'mt-2': isQuickView }"
          >
            <div class="form-group mb-0">
              <label class="text-secondary"> Start Time </label>
              <div
                class="inner-addon left-addon manual-timepicker-holder time-icon time-start-dropdown"
                :class="{ 'starting-time': isQuickView }"
              >
                <i class="text-muted genuicon genuicon-android-time" />
                <vue-timepicker
                  v-model="startTime"
                  lazy
                  hide-clear-button
                  manual-input
                  input-class="manual-timepicker-input"
                  input-width="95%"
                  placeholder="00:00"
                  @change="calculateDifference($event, true, false)"
                />
              </div>
            </div>
          </div>

          <div
            class="col sm-3 md-6"
            :class="{ 'mt-2': isQuickView }"
          >
            <div class="form-group mb-0">
              <label class="text-secondary"> End Time </label>
              <div
                class="inner-addon left-addon manual-timepicker-holder time-icon time-end-dropdown"
                :class="{ 'ending-time': !isQuickView }"
              >
                <i class="text-muted genuicon genuicon-android-time" />
                <vue-timepicker
                  v-model="endTime"
                  lazy
                  hide-clear-button
                  manual-input
                  input-class="manual-timepicker-input"
                  input-width="95%"
                  placeholder="00:00"
                  @change="calculateDifference($event, false, true)"
                />
              </div>
            </div>
          </div>

          <div
            class="col sm-3 md-6"
            :class="{ 'mt-2': isQuickView }"
          >
            <div class="form-group mb-0">
              <label
                for="hours"
                class="text-secondary"
              > Time Spent </label>
              <div class="inner-addon left-addon mr-4">
                <i class="text-muted genuicon genuicon-android-time" />
                <multi-select
                  id="hours"
                  v-model="timeSpent"
                  placeholder="0h 0m"
                  tag-placeholder="Set duration"
                  name="hours"
                  deselect-label=""
                  select-label=""
                  :taggable="true"
                  :multiple="false"
                  :options="quickTimeSpentEnteries"
                  :class="{ 'is-invalid': !isTimeSpentValid }"
                  :disabled="!isWriteAny"
                  @tag="setCustomTime"
                  @select="setTime"
                />
                <span
                  v-show="!isTimeSpentValid"
                  class="form-text text-danger small"
                >
                  Please enter a valid time value
                </span>
                <div class="text-info small form-text">
                  Ex. 1h 30m, 1hr 30min, 1:30
                </div>
              </div>
            </div>
          </div>

          <div
            class="col sm-3 md-6"
            :class="{ 'mt-2': isQuickView }"
          >
            <div class="form-group mb-0">
              <label
                for="date"
                class="text-secondary"
              > Date </label>
              <nice-datepicker
                id="date"
                :value="formatDate"
                placeholder="Date"
                :input-class="{ 'is-invalid': isFutureDate }"
                :disabled="!isWriteAny"
                @input="setDate($event)"
              />
              <span
                v-show="isFutureDate"
                class="form-text text-danger small"
              >
                {{ dateErrorMessage }}
              </span>
            </div>
          </div>
        </div>
        <div
          class="form-group"
          :class="{
            'col mt-2': isQuickView,
            'time-spent-user-field' : !isQuickView,
          }"
        >
          <div class="form-group mb-0">
            <label
              for="timeSpentUser"
              class="text-secondary"
            >
              Select User who spent time on this ticket
            </label>
            <contributor-select
              ref="contributorSelect"
              :value="timeEntryUser"
              only-members
              @select="addUser"
              @remove="removeUser"
            />
            <span
              v-if="isTimeSpentUserError"
              class="form-text text-danger small"
            >
              No user selected. Please select a user who spent time on this ticket.
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="form-group mb-0">
      <input
        :id="`ticketComment${editableComment.id}`"
        ref="commentInput"
        type="hidden"
        :value="editableComment.commentBody"
        :disabled="!isWrite"
      >
      <trix-vue
        ref="trixEditor"
        :key="commentKey"
        v-model="editableComment.commentBody"
        :current-entity="currentHelpTicket"
        :input-id="`ticketComment${editableComment.id}`"
        :class="{ 
          'trix-toolbar--small': isQuickView,
          'trix-editor--small': isQuickView && !hasCommentId,
        }"
        attachable-type="HelpTicketComment"
        grab-focus
        :disabled="disabled"
        :label="label"
        :show-title-bar="true"
        :display-survey-section="displaySurveySection"
        :comment-ai-response-msg="commentAiResponseMsg"
        :display-ai-comments="displayAiComments"
        :is-ht-show-page="true"
        :is-add-comment="true"
        @input="suggestions"
        @update-uploading-status="updateUploadingStatus"
        @handle-attachment-add="handleAttachmentAdd"
        @deleted-attachmet-id="deletedAttachmetId"
        @handle-attachment-remove="handleAttachmentRemove"
        @related-items="updateRelatedItems"
        @generate-ai-response="generateAiResponse"
      >
        <div
          class="d-flex row mt-2"
          :class="{ 'mb-3': isQuickView }"
        >
          <div 
            v-if="!isBasicAccess"
            :class="{
              'w-100': isQuickView,
              'd-flex': !isQuickView,
              'tickets-toggle': showPrivateUsersOption
            }"
          >
            <div
              class="ml-3"
              :class="{ 'col-md-8 ml-n1': isQuickView }"
            >
              <material-toggle
                v-model="editableComment.muteNotification"
                class="d-flex align-items-center rounded"
                :class="{
                  'justify-content-between mute-toggle': isQuickView && showPrivateUsersOption,
                  'mute-toggle-update': isQuickView && hasCommentId,
                }"
                slide-text-position="left"
                slide-text-classes="d-flex align-items-center mr-2"
                show-hover-state
                :init-active="editableComment.muteNotification"
                @toggle-sample="toggleNotification"
              >
                <span v-tooltip="'Mute Notifications'">
                  <i :class="notificationIcon" />
                </span>
              </material-toggle>
            </div>
            <div
              v-if="showPrivateUsersOption"
              class="ml-1"
              :class="{ 'col-md-8 ml-n1': isQuickView }"
            >
              <div
                class="bottom-class"
                :class="{'d-flex': editableComment.privateFlag}"
              >
                <material-toggle
                  v-model="editableComment.privateFlag"
                  class="d-flex align-items-center rounded"
                  :class="{ 'justify-content-between': isQuickView }"
                  slide-text-position="left"
                  slide-text-classes="d-flex align-items-center mr-2"
                  show-hover-state
                  :init-active="editableComment.privateFlag"
                  @toggle-sample="togglePrivate"
                >
                  <span v-tooltip="'Restrict to private users'">
                    <i class="nulodgicon-locked text-secondary not-as-small" />
                  </span>
                  <span class="p--responsive not-as-small">Restrict to private users</span>
                </material-toggle>
                <div
                  v-if="editableComment.privateFlag"
                  class="form-group px-2 mb-0 bg-colors"
                >
                  <private-contributors-dropdown
                    :value="privateContributors"
                    :disabled="!isWriteAny"
                    @add="addSelected"
                    @delete="removeSelected"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="col flex-class"
          :class="{
            'offset-md-6': !showPrivateUsersOption && isQuickView,
            'col d-flex flex-row align-items-center': !isQuickView || (isQuickView && hasCommentId),
            'd-flex flex-row-reverse justify-content-end mb-n3 ml-1 mt-1 col-md-12': (isQuickView && !hasCommentId)
          }"
        >
          <div
            v-if="!hasCommentId"
            class="position-relative flex-row"
          >
            <div class="btn-group">
              <button
                class="btn btn-sm btn-primary form-btn--responsive d-flex align-items-center"
                :class="{'px-3 mb-2': isQuickView, 'mb-0': !isQuickView}"
                :disabled="isDisabled"
                data-tc-add-comment
                @click.stop="addComment"
              >
                <span v-if="(isSubmitting && !isClosing) || attachmentUploading">
                  Saving
                  <pulse-loader
                    :loading="attachmentUploading"
                    class="ml-2 float-right"
                    color="#000"
                    size="0.125rem"
                  />
                </span>
                <span v-else-if="timeUsage">Save</span>
                <span v-else>
                  <i class="nulodgicon-paper-airplane" /> Add Comment
                </span>
              </button>
              <button
                class="btn btn-sm btn-primary cursor-pointer"
                :disabled="isDisabled"
                @click.stop="toggleDropdown"
              >
                <i 
                  v-click-outside="closeDropdown"
                  class="genuicon-up-down-arrow" 
                />
              </button>
            </div>
            <div
              class="dropdown-menu comment-actions-dropdown not-as-small mt-4"
              :class="{ 
                'show': showDropdown,
                'dropdown-position': !isBasicAccess,
                'dropdown-position-basic-access': isBasicAccess,
              }"
            >
              <div
                v-if="!isBasicAccess"
                class="text-secondary dropdown-text-menu px-3 py-2"
                @click.stop="addTask"
              >
                <i class="genuicon-nav-task-scheduler mr-1" />Add Task
              </div>
              <div
                v-if="showResolutionField"
                class="text-secondary dropdown-text-menu px-3 py-2"
                @click="addCommentAndCloseTicket"
              >
                <i class="nulodgicon-checkmark mr-2" />Mark Resolution
              </div>
              <div>
                <div class="dropdown-section-header">Schedule comment</div>
                <div
                  class="cursor-pointer text-secondary dropdown-text-menu px-3 py-2"
                  @click="handleScheduleOption('tomorrow')"
                >
                  Tomorrow at 9:00AM
                </div>
                <div
                  class="cursor-pointer text-secondary dropdown-text-menu px-3 py-2"
                  @click="handleScheduleOption('monday')"
                >
                  Monday at 9:00AM
                </div>
                <div
                  class="cursor-pointer text-secondary dropdown-text-menu px-3 py-2"
                  @click="handleCustomScheduleOption(initTasklist)"
                >
                  Custom Date/Time
                </div>
              </div>
            </div>
          </div>
          <div 
            v-else 
            class="btn-group p-0 float-right position-relative mr-2"
          >
            <button
              class="btn btn-sm btn-primary float-right form-btn--responsive"
              :class="{'mb-0': !isQuickView}"
              :disabled="disableUpdate"
              @click.stop="updateComment"
            >
              <span v-if="(isSubmitting && !isClosing) || attachmentUploading">
                Saving
                <pulse-loader
                  :loading="attachmentUploading"
                  class="ml-2 float-right py-2"
                  color="#000"
                  size="0.125rem"
                />
              </span>
              <span
                v-else
              >Update</span>
            </button>
            <div v-if="!isCommentPublished">
              <button
                class="btn btn-sm btn-primary-dark d-flex align-items-center justify-content-center dropdown-btn-rounded"
                type="button"
                :disabled="isDisabled"
                @click.prevent.stop="toggleDropdown"
              >
                <i class="dropdown-toggle arrow-down d-block" />
              </button>
              <div
                v-if="showDropdown"
                class="dropdown-menu right-0 show custom-dropdown-menu"
              >
                <div class="dropdown-section-header">Reschedule comment</div>
                <div
                  class="dropdown-item cursor-pointer"
                  @click="handleCustomScheduleOption(initTasklist)"
                >
                  Edit Details
                </div>
              </div>
            </div>
          </div>
          <button
            v-if="hasCommentId && commentUsage || (timeUsage && hasCommentId)"
            class="btn btn-sm float-right mr-1 form-btn--responsive"
            :class="{'mb-0': !isQuickView}"
            @click="cancel"
          >
            Cancel
          </button>
        </div>
      </trix-vue>
    </div>
    <help-ticket-close-modal
      v-if="!ticketCloseModal"
      ref="helpTicketCloseModal"
      :object="currentHelpTicket"
      :ticket-data="ticketData"
      :require-time-spent="requireTimeSpent"
      @handle-ok="handleCloseModal"
      @filter-data="filterData"
    />
    <scheduled-task-form
      ref="scheduledTaskFormModal"
      :selected-task="selectedScheduledTask"
      :is-scheduled-comment="isScheduledComment"
      :current-help-ticket="currentHelpTicket"
      :skip-task-details="skipTaskDetails"
      @input="okInput"
      @cancel="resetModalData"
    />
    <Teleport to="body">
      <sweet-modal
        ref="newTaskModal"
        title="Create a new Task"
      >
        <div>
          <project-tasks-form
            :value="task"
            :add-new-user="false"
            :is-saving="isSaving"
            :pop-up-modal="false"
            :is-quick-view="false"
            :show-close-button="true"
            @input="saveTask"
            @close-task-modal="closeTaskModal"
          />
        </div>
      </sweet-modal>
    </Teleport>
  </div>
</template>

<script>
  import http from 'common/http';
  import dates from 'mixins/dates';
  import { marked } from "marked";
  import consumer from "common/consumer";
  import MaterialToggle from 'components/shared/material_toggle.vue';
  import linkifyHtml from "linkify-html";
  import { mapGetters, mapMutations, mapActions } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import _cloneDeep from 'lodash/cloneDeep';
  import _debounce from "lodash/debounce";
  import NiceDatepicker from 'components/shared/nice_datepicker.vue';
  import permissionsHelper from "mixins/permissions_helper";
  import ticketActions from 'mixins/custom_forms/actions';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import vClickOutside from 'v-click-outside';
  import VueTimepicker from 'vue2-timepicker';
  import 'vue2-timepicker/dist/VueTimepicker.css';
  import ContributorSelect from "components/shared/contributors_select.vue";
  import MultiSelect from "vue-multiselect";
  import moment from 'moment-timezone';
  import VueMoment from 'vue-moment';
  import customFormHelper from "mixins/custom_form_helper";
  import PrivateContributorsDropdown from 'components/shared/private_contributors_dropdown.vue';
  import ScheduledTaskData, { defaultRecurrencePattern, defaultInitTasklist } from 'mixins/scheduled_task_data';
  import TrixVue from '../../trix_vue.vue';
  import ProjectTasksForm from './project_tasks/form.vue';
  import string from '../../../mixins/string';
  import ScheduledTaskForm from '../scheduled_tasks/scheduled_task_form.vue';

  Vue.use(VueMoment, {
    moment,
  });

  export default {
    $_veeValidate: {
      validator: 'new',
    },
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      MaterialToggle,
      TrixVue,
      NiceDatepicker,
      MultiSelect,
      PrivateContributorsDropdown,
      PulseLoader,
      VueTimepicker,
      HelpTicketCloseModal: () => import('../help_ticket_close_modal.vue'),
      ContributorSelect,
      ProjectTasksForm,
      SweetModal,
      ScheduledTaskForm,
    },
    mixins: [
      dates,
      permissionsHelper,
      ticketActions,
      customFormHelper,
      string,
      ScheduledTaskData,
    ],
    props: {
      disabled: {
        type: Boolean,
        default: false,
      },
      showPrivateUsersOption: {
        type: Boolean,
        default: false,
      },
      usage: {
        type: String,
        required: true,
      },
      comment: {
        type: Object,
        default: null,
      },
      timeEntry: {
        type: Object,
        default: null,
      },
      isEditing: {
        type: Boolean,
        default: false,
      },
      privateContributors: {
        type: Array,
        default: () => [],
      },
      ticketCloseModal: {
        type: Boolean,
        default: false,
      },
      isQuickView: {
        type: Boolean,
        default: false,
      },
      replyComment: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        isTimeSpentValid: true,
        quickTimeSpentEnteries: [
          "0h 15m",
          "0h 30m",
          "1h 0m",
          "4h 0m",
        ],
        responses: [],
        timeSpent: null,
        ticketData: null,
        previousContributors: _cloneDeep(this.privateContributors),
        hoursValidation: {
          required: true,
          regex: /^(\d*)\s*:\s*(\d*)$|^(\d*)?\s*(ho?u?r?s?)?\s*(\d*)?\s*(mi*n*u*t*e*s*)?$|^(\d*)(\.\d+)?$/,
        },
        dateErrorMessage: 'Please select a past or current date',
        editableComment: {...this.comment},
        originalComment: {...this.comment},
        editableTimeEntry: {...this.timeEntry},
        attachmentIds: [],
        attachmentUploading: false,
        isSubmitting: false,
        relatedItems: [],
        commentKey: false,
        startTime: null,
        endTime: null,
        isTimeUpdated: false,
        startFlag: false,
        endFlag: true,
        isEndTimeChanged: false,
        isClosing: false,
        isPrivateUsers: false,
        isNotificationUpdated: false,
        timeSpentUser: null,
        isTimeSpentUserError: false,
        showDropdown: false,
        openTaskModal: false,
        task: {
          description: '',
          priority: "low",
          assignees: [],
        },
        isSaving: false,
        notificationIcon: "nulodgicon-bell-o",
        isTimeDraft: false,
        isCommentDraft: false,
        isDraftResolved: false,
        isMarkResolution: false,
        initTasklist: defaultInitTasklist,
        selectedScheduledTask: null,
        skipTaskDetails: false,
        createScheduledComment: false,
        isScheduledComment: false,
        commentAiResponseMsg: "",
        ticketCommentSubscription: null,
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['currentCompanyUser']),
      ...mapGetters([
        'faqs',
        'currentHelpTicket',
        'timeSpents',
        'displaySurveySection',
        'currentHelpTicketDraft',
        'enableTicketDrafts',
        'displayAiComments',
      ]),
      isCommentPublished() {
        return this.comment?.status === 'published';
      },
      draftExists() {
        return (
          this.currentHelpTicketDraft &&
          this.currentHelpTicketDraft.companyUserId === this.$currentCompanyUserId
        );
      },
      draftCommentValue() {
        if (this.currentHelpTicketDraft.companyUserId !== this.$currentCompanyUserId) {
          return null;
        }
        const draftData = this.currentHelpTicketDraft?.comments || {};
        if (this.editableComment.id) {
          return draftData[this.editableComment.id] || null;
        }
        return draftData.newComment || null;
      },
      draftTimeValue() {
        if (this.currentHelpTicketDraft.companyUserId !== this.$currentCompanyUserId) {
          return null;
        }
        const draftData = this.currentHelpTicketDraft?.timeSpents || {};
        if (this.editableComment.id) {
          return draftData[this.editableComment.id] || null;
        }
        return draftData.newTime || null;
      },
      hasCommentId() {
        return Object.keys(this.comment).includes('id');
      },
      isTimeAndCommentUpdate() {
        return this.isCommentUpdate && !this.isTimeUpdated && this.isUserChanged;
      },
      isCommentUpdate() {
        return this.editableComment.commentBody === this.comment.commentBody && this.editableComment.privateFlag === this.comment.privateFlag;
      },
      timeUsage() {
        return this.usage === 'Time';
      },
      disableTimeSpent() {
        return this.timeUsage ? this.startTime === this.endTime : false;
      },
      commentUsage() {
        return this.usage === 'Comment';
      },
      addTimeCheck() {
        return this.timeUsage && this.editableTimeEntry.startedAt && this.timeSpent && !this.isFutureDate;
      },
      addCommentCheck() {
        return this.commentUsage && this.editableComment.commentBody;
      },
      disableComment() {
        if (!this.haveCommentBody(this.editableComment.commentBody)) {
          return true;
        }
        if (this.addCommentCheck || this.addTimeCheck) {
          if (this.commentUsage && this.editableComment.privateFlag) {
            return !(this.previousContributors && this.previousContributors.length > 0);
          }
          return false;
        }
        return true;
      },
      isFutureDate() {
        const today = new Date().getTime();
        const selectedDate = new Date(this.editableTimeEntry.startedAt).getTime();
        return selectedDate > today;
      },
      label() {
        if (this.timeUsage) {
          return "Note for this time entry";
        }
        return this.isEditing ? "Update Comment" : "Add a new Comment";
      },
      contributorIds() {
        let myArray = [];
        if (this.editableComment.privateFlag) {
          const uniqueSet = new Set(this.previousContributors.map(user => user.id));
          uniqueSet.add(Vue.prototype.$currentContributorId);
          myArray = Array.from(uniqueSet);
        }
        return myArray;
      },
      formFields() {
        return this.currentHelpTicket.customForm.formFields;
      },
      statusField() {
        return this.formFields.find(cff => cff.name === 'status');
      },
      isTicketClosed() {
        return this.statusField.customFormValue.valueStr === 'Closed';
      },
      isScopedAndFollower() {
        if (!this.isScoped) {
          return false;
        }

        const followersField = this.formFields.find(cff => cff.name === 'followers' && cff.fieldAttributeType === 'people_list');      
        if (followersField) {
          return followersField.customFormValue.find(cfv => cfv.valueInt === this.$currentContributorId);
        }
        return false;
      },
      isDisabled() {
        return this.isSubmitting || this.disableComment || this.attachmentUploading || this.disabled || this.disableTimeSpent;
      },
      disableUpdate() {
        return (
          this.isSubmitting ||
          this.disableComment ||
          this.attachmentUploading ||
          (this.disableTimeSpent && this.isCommentUpdate) ||
          (this.isTimeAndCommentUpdate && !this.isPrivateUsers) &&
          !this.isNotificationUpdated
        );
      },
      requireTimeSpent() {
        return this.currentHelpTicket.customForm.moduleForm.requireTimeSpentToClose && !this.timeSpents.length;
      },
      helpTicketId() {
        return this.$route.params.id || this.currentHelpTicket.id;
      },
      showResolutionField() {
        return this.statusField && !this.isTicketClosed && !this.isScopedAndFollower && !this.ticketCloseModal && !this.isBasicAccess;
      },
      formatDate() {
        return moment.tz(this.editableTimeEntry.startedAt, Vue.prototype.$timezone).format();
      },
      timeEntryUser() {
        const timeSpender = this.timeEntry?.id ? this.timeSpentUser : this.currentCompanyUser;
        return { name: timeSpender?.user?.fullName, id: timeSpender?.contributorId };
      },
      isUserChanged() {
        if (this.timeEntry?.id) {
          return (
            this.timeEntry.companyUserId === this.timeSpentUser?.id ||
            this.timeEntry.companyUserId === this.timeSpentUser?.rootId
          );
        }
        return false;
      },
    },
    watch: {
      comment() {
        if (this.isDraftResolved) {
          this.editableComment = {...this.comment};
        }
      },
      timeEntry() {
        if (this.isDraftResolved) {
          this.editableTimeEntry = {...this.timeEntry};
        }
      },
      'editableComment.commentBody' : function () {
        if (!this.isDraftResolved && this.enableTicketDrafts) {
          if (this.isTimeDraft && this.timeUsage &&
              ('newTime' in this.currentHelpTicketDraft.timeSpents) &&
              this.editableComment.commentBody !== this.draftTimeValue.commentBody) {
            this.handleDraftResolution('discard');
            this.$store.dispatch("handleTicketDraft");
          }
          if (this.isCommentDraft && this.commentUsage &&
              ('newComment' in this.currentHelpTicketDraft.comments) &&
              this.editableComment.commentBody !== this.draftCommentValue.commentBody) {
            this.handleDraftResolution('discard');
          }
        }
      },
    },
    created() {
      this.initializeDraft();
    },
    mounted() {
      this.subscribeCommentChannel();
    },
    methods: {
      ...mapMutations(['setIsCommentUpdated']),
      ...mapActions([
        'fetchTicketDraft',
      ]),
      initializeDraft() {
        this.isDraftResolved = false;
        if (!this.draftExists || !this.enableTicketDrafts) {
          return;
        }
        if (this.commentUsage && this.draftCommentValue) {
          this.editableComment = _cloneDeep(this.draftCommentValue);
          this.isCommentDraft = true;
        }
        else if (this.timeUsage && this.draftTimeValue) {
          this.editableComment = _cloneDeep(this.draftTimeValue);
          this.isTimeDraft = true;
        }
      },
      handleDraftResolution(actionType) {
        if (actionType === 'discard' && this.enableTicketDrafts) {
          this.editableComment = this.originalComment;
          if (this.commentUsage) {
            const comments = { ...this.currentHelpTicketDraft?.comments};
            if (this.editableComment.id) {
              delete comments[this.editableComment.id];
            } else {
              delete comments.newComment;
            }
            this.updateDraftCommentValue(comments);
            this.isDraftResolved = true;
          } else if (this.timeUsage) {
            const timeSpents = { ...this.currentHelpTicketDraft?.timeSpents};
            if (this.editableComment.id) {
              delete timeSpents[this.editableComment.id];
            } else {
              delete timeSpents.newTime;
            }
            this.updateDraftCommentValue(timeSpents);
            this.isDraftResolved = true;
          }
        }
      },
      updateDraftCommentValue(updatedData) {
        if (this.commentUsage) {
          const updatedCommentsData = {
            comments: updatedData,
            id: this.currentHelpTicketDraft.id,
            ...(this.currentHelpTicketDraft.helpTicketId || { helpTicketId: this.helpTicketId.id }),
          };
          if (!updatedCommentsData.commnets) {
            this.$store.commit('updateCurrentHelpTicketDraft', updatedCommentsData);
          }
          this.$store.commit('updateCurrentHelpTicketDraft', updatedCommentsData);
        } else if (this.timeUsage) {
          const updatedTimeSpentsData = {
            timeSpents: updatedData,
            id: this.currentHelpTicketDraft.id,
            ...(this.currentHelpTicketDraft.helpTicketId || { helpTicketId: this.helpTicketId.id }),
          };
          this.$store.commit('updateCurrentHelpTicketDraft', updatedTimeSpentsData);
        }
      },
      resetModalData() {
        this.skipTaskDetails = false;
        this.selectedScheduledTask = null;
        this.initTasklist = defaultInitTasklist;
      },
      fetchScheduledTasks() {
        const url = '/scheduled_tasks.json';
        http
          .get(url).then(res => {
            this.scheduledTasks = res.data.scheduledTasks;
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error fetching scheduled tasks. ${error.response.data.message}`);
          });
      },
      okInput(taskId) {
        this.fetchScheduledTasks();
        this.createScheduledComment = true;
        if (taskId) {
          this.addComment(taskId);
        } else {
          this.updateComment();
        }
        this.resetData();
      },
      taskWithRecurrenceData(task) {
        if (task?.id && task.recurring) {
          task.recurrence.recurrencePattern.daily ||= { recurrenceType: 'revision', revisionCount: 1 };
          task.recurrence.recurrencePattern.weekly ||= { selectedDays: [], revisionCount: 1 };
          task.recurrence.recurrencePattern.monthly ||= { recurrenceType: 'monthDay', monthDay: 1, dayMonthRevision: 1, weekMonthRevision: 1, selectedWeek: 'First', weekDayName: "Monday" };
          task.recurrence.recurrencePattern.yearly ||= { recurrenceType: 'monthDayOption', revisionCount: 1, dayMonthName: "January", weekMonthName: "January", monthDay: 1, weekDay: "First", dayName: "Monday" };
        } else if (task?.id && !task.recurring) {
          task.recurrence ||= defaultRecurrencePattern;
        }
        return task;
      },
      handleCustomScheduleOption(task) {
        this.showDropdown = false;

        if (!this.hasCommentId) {
          const initTask = _cloneDeep(task);
          initTask.assigneeId = this.$currentContributorId;

          const timeZone = Vue.prototype.$timezone;
          const now = moment().tz(timeZone);
          const minutes = now.minutes();
          const roundedMinutes = Math.ceil(minutes / 15) * 15;
          
          if (roundedMinutes === 60) {
            now.add(1, 'hour').startOf('hour');
          } else {
            now.minutes(roundedMinutes).seconds(0);
          }

          const dateTime = now.format('YYYY-MM-DD');
          const currentTime = now.format('HH:mm');
          const startTime = this.convertTimeFormat(currentTime, 'to12');
          
          initTask.taskStartedAt = {
            date: dateTime,
            startTime,
            timeZone,
          };

          this.openScheduledTaskFormModal(initTask);
        } else {
          this.fetchScheduledCommentTask(this.comment.id);
        }
      },
      fetchScheduledCommentTask(commentId) {
        const params = { comment_id: commentId };
        const helpticketId = this.helpTicketId;
        return http
          .get(`/tickets/${helpticketId}/ticket_comments/get_scheduled_comment`, { params })
          .then((res) => {
            if (res.data && Object.keys(res.data).length) {
              const scheduledCommentTask = res.data;
              this.convertTimeDate(scheduledCommentTask);
              this.openScheduledTaskFormModal(scheduledCommentTask);
            }
          })
          .catch((error) => {
            const errorMessage = error?.response?.data?.message || '';
            this.emitError(`Sorry there was an error fetching the Scheduled comment details. ${errorMessage}`);
          });
      },
      openScheduledTaskFormModal(task) {
        const initTask = _cloneDeep(task);
        this.isScheduledComment = true;
        this.selectedScheduledTask = this.taskWithRecurrenceData(initTask);
        this.$refs.scheduledTaskFormModal.open();
        this.setScheduledCommentDetails(initTask);
      },
      onClickOutside() {
        this.showDropdown = false;
      },
      handleScheduleOption(option) {
        this.showDropdown = false;

        const dateObj = new Date();
        const scheduledDate = new Date();

        if (option === 'tomorrow') {
          scheduledDate.setDate(dateObj.getDate() + 1);
        } else if (option === 'monday') {
          const day = scheduledDate.getDay();
          const daysUntilMonday = (8 - day) % 7 || 7;
          scheduledDate.setDate(scheduledDate.getDate() + daysUntilMonday);
        } else {
          return;
        }
        
        const dateTime = scheduledDate.toISOString().slice(0, 10);
        const startTime = this.convertTimeFormat('09:00', 'to12');
        const timeZone = Vue.prototype.$timezone;

        const initTask = _cloneDeep(defaultInitTasklist);
        initTask.taskStartedAt = {
          date: dateTime,
          startTime,
          timeZone,
        };

        initTask.assigneeId = this.$currentContributorId;

        this.skipTaskDetails = true;
        this.isScheduledComment = true;

        this.openScheduledTaskFormModal(initTask);
      },
      closeTaskModal() {
        this.openTaskModal = false;
        this.$refs.newTaskModal.close();
      },
      setCustomTime(value) {
        this.setTime(value);
        if (this.isTimeSpentValid) {
          this.quickTimeSpentEnteries.push(value);
          this.timeSpent = value;
        }
      },
      onWorkspaceChange() {
        if (this.timeUsage) {
          this.initTimeSpent();
        };
        if (this.timeEntry?.id) {
          this.fetchCompanyUser(this.timeEntry.companyUserId);
        } else {
          this.timeSpentUser = this.currentCompanyUser;
        }
        if (this.currentHelpTicket?.id) {
          this.filterData();
        }
      },
      togglePrivate() {
        if (this.editableComment.privateFlag) {
          this.previousContributors = [];
        }
        this.editableComment.privateFlag = !this.editableComment.privateFlag;
      },
      toggleNotification() {
        this.editableComment.muteNotification = !this.editableComment.muteNotification;
        this.notificationIcon = this.editableComment.muteNotification ? "genuicon-bell-slash-o" : "nulodgicon-bell-o";
        this.isNotificationUpdated = true;
      },
      updateRelatedItems(relatedItems) {
        this.relatedItems = relatedItems;
      },
      setDate(date) {
        this.editableTimeEntry.startedAt = date;
      },
      haveCommentBody(str) {
        if (str) {
          const parser = new DOMParser();
          const doc = parser.parseFromString(str, 'text/html');
          return !/^\s*$/.test(doc.body.innerText);
        }
        return true;
      },
      initTimeSpent() {
        this.startTime = this.editableTimeEntry.startTime ? this.editableTimeEntry.startTime : "00:00";
        this.endTime = this.editableTimeEntry.endTime ? this.editableTimeEntry.endTime : "00:00";
        const timeDiff =  moment.utc(moment(this.endTime,"HH:mm").diff(moment(this.startTime,"HH:mm")));
        this.timeSpent = `${timeDiff.hours()}h ${timeDiff.minutes()}m`;
      },
      addSelected(id) {
        this.previousContributors.push({ id });
        this.isPrivateUsers = true;
      },
      removeSelected(id) {
        const selected = this.previousContributors.find((c) => c.id === id);
        if (selected) {
          const idx = this.previousContributors.indexOf(selected);
          this.previousContributors.splice(idx, 1);
          this.isPrivateUsers = true;
        }
      },
      filterData() {
        const { formFields } = this.currentHelpTicket.customForm;
        const requiredForCloseAndEmpty = [];
        formFields.forEach((cff) => {
          if (cff.requiredToClose) {
            const fieldValues = this.currentHelpTicket.values.filter(val => val.customFormFieldId === cff.id);
            if (fieldValues.length === 0) {
              requiredForCloseAndEmpty.push(cff);
            }
          }
        });
        this.ticketData = _cloneDeep(this.currentHelpTicket);
        this.ticketData.customForm.formFields = requiredForCloseAndEmpty;
      },
      handleCloseModal() {
        this.filterData();
        if (this.ticketData.customForm?.formFields?.length === 0 && !this.requireTimeSpent) {
          this.$refs.helpTicketCloseModal.close();
          this.editableComment.resolutionFlag = true;
          this.addComment();
        } else {
          this.emitError("The fields which are required to close the ticket cannot be empty.");
        }
      },
      suggestions() {
        this.commentsDataUpdate();
        if (this.commentUsage && !this.isEditing) {
          this.addDraftComment();
        }
        if (this.comment.length > 0) {
          window.onbeforeunload = () => "You have unsaved changes on the form.";
          this.$emit("update-save-status", false);
          http
            .get('/snippets.json', { params: { search: this.editableComment }})
            .then(res => {
              this.responses = res.data.snippets;
            })
            .catch(error => {
              this.emitError(`Sorry, there was an error fetching responses. ${error.response.data.message}`);
            });
        } else {
          this.responses = [];
        }
      },
      commentsDataUpdate: _debounce(
        function () {
          if (this.editableComment.commentBody && this.currentHelpTicketDraft && this.enableTicketDrafts) {
            if (this.commentUsage) {
              const comments = { ...this.currentHelpTicketDraft?.comments};
              if (this.editableComment.id) {
                comments[this.editableComment.id] = this.editableComment;
              } else {
                comments.newComment = this.editableComment;
              }
              this.updateDraftCommentValue(comments);
            } else if (this.timeUsage) {
              const timeSpents = { ...this.currentHelpTicketDraft?.timeSpents};
              if (this.editableComment.id) {
                timeSpents[this.editableComment.id] = this.editableComment;
              } else {
                timeSpents.newTime = this.editableComment;
              }
              this.updateDraftCommentValue(timeSpents);
            }
          }
        },
        1000
      ),
      addComment(taskId) {
        this.closeDropdown();
        if (this.timeUsage && !this.timeSpentUser) {
          this.isTimeSpentUserError = true;
          this.emitError(`Please correct the highlighted errors before submitting.`);
          return;
        }
        this.isSubmitting = true;
        if (this.timeUsage) {
          this.$validator.validateAll().then((result) => {
            if (result) {
              this.saveComment(taskId);
            } else {
              this.isSubmitting = false;
              this.emitError(`Please correct the highlighted errors before submitting.`);
            }
        });
        } else {
          this.saveComment(taskId);
        }
      },
      addCommentAndCloseTicket() {
        this.isClosing = true;
        if (this.ticketData.customForm?.formFields?.length > 0 || this.requireTimeSpent) {
          this.$refs.helpTicketCloseModal.open();
        } else {
          this.editableComment.resolutionFlag = true;
          this.isMarkResolution = true;
          this.addComment();
        }
        this.closeDropdown();
      },
      saveComment(taskId) {
        this.setSubmitData();
        const params = { 
          help_ticket_comment: this.editableComment,
          relatedItems: this.relatedItems,
          company_id: this.currentHelpTicket.company.id,
        };
        if (this.attachmentIds.length > 0) {
          params.attachment_ids = this.attachmentIds;
        }
        if (this.createScheduledComment && taskId) {
          params.status = 'scheduled';
          params.taskId = taskId;
        }
        http
          .post(`/ticket_comments.json`, params)
          .then(() => {
            this.attachmentIds = [];
            if (this.editableComment.timeSpent) {
              if (this.timeEntry.id) {
                this.emitSuccess(`Time spent entry updated`);
              } else {
                this.emitSuccess(`Time spent entry added`);
              }
              this.addUser(this.currentCompanyUser);
            } else {
              if (this.commentUsage) {
                this.removeDraftComment();
              }
              if (this.createScheduledComment) { 
                this.emitSuccess(`Scheduled comment added`);
              } else {
                this.emitSuccess(`Ticket comment added`);
              }
            }
            if (this.editableComment.resolutionFlag) {
              this.updateTicketStatus();
            }
            this.$emit('reply-added');
            this.resetData();
            this.isDraftResolved = false;
            this.discardDraft();
            this.fetchScheduledComments();
            this.$store.dispatch('fetchTimeSpents', this.helpTicketId);
            const fetch = () => this.$store.dispatch('fetchComments', this.helpTicketId);
            this.isMarkResolution ? setTimeout(fetch, 1000): fetch();
            this.$emit("update-save-status", true);
            window.onbeforeunload = null;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error adding the comment.');
          })
          .finally(() => {
            this.isSubmitting = false;
            this.$store.dispatch("fetchTicket", this.helpTicketId);
          });
      },
      fetchScheduledComments() {
        const helpticketId = this.helpTicketId;
        const contributorId = this.$currentContributorId;
        this.$store.dispatch('fetchScheduledComments', { helpticketId, contributorId });
      },
      discardDraft() {
        if (!this.isDraftResolved) {
          this.handleDraftResolution('discard');
        }
      },
      updateComment() {
        if (this.timeUsage && !this.timeSpentUser) {
          this.isTimeSpentUserError = true;
          this.emitError(`Please correct the highlighted errors before submitting.`);
          return;
        }
        this.isSubmitting = true;
        if (this.timeUsage) {
          this.$validator.validateAll().then(result => {
          if (result) {
            this.update();
          }
        });
        } else {
          this.update();
        }
      },
      update() {
        this.isSubmitting = true;
        this.setSubmitData();
        const params = {
          help_ticket_comment: this.editableComment,
          company_id: this.currentHelpTicket.company.id,
        };
        if (this.attachmentIds.length > 0) {
          params.attachment_ids = this.attachmentIds;
        }
        if (this.editableComment.id) {
          if (this.deletedAttachmetIds.length > 0) {
            this.removeAttachmetUploads();
          }
          http
            .put(`/ticket_comments/${this.editableComment.id}.json`, params)
            .then(() => {
              this.attachmentIds = [];
              if (this.editableComment.timeSpent) {
                this.emitSuccess(`Time spent entry updated`);
              } else if (this.createScheduledComment) {
                this.emitSuccess(`Scheduled comment updated`);
              } else {
                this.emitSuccess(`Ticket comment updated`);
              }
              this.fetchScheduledComments();
              this.$store.dispatch('fetchTimeSpents', this.helpTicketId);
              this.$store.dispatch('fetchComments', this.helpTicketId);
              this.$emit("update-save-status", true);
              this.discardDraft();
              window.onbeforeunload = null;
            })
            .catch(error => {
              this.emitError(`Sorry, there was an error updating the comment. ${error.response.data.message}.`);
            })
            .finally(() => {
              this.isSubmitting = false;
              this.$store.dispatch("fetchTicket", this.helpTicketId);
            });
        } else {
          this.saveComment();
        }
        this.setIsCommentUpdated(true);
      },
      extractText(s) {
        const span = document.createElement('span');
        span.innerHTML = s;
        return span.textContent;
      },
      setSubmitData() {
        if (this.editableComment.commentBody) {
          this.editableComment.commentText = this.extractText(this.editableComment.commentBody);
          this.editableComment.commentBody = linkifyHtml(this.editableComment.commentBody, { className: '' });
        }
        if (this.commentUsage) {
          this.editableComment.privateContributorIds = this.contributorIds;
        }
        if (this.timeUsage) {
          const timeStartedtAt = moment.tz(this.editableTimeEntry.startedAt, Vue.prototype.$timezone).format();
          this.editableComment.timeSpent = {
            timeSpent: this.timeSpent,
            startedAt: timeStartedtAt,
            companyUserId: this.timeSpentUser?.rootId || this.timeSpentUser.id || this.timeSpent.companyUserId,
            id: this.editableTimeEntry.id,
            startTime: this.startTime,
            endTime: this.endTime,
          };
        }
        if (this.replyComment ) {
          this.editableComment.parentCommentId = this.replyComment.id;
        }
      },
      cancel() {
        if (this.commentUsage) {
          this.resetData();
          this.removeDraftComment();
          this.$emit('private-contributors', this.previousContributors);
          this.$emit("hide-editor");
        } else {
          this.$emit('toggle-edit-time-entry-form', this.timeSpent);
        }
        this.discardDraft();
      },
      resetData() {
        const currentTime = moment().tz(Vue.prototype.$timezone);
        const padWithZero = (number) => String(number).padStart(2, '0');
        this.editableComment = { ...this.comment };
        this.editableTimeEntry = { ...this.timeEntry };
        this.commentKey = !this.commentKey;
        this.timeSpent = "00:00";
        this.startTime = `${padWithZero(currentTime.hours())}:${padWithZero(currentTime.minutes())}`;
        this.endTime = this.startTime;
        this.isEndTimeChanged = false;
      },
      updateUploadingStatus(status) {
        this.attachmentUploading = status;
      },
      handleAttachmentAdd(data) {
        this.attachmentIds.push(data.attachment.id);
      },
      handleAttachmentRemove(attachmentId) {
        const idx = this.attachmentIds.indexOf(attachmentId);
        if (idx > -1) {
          this.attachmentIds.splice(idx, 1);
        }
      },
      calculateDifference(event, startFlag, endFlag) {
        this.isTimeUpdated = true;
        this.startFlag = startFlag;
        this.endFlag = endFlag;
        if (this.endFlag) {
          this.isEndTimeChanged = true;
        }
        if (this.isEndTimeChanged && this.startFlag) {
          let timeChange = null;
          const timeSpentArray = this.timeSpent.match(/(\d+)h (\d+)m/);
          const hours = parseInt(timeSpentArray[1], 10);
          const minutes = parseInt(timeSpentArray[2], 10);
          timeChange = moment(this.startTime, "HH:mm").add(parseInt(hours, 10), 'hours');
          timeChange = timeChange.add(parseInt(minutes, 10), 'minutes');
          this.endTime = timeChange.format("HH:mm");
          this.isEndTimeChanged = false;
        };
        if (this.startTime && this.endTime) {
          const timeDiff =  moment.utc(moment(this.endTime,"HH:mm").diff(moment(this.startTime,"HH:mm")));
          this.timeSpent = `${timeDiff.hours()}h ${timeDiff.minutes()}m`;
        };
      },
      setTime(value) {
        if (this.hoursValidation.regex.test(value)) {
          this.isTimeSpentValid = true;
          const time = value;
          let hours = null;
          let minutes = null;
          let timeDivision = null;
          let timeChange = null;

          if ((/^(\d*)\s*:\s*(\d*)$/).test(time)) {
            timeDivision = time.split(':'); 
            hours = timeDivision[0] ? timeDivision[0] : "0";
            minutes = timeDivision[1] ? timeDivision[1] : "0";
          } else if ((/^(\d*)?\s*(ho?u?r?s?)?\s*(\d*)?\s*(mi*n*u*t*e*s*)?$/).test(time)) {
            timeDivision = time.split('h');
            hours = timeDivision[0] ? timeDivision[0] : "0";
            minutes = timeDivision[1] ? timeDivision[1].substr(0, timeDivision[1].indexOf('m')) : "0";
          } else if ((/^(\d*)(\.\d+)?$/).test(time)) {
            timeDivision = time.split('.');
            hours = timeDivision[0] ? timeDivision[0] : "0";
            minutes = timeDivision[1] ? timeDivision[1] * 6 : "0";
          }
          if (hours && minutes) {
            if (this.startFlag) {
              this.startTime = this.startTime === '' ? '00:00' : this.startTime;
              timeChange = moment(this.startTime, "HH:mm").add(parseInt(hours, 10), 'hours');
              timeChange = timeChange.add(parseInt(minutes, 10), 'minutes');
              this.endTime = timeChange.format("HH:mm");
            } else if (this.endFlag) {
              this.endTime = this.endTime === '' ? '00:00' : this.endTime;
              timeChange = moment(this.endTime,"HH:mm").subtract(parseInt(hours, 10), 'hours');
              timeChange = timeChange.subtract(parseInt(minutes, 10), 'minutes');
              this.startTime = timeChange.format("HH:mm");
            }
          }
        } else {
          this.isTimeSpentValid = false;
        }
      },
      addDraftComment() {
        if (this.editableComment.commentBody.replace(/(<([^>]+)>)/gi, "").length > 0) {
          const compressedComment = this.compressData(JSON.stringify(this.editableComment));
          localStorage.setItem(`user ${this.$currentCompanyUserId}- ticket ${this.currentHelpTicket.id}`,compressedComment);
        } else {
          this.removeDraftComment();
          this.originalComment = {};
        }
      },
      removeDraftComment() {
        this.$emit('remove-draft');
        localStorage.removeItem(`user ${this.$currentCompanyUserId}- ticket ${this.currentHelpTicket.id}`);
      },
      updateTicketStatus() {
        this.addFormValue(this.currentHelpTicket.id, {
          name: 'status',
          value: 'Closed',
          label: this.statusField.label,
          custom_form_field_id: this.statusField.id,
          resolution_flag: true,
          company_id: this.currentHelpTicket.company.id,
        });
      },
      addUser(user) {
        this.timeSpentUser = user;
        this.isTimeSpentUserError = false;
      },
      removeUser() {
        this.timeSpentUser = null;
      },
      fetchCompanyUser(id) {
        http
          .get(`/company_users/${id}.json`)
          .then(res => {
            this.timeSpentUser = res.data;
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error loading user (${error.message}).`);
          });
      },
      toggleDropdown() {
        this.showDropdown = !this.showDropdown;
      },
      closeDropdown() {
        this.showDropdown = false;
      },
      addTask() {
        this.closeDropdown();
        this.task = {
          ...this.task,
          description: this.editableComment.commentBody.replace(/<\/?[^>]+(>|$)/g, ""),
        };
        this.openTaskModal = true;
        this.$refs.newTaskModal.open();
      },
      saveTask(task) {
        this.isSaving = true;
        const params = {  
          project_task: task,
          company_id: this.currentHelpTicket.company.id,
        };
        return http
          .post(`/tickets/${this.currentHelpTicket.id}/project_tasks.json`, params)
          .then(res => {
            if(res) {
              this.$store.dispatch("fetchTicket", this.currentHelpTicket.id);
              this.emitSuccess('Successfully created a new task');
              this.isSaving = false;
              this.openTaskModal = false;
              
              const taskTab = Array.from(document.querySelectorAll('.sub-menu-item'))
                .find(el => el.textContent.trim() === 'Tasks');
              if (taskTab) {
                taskTab.click();
              }
            }
          })
          .catch(error => {
            this.emitError(`Sorry, there was an error saving the task.  Please try again later. ${error.message}`);
            this.isSaving = false;
            this.openTaskModal = false;
          });
      },
      generateAiResponse(selectedParams) {
        if (!this.editableComment.commentBody?.length) {
          return;
        }
        const params = {
          ticket_id: this.helpTicketId,
          action_type: selectedParams.actionType,
          comment_body: this.editableComment.commentBody,
          comment_id: this.editableComment.id,
          sub_action_type: selectedParams.subType,
        };

        const methodToCall = selectedParams.actionType === 'change-tone' ? 'change_comment_tone' : 'transform_comment';

        http
          .post(`/ticket_comments/${methodToCall}.json`, params)
          .then(() => {
            this.commentAiResponseMsg = "";
            if (this.$refs.trixEditor) {
              this.$refs.trixEditor.$refs.commentAiResponseModal.open();
            }
          })
          .catch(error => {
            const errorMsgPart = methodToCall === 'change_comment_tone' ? 'changing comment tone' : 'shortening reply';
            this.emitError(`Sorry, there was an error while ${errorMsgPart}. (${error.message}).`);
          });
      },
      subscribeCommentChannel() {
        const vm = this;
        if (!this.ticketCommentSubscription) {
          this.ticketCommentSubscription = consumer.subscriptions.create(
            { channel: "TicketCommentChannel", ticket_id: vm.helpTicketId, company_user_id: vm.$currentCompanyUserId, comment_id: vm.editableComment?.id },
            {
              received(data) {
                if (data.status === 'error') {
                  vm.emitError('Sorry, there was an error while generating response. Please try again a few moments later.');
                }

                if (data.message) {
                  vm.commentAiResponseMsg = marked(data.message);
                }
              },
            }
          );
        }
      },
    },
  };
</script>

<style lang="scss" scoped>

  .dropdown-section-header {
    background-color: #f1f1f1;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    color: #5f5e5e;
    margin-top: 0.375rem;
    margin-bottom: 0.375rem;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .suggestions-list.row {
    margin: auto 0rem;
  }

  .suggestions-list .col-sm-3{
    margin: 0rem;
    padding: 0.313rem;
  }

  .suggestions-list .item {
    cursor: pointer;
    background-color: #def5ee38;

  border: 1px solid #def5ee38;
    padding: 0.25rem 0.9375rem;
    border-radius: 5%;
    text-align: center;
    min-height: 3.25rem;

    display: block;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.875rem;
  }

  .suggestions-list .item:hover {
    cursor: pointer;
    background-color: #cce5ff;
    border: 0.0625rem solid #9bcbfd;
  }

  .short-control {
    width: 5rem;
    padding: 0.125rem;
    line-height: 0.7rem;
    font-size: 1.0rem;
    padding: 0rem;
    height: 2rem;
  }

  .add-on .input-group-btn > .btn {
    border-left-width: 0;
    left: -0.125rem;
    box-shadow: inset 0 0.0625rem 0.0625rem rgba(0, 0, 0, 0.075);
  }

  .add-on .form-control:focus {
  box-shadow:none;
  border-color:#cccccc;
  }

  .inner-addon {
    position: relative;
  }

  .inner-addon .genuicon {
    position: absolute;
    color: #BBB;
    padding: 0.625rem;
    font-size: 1.2rem;
    pointer-events: none;
  }

  a.btn-outline-secondary:hover {
    color: #FFF;
  }
  .left-addon .genuicon  { left:  0rem;}
  .right-addon .genuicon { right: 0rem;}

  .left-addon input  { padding-left:  2.5rem; }
  .right-addon input { padding-right: 2.5rem; }

  [type='checkbox'] {
    display: none;
  }

  :checked ~ .checkbox {
    background-color: $teal;
    border-color: $teal;
  }

  .align-min-height {
    min-height: 6.25rem;
  }

  .p--responsive {
    vertical-align: middle;
    margin-left: 0.25rem;
    margin-bottom: -0.0625rem;
  }

  .time-spent-input {
    background-color: white;
    color: #495057;
  }

  .time-icon {
    z-index: 1;
  }

  .starting-time {
    z-index: map-get($zIndex, 'dropdown');
  }

  .mute-toggle {
    gap: 7.2rem;
  }

  .mute-toggle-update {
    gap: 3.3rem;
  }

  .tickets-toggle {
    width: 100%;
    align-items: center !important;
  }

  .time-spent-user-field {
    width: 20rem;
  }

  .time-start-dropdown {
    z-index: 6
  }

  .time-end-dropdown {
    z-index: 5
  }

  .custom-dropdown {
    position: absolute;
    top: -6.25rem;
    right: 0;
    z-index: 1000;
  }

  .dropdown-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    background: white;
    border: 0.0625rem solid #ddd;
    border-radius: 0.25rem;
    color: #333;
  }

  .dropdown-item:hover {
    background: #f0f0f0;
  }

  .flex-class {
    flex-grow: 1 !important;
    justify-content: right !important;
  }

  .option-btn {
    white-space: nowrap;
  }

  .modal-title {
    margin: 0;
  }

  .close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
  }

  .modal-body {
    margin-top: 5rem !important;
  }

  .bottom-class {
    align-items: flex-end;
    flex-direction: row;
    max-height: 2.125rem;
  }

  .bg-colors {
    background-color: $themed-lighter !important;
  }

  :deep(.sweet-content-content) {
    max-height: 35rem !important;
  }

  .dropdown-position {
    top: -18.5rem;
  }

  .dropdown-position-basic-access {
    top: -16rem;
  }

  .dropdown-menu-position {
    top: -17.5rem;
  }
  
  .dropdown-menu-position-basic-access {
    top: -15rem;
  }
</style>
