<template>
  <tr
    class="box--with-hover w-100 not-as-small"
    :class="{ 'row-selected': isSelected || isQuickViewTicket , 'font-weight-bold': isBold }"
    data-tc="ticket table"
    @click.stop.prevent="routeOrSelect"
  >
    <td
      v-for="(preference, index) in selectedColumns"
      :key="`ticket-row-data-${index}`"
      :class="{ 'table-data--sticky': isSticky(preference.title) }"
      :data-tc-ticket="index"
    >
      <ticket-badge
        v-if="preference.name == 'ticket_number'"
        class="badge--top-left"
        :ticket="ticket"
      />
      <ticket-row-data
        :ticket="ticket"
        :preference="preference"
        :selected-tickets="selectedTickets"
        :is-insights="isInsights"
        @input="checkRequiredFields"
        @select="selectTicket"
      />
    </td>
  </tr>
</template>

<script>
  import { mapGetters } from 'vuex';
  import tableLayoutStyle from 'mixins/table_layout_style';
  import ticketHelper  from 'mixins/ticket_helper';
  import { managementToCompany } from 'mixins/msp_helper';
  import TicketRowData from './ticket_row_data.vue';
  import TicketBadge from './ticket_badge.vue';

  export default {
    components: {
      TicketRowData,
      TicketBadge,
    },
    mixins: [tableLayoutStyle, ticketHelper],
    props: ['ticket', 'selectedTickets', 'selectedColumns', 'isInsights', 'isQuickView', 'isQuickViewTicket'],
    data() {
      return {
        listTableLayoutModuleStyle: "help_tickets",
      };
    },
    computed: {
      ...mapGetters('GlobalStore', ['currentCompanyUser']),
      isSelected() {
        return this.selectedTickets.find(({id}) => this.ticket.id === id );
      },
    },
    methods: {
      routeOrSelect(e) {
        let isModalOpen = null;
        const ticketUrl = `/help_tickets/${this.ticket.id}`;
        if (document.getElementById('helpTicketCloseModal')) {
          isModalOpen = document.getElementById('helpTicketCloseModal').classList.contains('is-visible');
        }
        this.handleSwitchToCompanyView();
        const elements = ['multiselect__tags', 'multiselect__select'];

        if (e.shiftKey) {
          this.selectTicket();
        } else if (e.ctrlKey || e.metaKey) {
          window.open(ticketUrl, '_blank');
        } else if (isModalOpen || elements.includes(e.srcElement.className)) {
          return;
        } else if (this.ticket.company && this.ticket.company.id !== getCompanyFromStorage().id) {
            this.$emit('child-ticket-opener', this.ticket);
        } else if (this.ticket.workspaceId && this.ticket.workspaceId !== getWorkspaceFromStorage().id) {
          if (this.isQuickView) {
            this.$emit('open-quick-view', this.ticket.id);
            return;
          }
          const url = `/help_tickets/${this.ticket.id}?workspace_id=${this.ticket.workspaceId}`;
          window.open(url, '_blank');
        } else if (this.isInsights) {
          window.open(ticketUrl, '_blank');
        } else if (this.isQuickView) {
          this.$emit('open-quick-view', this.ticket.id);
        } else {
          this.$router.push({ path: `/${this.ticket.id}` });
        }
      },
      selectTicket() {
        this.$emit('select-ticket', this.ticket);
      },
      checkRequiredFields(params) {
        this.$emit('input', params);
      },
      handleSwitchToCompanyView() {
        managementToCompany();
      },
    },
  };
</script>
