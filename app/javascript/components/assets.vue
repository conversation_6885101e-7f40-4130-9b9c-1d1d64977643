<template>
  <div ref="assets">
    <module-header
      module="Assets"
      :show-header="showHeader"
      :dashboard-class="dashboardClass"
      :prop-is-write-any="isWriteAny"
      :prop-is-read-any="isReadAny"
    >
      <template #menu="{type}">
        <module-vertical-nav
          module="assets"
          :type="type"
          :nav-links="navLinks"
          :routes-look-up="routesLookUp"
          :placeholder="placeholder"
          @on-permissions-click="goToAssetsPermissions"
        />
      </template>
      <template #horizontal>
        <module-menu @on-permissions-click="goToAssetsPermissions" />
      </template>
      <template #loader>
        <span
          v-if="assetsLoading"
          class="ml-3 d-inline-block"
        >
          <pulse-loader
            color="#0d6efd"
            size="0.5rem"
            loading
          />
        </span>
      </template>
      <template #right-nav>
        <div v-tooltip.bottom="tooltipMessage">
          <dashboard-actions
            :is-assets-analytics="isAssetsAnalytics"
            :class="disableLinks"
            @download-asset-data="downloadAssetData"
          />
        </div>
      </template>
      <template #content>
        <router-view />
      </template>
      <template #additional-content>
        <Teleport to="body">
          <notifications position="bottom right" />
        </Teleport>
        <module-onboarding-template
          v-if="isWriteAny || $isSampleCompany"
          ref="onboarding"
          :tip-card-info="tipCardInfo"
          :step="step"
          @step="moveToStep"
          @close-walkthrough="onCloseWalkthrough"
        />
      </template>
    </module-header>

    <sweet-modal
      ref="staffInModule"
      v-sweet-esc
      title="People with Assets module permissions"
      width="50%"
      modal-theme="dark-header theme-centered-title"
    >
      <template slot="default">
        <module-users
          v-if="loadData"
          :load-data="loadData"
          mod-name="assets"
          permission-name="ManagedAsset"
        />
      </template>
    </sweet-modal>
    <export-modal 
      ref="exportModal"
      is-assets-module
    />
    <downloading-popup 
      v-if="isDownloadAsset" 
      @close-widget="closeDownloadingWidget" 
    />
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapGetters, mapMutations, mapActions } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import ModuleUsers from "components/shared/group_permissions/module_users.vue";
  import assetsOnboardingMixin from 'mixins/module_onboardings/managed_assets';
  import assetHelper from 'mixins/assets/asset_helper';
  import assetsNavbarHelper from 'mixins/assets/navbar_helper';
  import ModuleHeader from "components/shared/module_header.vue";
  import ModuleOnboardingTemplate from 'components/shared/module_onboarding_template.vue';
  import ExportModal from 'components/shared/ticket_export_modal.vue';
  import DownloadingPopup from 'components/assets_downloading_popup.vue';
  import ModuleVerticalNav from './shared/module_vertical_nav.vue';
  import ModuleMenu from './assets/module_menu.vue';
  import DashboardActions from './assets/dashboard_actions.vue';
  import modernViewsHelper from '../mixins/modern_views_helper';

  export default {
    components: {
      ModuleHeader,
      ModuleVerticalNav,
      ModuleMenu,
      ModuleOnboardingTemplate,
      DashboardActions,
      SweetModal,
      ModuleUsers,
      ExportModal,
      PulseLoader,
      DownloadingPopup,
    },
    mixins: [
      assetHelper,
      assetsNavbarHelper,
      assetsOnboardingMixin,
      modernViewsHelper,
    ],
    data() {
      return {
        loadData: false,
      };
    },
    computed: {
      ...mapGetters(['successMessage', 'assetsLoading', 'displayRiskCenterSection']),
      ...mapGetters('GlobalStore', ['errorMessage', 'downloadingAssetReports', 'isDownloadAsset']),
      dashboardClass() {
        return this.$route.name === "dashboard" && window.innerWidth < 900;
      },
      showHeader() {
        return (
          !(['new', 'edit'].includes(this.$route.name)) &&
          !/^\/\d+$/.test(this.$route.fullPath) &&
          !/denied$/.test(this.$route.fullPath)
        );
      },
      isAssetsAnalytics() {
        return this.$route.path === "/analytics";
      },
    },
    watch: {
      errorMessage() { this.emitError(this.errorMessage); },
      successMessage() {
        if (this.successMessage !== '') {
          this.emitSuccess(this.successMessage);
          this.setSuccessMessage('');
        }
      },
      routesLookUp: {
        handler() {
          this.setupPusherListener();
        },
        deep: true,
      },
    },
    methods: {
      ...mapMutations(['setSuccessMessage']),
      ...mapMutations('GlobalStore', ['updateDownloadingAssetReports', 'setIsDownloadAsset']),
      ...mapActions('GlobalStore', ['setupPusherListener', 'checkReportProgess']),
      ...mapActions(['shouldDisplayRiskCenter']),
      async onWorkspaceChange() {
        this.applyDefaultVerticalNav();
        this.shouldDisplayRiskCenter();
        this.viewTypeFromCookie();
        await this.assetPreferencesCall();
        this.$store.dispatch("fetchPermissions");
        if (!this.isAssetsAnalytics) {
          this.$store.dispatch('fetchDiscoveredAssetsSummary');
        }
        this.$store.dispatch('fetchAppsLocationSummary');
        this.$store.dispatch('checkIfUnseenDiscoveryLocations');
        this.checkReportProgess(this.$currentCompanyUserId);
        this.setupPusherListener();
      },
      closeDownloadingWidget() {
        this.setIsDownloadAsset(false);
      },
      goToAssetsPermissions() {
        this.loadData = true;
        this.$refs.staffInModule.open();
      },
      navigateToImportAssetsData() {
        this.$router.push("/import_assets");
      },
      downloadAssetData(type) {
        const params =  type ? { is_people_assets: true } : {};
        http
          .get('/managed_assets/export', { params })
          .then(() => {
            this.$refs.exportModal.open();
            setTimeout(()=> {
              this.setIsDownloadAsset(true);
            }, 1000);
          })
          .catch(() => {
            this.emitError("Something went wrong. Please refresh the page and try again.");
          });
      },
    },
  };
</script>
