export default {
  computed: {
    ticketSubject() {
      return this.activity.activityLabel;
    },
    taskId() {
      if (this.activity.data.taskId) {
        return this.activity.data.taskId;
      }
      return "";
    },
    assetAutomatedTask() {
      return this.activity.data.managedAssetId;
    },
    taskSerialNumber() {
      return this.activity.data.taskSerialNumber || "";
    },
    viaAutomatedTask() {
      if (this.taskId && this.isWrite) {
        const taskUrl = this.assetAutomatedTask
          ? `/managed_assets/automated_tasks?taskId=${this.taskId}&edit=true`
          : `/help_tickets/automated_tasks?taskId=${this.taskId}&edit=true`;

        return `via automated task# <a href="${taskUrl}" target="_blank">${this.taskSerialNumber ? this.taskSerialNumber : this.taskId}</a>`;
      }
      else if (this.taskId) {
        return `via automated task# <b>${this.taskSerialNumber ? this.taskSerialNumber : this.taskId}</b>`;
      }
      return "";
    },
    viaMergedTicket() {
      let data = this.activity.data.mergedTicketData;
      if (data) {
        data = JSON.parse(data);
        return `via merged ticket# <a href=/help_tickets/${data.id}>${data.ticket_number}</a>`;
      }
      return "";
    },
    currentValueDisplay() {
      if (this.isSmartListItem || this.activity.activityType === "external_user") {
        if (this.activity.data.currentName) {
          return this.activity.data.currentName;
        }
        return this.activity.data.currentValue;
      } else if (this.currentValue) {
        if (this.activity.activityType === "date") {
          return moment(this.currentValue).format("MMM DD, YYYY");
        } else if (this.activity.activityType === "phone") {
          return String(this.currentValue).split(',')[1];
        }
        return this.currentValue
          .replace(/^\s*[\r\n]/gm, "")
          .replace(/(<head[\w\W]+head>)/g, "")
          .replace(/<\/?[^>]+>/gi, " ")
          .trim();
      }
      return "An item";
    },
    previousValueDisplay() {
      if (this.isSmartListItem || this.activity.activityType === "external_user") {
        if (this.activity.data.previousName) {
          return this.activity.data.previousName;
        }
        return this.activity.data.previousValue;
      } else if (this.previousValue) {
        if (this.activity.activityType === "date") {
          return moment(this.previousValue).format("MMM DD, YYYY");
        } else if (this.activity.activityType === "phone") {
          return String(this.previousValue).split(',')[1];
        }
        return this.previousValue
          .replace(/^\s*[\r\n]/gm, "")
          .replace(/(<style[\w\W]+style>)/g, "")
          .replace(/<\/?[^>]+>/gi, " ")
          .trim();
      }
      return "An item";
    },
  },
  methods: {
    itemBody(itemData) {
      let htmlObject = document.createElement("div");
      htmlObject.innerHTML = itemData;
      const links = htmlObject.getElementsByTagName("a");
      if (links.length > 0) {
        this.getIcon(links);
      }
      // Image upload removes attachments but this is a
      //  backup in case the image HTML didn't delete
      const images = htmlObject.getElementsByTagName("img");
      images.forEach((img) => {
        img.setAttribute(
          "onerror",
          "this.onerror=null; this.style.display='none'"
        );
      });
      htmlObject = htmlObject.outerHTML;
      return htmlObject;
    },
  },
};
