require 'malloc_trim'

class Integrations::Azure::SaveUsageTransactionsWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb
  include AzureCommonMethods

  sidekiq_options queue: 'integrations'

  def perform(config_id, subscriptions, vendor_id, product_id, first_time_flag, next_link, current_index=0)
    @starting_time = Time.current
    set_read_replica_db do
      @azure_config = Integrations::Azure::Config.find_by(id: config_id)
      @company = @azure_config.company
      @company_integration = @azure_config.company_integration
    end
    @first_time_flag = first_time_flag

    if subscriptions.any?
      # No need to load association proxy - we'll use direct model queries
      @company_integration_id = @company_integration.id
      @vendor_id = vendor_id
      @product_id = product_id

      current_subscription = subscriptions[current_index]
      refresh_access_token
      response, next_url = azure_client.get_subscription_usage(current_subscription['id'], next_link)
      pusher(@azure_config, true, 0.4)
      save_usage_transactions(response) if response.present?

      if ((next_url.nil? || next_url.include?('None')) && current_index === subscriptions.size - 1)
        finalize_sync
      elsif (next_url.present?)
        Integrations::Azure::SaveUsageTransactionsWorker.perform_async(@azure_config.id, subscriptions, @vendor_id, @product_id, @first_time_flag, next_url, current_index)
      else
        Integrations::Azure::SaveUsageTransactionsWorker.perform_async(@azure_config.id, subscriptions, @vendor_id, @product_id, @first_time_flag, nil, current_index + 1)
      end
      intg_duration_analysis(@company.id, @first_time_flag)
    else
      finalize_sync
      intg_duration_analysis(@company.id, @first_time_flag)
    end
  rescue Exception => e
    @company_integration.assign_attributes(sync_status: :failed,
                                           status: false,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: e.message,
                                           user_error_message: e.message)

    @company_integration.save!

    error_messages = [
      "invalid_grant",
      "Access token has expired.",
      "Access token validation failure.",
      "Access token has expired or is not yet valid.",
      "Signing key is invalid.",
      "Your access token has expired. Please renew it before submitting the request.",
      "The security token included in the request is invalid.",
      "Authentication failed."
    ]

    if !@first_time_flag && error_messages.find{ |em| e.message.downcase.include?(em.downcase) }
      send_notification(@company_integration)
    end

    azure_client.api_logs.each do |log_param|
      LogCreationWorker.perform_async('Logs::ApiEvent', log_param.to_json)
    end

    Rails.logger.error(e)

    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find { |em| e.message.downcase.include?(em.downcase) }
    end
    pusher(@azure_config, false, 0) if @first_time_flag
  ensure
    GC.start
    MallocTrim.trim
  end

  def save_usage_transactions(response)
    return if response.blank?

    start_time = Time.current
    Rails.logger.info "[AZURE WORKER OPTIMIZED] Processing #{response.size} usage records for company_id: #{@company.id}"

    # Process each usage record individually but efficiently
    response.each do |usage|
      next unless usage.present? && cost_present_or_not_zero?(usage)

      transaction_date = usage["properties"]["date"]
      service_name = usage["properties"]["consumedService"]
      amount = transaction_amount(usage)

      # Use direct model query instead of association
      cloud_usage_transaction = set_read_replica_db do
        CloudUsageTransaction.find_or_initialize_by(
          company_id: @company.id,
          transaction_date: transaction_date,
          name: service_name,
          company_integration_id: @company_integration_id,
          is_manual: false
        )
      end

      cloud_usage_transaction.assign_attributes(
        vendor_id: @vendor_id,
        product_id: @product_id,
        amount: amount
      )

      cloud_usage_transaction.save!
    end

    end_time = Time.current
    Rails.logger.info "[AZURE WORKER OPTIMIZED] Completed processing in #{((end_time - start_time) * 1000).round(2)}ms"
  end

  private

  def process_usage_batch(valid_usages)
    # Extract unique dates and names for lookup
    dates = valid_usages.pluck(:transaction_date).uniq
    names = valid_usages.pluck(:name).uniq

    # Single query to find existing records
    existing_records = set_read_replica_db do
      CloudUsageTransaction.where(
        company_id: @company.id,
        company_integration_id: @company_integration_id,
        is_manual: false,
        transaction_date: dates
      ).where(name: names)
       .index_by { |record| [record.transaction_date, record.name] }
    end

    # Prepare records for batch operations
    records_to_update = []
    records_to_create = []

    valid_usages.each do |usage|
      lookup_key = [usage[:transaction_date], usage[:name]]
      existing_record = existing_records[lookup_key]

      if existing_record
        # Update existing record
        existing_record.assign_attributes(
          vendor_id: @vendor_id,
          product_id: @product_id,
          amount: usage[:amount]
        )
        records_to_update << existing_record if existing_record.changed?
      else
        # Create new record
        records_to_create << CloudUsageTransaction.new(
          company_id: @company.id,
          transaction_date: usage[:transaction_date],
          name: usage[:name],
          company_integration_id: @company_integration_id,
          is_manual: false,
          vendor_id: @vendor_id,
          product_id: @product_id,
          amount: usage[:amount]
        )
      end
    end

    # Batch operations
    perform_batch_operations(records_to_create, records_to_update)
  end

  def perform_batch_operations(records_to_create, records_to_update)
    Rails.logger.info "[AZURE WORKER OPTIMIZED] Batch operations: #{records_to_create.size} creates, #{records_to_update.size} updates"

    batch_start = Time.current

    # Try using upsert_all for maximum efficiency (Rails 6.1+)
    if use_upsert_optimization?
      perform_upsert_operations(records_to_create, records_to_update)
    else
      perform_traditional_batch_operations(records_to_create, records_to_update)
    end

    batch_end = Time.current
    Rails.logger.info "[AZURE WORKER OPTIMIZED] Batch operations completed in #{((batch_end - batch_start) * 1000).round(2)}ms"
  end

  def use_upsert_optimization?
    # Check if Rails version supports upsert_all and database supports it
    CloudUsageTransaction.respond_to?(:upsert_all) &&
    ActiveRecord::Base.connection.adapter_name.downcase.include?('postgresql')
  end

  def perform_upsert_operations(records_to_create, records_to_update)
    all_records = (records_to_create + records_to_update).map do |record|
      record.attributes.merge(
        'created_at' => Time.current,
        'updated_at' => Time.current
      )
    end

    if all_records.any?
      CloudUsageTransaction.upsert_all(
        all_records,
        unique_by: [:company_id, :transaction_date, :name, :company_integration_id, :is_manual],
        returning: false
      )
      Rails.logger.info "[AZURE WORKER OPTIMIZED] Upserted #{all_records.size} records in single operation"
    end
  end

  def perform_traditional_batch_operations(records_to_create, records_to_update)
    ActiveRecord::Base.transaction do
      # Batch insert new records
      if records_to_create.any?
        CloudUsageTransaction.insert_all(
          records_to_create.map(&:attributes),
          returning: false
        )
        Rails.logger.info "[AZURE WORKER OPTIMIZED] Batch inserted #{records_to_create.size} records"
      end

      # Batch update existing records
      if records_to_update.any?
        records_to_update.each(&:save!)
        Rails.logger.info "[AZURE WORKER OPTIMIZED] Updated #{records_to_update.size} records"
      end
    end
  end

  def cost_present_or_not_zero?(usage)
    (usage["properties"]["costInBillingCurrency"] && usage["properties"]["costInBillingCurrency"] != 0) || 
    (usage["properties"]["cost"] && usage["properties"]["cost"] != 0)
  end

  def transaction_amount(usage)
    usage["properties"]["costInBillingCurrency"].presence || usage["properties"]["cost"]
  end

  def azure_client
    @azure_service ||= Integrations::Azure::FetchData.new(@company.id, @first_time_flag)
  end

  def finalize_sync
    pusher(@azure_config, true, 0.8)

    @company_integration.assign_attributes(sync_status: :successful,
                                           status: true,
                                           last_synced_at: DateTime.now,
                                           active: true,
                                           error_message: nil,
                                           notified: nil)
    @company_integration.save!

    pusher(@azure_config, true, 1) if @first_time_flag
  end
end
