require 'malloc_trim'

class Integrations::Azure::SaveUsageTransactionsWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb
  include AzureCommonMethods

  sidekiq_options queue: 'integrations'

  def perform(config_id, subscriptions, vendor_id, product_id, first_time_flag, next_link, current_index=0)
    @starting_time = Time.current
    initialize_config(config_id)
    @first_time_flag = first_time_flag
    @vendor_id = vendor_id
    @product_id = product_id

    if subscriptions.any?
      process_subscription(subscriptions, current_index, next_link)
    else
      finalize_sync
    end

    intg_duration_analysis(@company.id, @first_time_flag)
  rescue Exception => e
    handle_sync_error(e)
  ensure
    GC.start
    MallocTrim.trim
  end

  def save_usage_transactions(response)
    return if response.blank?

    response.each do |usage|
      next unless usage.present? && cost_present_or_not_zero?(usage)

      cloud_usage_transaction = set_read_replica_db do
        CloudUsageTransaction.find_or_initialize_by(
          company_id: @company.id,
          transaction_date: usage["properties"]["date"],
          name: usage["properties"]["consumedService"],
          company_integration_id: @company_integration_id,
          is_manual: false
        )
      end

      cloud_usage_transaction.assign_attributes(
        vendor_id: @vendor_id,
        product_id: @product_id,
        amount: transaction_amount(usage)
      )

      cloud_usage_transaction.save!
    end
  end

  private

  def initialize_config(config_id)
    set_read_replica_db do
      @azure_config = Integrations::Azure::Config.find_by(id: config_id)
      @company = @azure_config.company
      @company_integration = @azure_config.company_integration
      @company_integration_id = @company_integration.id
    end
  end

  def process_subscription(subscriptions, current_index, next_link)
    current_subscription = subscriptions[current_index]
    refresh_access_token
    response, next_url = azure_client.get_subscription_usage(current_subscription['id'], next_link)
    pusher(@azure_config, true, 0.4)
    save_usage_transactions(response) if response.present?

    if last_subscription?(next_url, current_index, subscriptions.size)
      finalize_sync
    elsif next_url.present?
      schedule_next_batch(subscriptions, current_index, next_url)
    else
      schedule_next_subscription(subscriptions, current_index)
    end
  end

  def last_subscription?(next_url, current_index, total_subscriptions)
    (next_url.nil? || next_url.include?('None')) && current_index == total_subscriptions - 1
  end

  def schedule_next_batch(subscriptions, current_index, next_url)
    self.class.perform_async(@azure_config.id, subscriptions, @vendor_id, @product_id, @first_time_flag, next_url, current_index)
  end

  def schedule_next_subscription(subscriptions, current_index)
    self.class.perform_async(@azure_config.id, subscriptions, @vendor_id, @product_id, @first_time_flag, nil, current_index + 1)
  end

  def cost_present_or_not_zero?(usage)
    properties = usage["properties"]
    (properties["costInBillingCurrency"]&.nonzero?) || (properties["cost"]&.nonzero?)
  end

  def transaction_amount(usage)
    properties = usage["properties"]
    properties["costInBillingCurrency"].presence || properties["cost"]
  end

  def azure_client
    @azure_service ||= Integrations::Azure::FetchData.new(@company.id, @first_time_flag)
  end

  def finalize_sync
    pusher(@azure_config, true, 0.8)
    @company_integration.update!(
      sync_status: :successful,
      status: true,
      last_synced_at: DateTime.now,
      active: true,
      error_message: nil,
      notified: nil
    )
    pusher(@azure_config, true, 1) if @first_time_flag
  end

  def handle_sync_error(error)
    @company_integration.update!(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: error.message,
      user_error_message: error.message
    )

    auth_error_messages = %w[
      invalid_grant
      Access\ token\ has\ expired
      Access\ token\ validation\ failure
      Access\ token\ has\ expired\ or\ is\ not\ yet\ valid
      Signing\ key\ is\ invalid
      Your\ access\ token\ has\ expired
      The\ security\ token\ included\ in\ the\ request\ is\ invalid
      Authentication\ failed
    ]

    is_auth_error = auth_error_messages.any? { |msg| error.message.downcase.include?(msg.downcase) }

    send_notification(@company_integration) if !@first_time_flag && is_auth_error

    azure_client.api_logs.each { |log| LogCreationWorker.perform_async('Logs::ApiEvent', log.to_json) }

    Rails.logger.error(error)
    Bugsnag.notify(error) if (Rails.env.production? || Rails.env.staging?) && !is_auth_error
    pusher(@azure_config, false, 0) if @first_time_flag
  end
end
