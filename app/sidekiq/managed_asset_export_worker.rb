class ManagedAssetExportWorker
  include Sidekiq::Job
  sidekiq_options queue: 'critical'

  def perform(company_id, company_user_id, analytics_params, is_people_assets = false)
    @is_people_assets = is_people_assets
    company = Company.find_by_cache(id: company_id)
    options = @is_people_assets ? { is_people_assets: is_people_assets } : {}
    workbook = ImportExport::Assets::XlsExport.new(company, options, analytics_params).export
    data = {
      company: company,
      company_user_id: company_user_id,
    }
    @file_excel = nil
    @file_excel_path = ''

    track_id = SecureRandom.uuid
    timestamp = Time.now.to_i
    file_name = is_people_assets ? "people_in_assets.xlsx" : "managed_assets.xlsx"

    trigger_pusher(company.guid, file_name, track_id, 0.2, '', timestamp)

    report = CustomReport.create(
      name: file_name,
      report_content_type: "xlsx",
      company_id: company.id,
      creator_id: company_user_id,
      module_name: "managed_assets",
      track_id: track_id,
      percentage: 0.2
    )
    excel_file = save_report(data, workbook, report.id)
    s3_url = upload_to_s3(@file_excel, company)

    trigger_pusher(company.guid, file_name, track_id, 1.0, s3_url, timestamp)

    report.update(
      percentage: 1.0,
      download_link: s3_url,
      ready_to_download: true
    )

    ReportsMailer.send_reports_mail(company_user_id, company.id, excel_file, true).deliver_now
  end

  def trigger_pusher(company_guid, file_name, track_id, percentage, file_url, timestamp)
    Pusher.trigger(
      company_guid,
      'download-asset-excel',
      {
        excelFile: file_url,
        report: {
          trackId: track_id,
          name: file_name,
          percentage: percentage,
          timestamp: timestamp
        }
      }
    )
  end

  def save_report(data, file, report_id)
    temp_file_name = "#{data[:company].subdomain}_managed_assets_data.xlsx"
    file_path = Rails.root.join("tmp", temp_file_name)
    File.open(file_path, "wb") do |f|
      f << file.stream.string
    end

    new_file = File.open(file_path)
    @file_excel = new_file
    @file_excel_path = file_path

    report_name = "#{data[:company].subdomain}_managed_assets_data.xlsx"
    
    report = CustomReport.find(report_id)
    report.update(
      report: { io: new_file, filename: File.basename(new_file.path) },
      report_file_name: report_name
    )

    report
  end

  def upload_to_s3(file, company)
    s3 = Aws::S3::Resource.new(
      region: Rails.application.credentials.aws[:s3][:region],
      access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )

    file_name = @is_people_assets ? 'people_in_assets.xlsx' : 'managed_assets.xlsx'
    obj = s3.bucket(Rails.application.credentials.aws[:s3][:bucket]).object(file_name)
    
    begin
      obj.put(body: file, acl: 'public-read')
      File.delete(@file_excel_path) if File.exist?(@file_excel_path)
      obj.public_url
    rescue StandardError => e
      Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    end
  end
end
