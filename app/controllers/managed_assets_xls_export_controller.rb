# To export managed assets data in Xls format from header menu
class ManagedAssetsXlsExportController < BaseXlsController
  set_privilege_name("ManagedAsset")

  def export_class
    ImportExport::Assets::XlsExport
  end

  def export_name
    "assets"
  end

  def index
    ManagedAssetExportWorker.perform_async(scoped_company.id, scoped_company_user.id, params['assets_analytics_export'], params['is_people_assets'] == 'true')
    render json: {}
  end
end
