# Main file for data mapping of modules in respective formats
require 'rubyXL'

module ImportExport
  class XlsExportBase
    attr_accessor :company, :workbook,:time_frame ,:options, :new_mapping, :type_mapping_manipulated

    def initialize(company, options={}, filters_data=nil, type_mapping={}, workspace=nil, is_reporting_module=nil)
      @company = company
      @filters_data = filters_data unless filters_data.nil?
      @options = options if options.present?
      @new_mapping = type_mapping if type_mapping.present?
      @report_module = @options.with_indifferent_access[:module] if options.present?
      @type_mapping_manipulated = {}
      @workspace = workspace
      @calendar_export = options[:calendar_export] || false
      @selected_view = options[:selected_view]
      @query_params = options[:query_params]
      @is_reporting_module = is_reporting_module
      @is_people_assets = options[:is_people_assets]
    end

    # Using this method to create Xlsx file and then add data in the sheets
    def export
      @workbook = RubyXL::Workbook.new
      @workbook.worksheets.shift
      @company_asset_types = company.asset_types
      @type_mapping_manipulated = filtered_type_mapping
      if is_asset_module?
        known_types = AssetType.pluck("name").compact.map(&:downcase)
        @company_asset_types.each do |asset_type|
          if !known_types.include?(asset_type.xls_friendly_name.downcase)
            @type_mapping_manipulated["#{asset_type.xls_friendly_name}"] = :other
          end
        end
      end

      @type_mapping_manipulated.keys.each { |type| to_xlsx(type) }
      @workbook
    end

    def import_type(resource)
      return  @type_mapping_manipulated[people_assignment_index(resource)] if @is_people_assets
      @type_mapping_manipulated[type_index(resource)]
    end

    def is_required?(column)
      required.include?(column) if !@is_people_assets
    end

    def create_presenter(resource)
      return ImportExport::Assets::Base.new(resource) if @is_people_assets
  
      import_class(import_type(resource)).new(resource)
    end

    def is_company_user_status_field?
      return options[:company_module] == 'company_user' && options[:status]
    end

    # Map column names for custom entity, time spent and others
    def columns_names(type = nil, detail_type, worksheet)
      if self.class.to_s.downcase.include?("customentity") && !custom_report?
        column_names = get_column_names(type)&.pluck(:name)
        if options[:class].present? && extra_columns[options[:company_module].to_sym].present?
          if is_company_user_status_field?
            column_names.push(extra_columns[options[:company_module].to_sym][:name]) 
          elsif options[:company_module] != 'company_user'
            column_names.unshift(extra_columns[options[:company_module].to_sym][:name]) 
          end
        end
      elsif self.class.to_s.downcase.include?("timespent")
        column_names = @options[:attributes][:time_spent]
      else
        column_names =  if detail_type == :help_ticket
                          attributes[detail_type].map do |field|
                            [field[0]&.humanize&.titleize, field[1]&.humanize&.titleize]
                          end
                        elsif @is_people_assets
                          people_attributes.transform_keys(&:to_sym)[detail_type]
                        else
                          columns.transform_keys(&:to_sym)[detail_type]
                        end
      end

      column_names&.each_with_index do |column, index|
          if detail_type == :help_ticket
            cell_value = @is_reporting_module ? column[1] : column[0]
          else
            cell_value = column
          end
        worksheet.add_cell(0, index, cell_value)
        worksheet.change_column_width(index, 25)
        is_required_column = detail_type == :help_ticket ? column[0] : column
        worksheet[0][index].change_font_bold(true) if is_required?(is_required_column)
      end

      if detail_type == :help_ticket
        help_ticket_column_names = column_names.map { |column| column[0] }
        help_ticket_column_names
      else
        column_names
      end
    end

    # In this method, we fetch the data on the basis of id/values and populate them in rows
    def populate_excel_data_rows(type)
      work_sheet_name = @calendar_export ? "Contracts" : type&.gsub(/[\W\s]+/, ' ').squish.to_s.titleize
      if self.class.to_s.downcase.include?("assets")
        work_sheet_name += "+" if work_sheet_name == '2000'
      end
      worksheet = workbook.add_worksheet(work_sheet_name)
      detail_type = @type_mapping_manipulated[type]
      column_names = columns_names(type, detail_type, worksheet)
      contract_types = ['open_ended', 'fixed_term']
      if self.class.to_s.downcase.include?("customentity")
        fields = get_column_names(type)
        attribute_types = fields&.pluck(:field_attribute_type)
        ids = fields&.pluck(:id)
        fields = fields&.pluck(:name)
        extra_cols = extra_columns[options[:company_module].to_sym][:name] if options[:class].present? && extra_columns[options[:company_module].to_sym].present?
        if extra_cols.present?
          if is_company_user_status_field?
            fields&.push(extra_cols)
            ids&.push(extra_cols)
            attribute_types&.push(extra_cols)
          elsif options[:company_module] != 'company_user'
            fields&.unshift(extra_cols)
            ids&.unshift(extra_cols)
            attribute_types&.unshift(extra_cols)
          end
        end
        if options[:class] == "CompanyUser" && !resources(type).is_a?(Array)
          items = resources(type).includes(:user, :custom_form_values)
        else
          items = resources(type)
        end
      else
        fields = @is_people_assets ? people_attributes[detail_type] : attributes[detail_type]
        items = if @calendar_export
                  filter_contracts(@calendar_export, @selected_view, @query_params, options[:contract_type])
                else
                  @is_people_assets ? people_resources(type) : resources
                end
      end
      row_idx = 0
      items.each_slice(1000) do |resources_batch|
        resources_batch.each do |resource|
          asset_type = @is_people_assets ? people_assignment_index(resource) : type_index(resource)
          if asset_type == type || self.class.to_s.downcase.include?("customentity") || contract_types.include?(asset_type)
            row_idx = row_idx + 1
            presenter = create_presenter(resource)
            column_names.size.times do |idx|
              if is_date_field fields[idx].downcase
                current_cell = worksheet.add_cell(row_idx, idx)
                current_cell.set_number_format('yyyy-mm-dd')
                if self.class.to_s.downcase.include?("customentity")
                  current_cell.change_contents(date_value_cell(fetchField(resource, fields[idx], attribute_types[idx], ids[idx]), fields[idx].downcase))
                else
                  current_cell.change_contents(date_value_cell(presenter.send(fields[idx]), fields[idx].downcase))
                end
              else
                if self.class.to_s.downcase.include?("customentity")
                  fetched_value = fetchField(resource, fields[idx], attribute_types[idx], ids[idx])
                  value = is_numeric_value(fetched_value) ? fetched_value.to_f : fetched_value
                  value = value.remove_unicode_control_codes if value.is_a?(String) && value.include_unicode_control_codes?
                  worksheet.add_cell(row_idx, idx, value)
                else
                  value = presenter.send(fields[idx])
                  if fields[idx] == "mfa_enabled" && [true, false].include?(value)
                    value = value ? "Yes" : "No"
                  end
                  value = value.join(', ') if value.is_a?(Array)
                  value = value.remove_unicode_control_codes if value.is_a?(String) && value.include_unicode_control_codes?
                  if is_currency_field fields[idx].downcase
                    current_cell = worksheet.add_cell(row_idx, idx, value)
                    current_cell.set_number_format('$0.00')
                  else
                    worksheet.add_cell(row_idx, idx, value)
                  end
                end
              end
            end
          end
        end
      end
    end

    def date_value_cell(value, column_name)
      if column_name != "alert_dates_formatted"
        value&.to_date rescue nil
      else
        value
      end
    end

    def to_xlsx(type)
      populate_excel_data_rows(type)
    end

    # In this method, we have already fetched the data and just need to map and populate in the rows
    def query_based_data_report(type, excel_worksheet = nil)
      worksheet = excel_worksheet.present? ? excel_worksheet : workbook.add_worksheet(type.to_s.titleize)
      detail_type = @type_mapping_manipulated[type]
      column_names = columns_names(detail_type, worksheet)
      if detail_type == :help_ticket
        attribute_helptickets = attributes[detail_type]
        fields = attribute_helptickets.map { |column| column[0] }
      else
        fields = attributes[detail_type]
      end
      row_idx = 0

      field_attribute_types = []
      field_attribute_types = columns["help_ticket"].pluck(:name) if columns["help_ticket"]
      resource_values = type == 'TimeSpent' ? @options.with_indifferent_access[:data].values.first : @options.with_indifferent_access[:data].values
      resource_values.each_slice(1000) do |resources_batch|
        resources_batch.each do |resource|
          next unless type_index(resource) == type

          row_idx += 1
          column_names.size.times do |idx|
            fields[idx] = fields[idx][0] !~ /\D/ ? '_' + fields[idx] : fields[idx] if @report_module == "help_ticket"
            if is_date_field(fields[idx].downcase) || is_date_type_field(field_attribute_types[idx])
              current_cell = worksheet.add_cell(row_idx, idx)
              row_data = is_date_type_field(field_attribute_types[idx]) ? convert_to_date(resource[fields[idx]]) : convert_to_date_time(resource[fields[idx]])
              current_cell.set_number_format('m/d/yy HH:MM')
              value = is_numeric_value(row_data) ? row_data.to_f : row_data
              current_cell.change_contents(value)
            else
              if field_attribute_types[idx] == "people_list" 
                row_data = email_or_name(resource[fields[idx]])
              else
                row_data = resource[fields[idx]]
              end
              value = is_numeric_value(row_data) ? row_data.to_f : row_data
              worksheet.add_cell(row_idx, idx, value)
            end
          end
        end
      end
    end

    def is_date_field(field)
      field == "warranty_expiration_formatted" || is_current_module_and_date_field(field)
    end

    def is_current_module_and_date_field(field)
      check_current_module && (field["date"] || field["created_at"] || field["updated_at"] || field["date_created"] || field["date_opened"] || field["date_closed"])
    end

    def check_current_module
      current_module = self.class.to_s.downcase
      if current_module["contracts"] || current_module["generaltransactions"]
        return true
      elsif options.present? && options[:company_module].present? && ["location", "helpdesk", "companyusers"].include?(options[:company_module])
        return true
      else
        return false
      end
    end

    def convert_to_date_time(value)
      value&.to_datetime rescue nil
    end

    def convert_to_date(value)
      value&.to_date rescue nil
    end

    def is_date_type_field(field_type)
      return (check_current_module) && field_type == "date"
    end

    def is_currency_field(field)
      return (field["purchase_price"] || field["monthly_cost"] || field["amount"] || field["contract_value_amount"] || field["salvage"])
    end

    def is_numeric_value(value)
      return value.to_s.match(/\A-?(?:\d+(?:\.\d*)?|\.\d+)\z/)
    end

    def custom_report?
      ["vendor", "contract", "help_ticket"].include?(@report_module)
    end

    def is_asset_module?
      @is_asset_module ||= self.class.to_s.downcase.include?("assets")
    end

    def is_contracts_module?
      @is_contracts_module ||= self.class.to_s.downcase.include?('contracts')
    end

    def is_transactions_module?
      @is_transactions_module ||= self.class.to_s.downcase.include?('generaltransactions')
    end

    def is_system_details_module?
      @is_system_details_module ||= self.class.to_s.downcase.include?('systemdetails')
    end

    def filtered_type_mapping
      return type_mapping.filter { |key, value| key.underscore == options[:system_detail_category] } if is_system_details_module?

      return type_mapping.dup unless is_asset_module?

      return type_mapping[options[:contract_type]] if is_contracts_module?

      return type_mapping[options[:transaction_type]] if is_transactions_module?

      return people_type_mapping if @is_people_assets

      active_asset_types = @company_asset_types.map(&:xls_friendly_name)
      type_mapping.filter { |key, value| active_asset_types.include?(key) }
    end
  
    def email_or_name(data)
      return data if @is_reporting_module
      
      data.split.last
    end

    def company_contributors
      @company_contributors ||= company.contributors
    end
  
    def company_locations
      @company_locations ||= company.locations.to_h { |loc| [loc.id, loc.name] }
    end
  
    def company_managed_assets
      @company_managed_assets ||= company.managed_assets.pluck(:id, :name).to_h
    end
  
    def company_contracts
      @company_contracts ||= company.contracts.pluck(:id, :name).to_h
    end
  
    def company_vendors
      @company_vendors ||= company.vendors.pluck(:id, :name).to_h
    end
  
    def company_telecom_services
      @company_telecom_services ||= company.telecom_services.pluck(:id, :name).to_h
    end
  end
end
