module ImportExport::Assets::Shared
  TYPE_MAPPING = {
    "Laptop" => :computer,
    "Desktop" => :computer,
    "Server" => :computer,
    "Apple Device" => :computer,
    "Windows" => :computer,
    "Phone" => :phone,
    "Mobile" => :mobile,
    "Iphone" => :mobile,
    "Ipad" => :mobile,
    "Router" => :networking_device,
    "Switch" => :networking_device,
    "Firewall" => :networking_device,
    "Tablet" => :mobile,
    "Printer" => :printer,
    "Virtual Machine" => :virtual_machine,
    "Thin Client" => :virtual_machine,
    "Phone System" => :phone,
    "Wap" => :networking_device,
    "Dongle" => :other,
    "Tv" => :other,
    "Other" => :other,
    "Unrecognized" => :other,
  }

  PEOPLE_TYPE_MAPPING = {
    "Assigned" => :assigned,
    "Unassigned" => :unassigned,
    "Archived" => :archived,
  }

  COLUMNS = {
    computer: %w{ name asset_type asset_tag processor memory hard_drive serial_number product_number mac_addresses ip_address brand model operating_system tags notes
                  used_by managed_by location department product_key acquisition_date warranty_expiration install_date alert_dates purchase_price salvage depreciation status po vendor},
    phone: %w{ name asset_type asset_tag connection_interface serial_number product_number mac_addresses ip_address brand model operating_system tags notes
               used_by managed_by location department acquisition_date warranty_expiration install_date alert_dates purchase_price salvage depreciation status po vendor},
    mobile: %w{ name asset_type asset_tag imei serial_number product_number mac_addresses ip_address brand model operating_system tags notes
                used_by managed_by location department acquisition_date warranty_expiration install_date alert_dates purchase_price salvage depreciation status po vendor},
    networking_device:%w{ name asset_type asset_tag ports serial_number product_number mac_addresses ip_address brand model operating_system tags notes
                          used_by managed_by location department acquisition_date warranty_expiration install_date alert_dates purchase_price salvage depreciation status po vendor},
    printer: %w{ name asset_type asset_tag serial_number product_number mac_addresses ip_address brand model operating_system tags notes
                 used_by managed_by location department acquisition_date warranty_expiration install_date alert_dates purchase_price salvage depreciation status po vendor},
    other: %w{ name asset_type asset_tag serial_number product_number mac_addresses ip_address brand model operating_system tags notes
               used_by managed_by location department acquisition_date warranty_expiration install_date alert_dates purchase_price salvage depreciation status po vendor},
    virtual_machine: %w{ name asset_type asset_tag processor memory hard_drive serial_number product_number mac_addresses ip_address brand model operating_system tags notes
                         used_by managed_by location department product_key acquisition_date warranty_expiration install_date alert_dates purchase_price salvage depreciation status po vendor},
  }

  PEOPLE_COLUMNS = {
    assigned: %w{ name used_by managed_by status sources tags location asset_user_accounts },
    unassigned: %w{ name used_by managed_by status sources tags location asset_user_accounts },
    archived: %w{ name used_by managed_by status sources tags location asset_user_accounts },
  }

  PEOPLE_ATTRIBUTES = {
    assigned: %w{ name used_by_email managed_by_email status sources tags location_name last_check_in },
    unassigned: %w{ name used_by_email managed_by_email status sources tags location_name last_check_in },
    archived: %w{ name used_by_email managed_by_email status sources tags location_name last_check_in },
  }

  ATTRIBUTES = {
    computer: %w{ name type_name asset_tag processor memory hard_drive serial_number product_number mac_addresses ip_address manufacturer model operating_system
                  tags description used_by_email managed_by_email location_name department os_product_key acquisition_date_formatted warranty_expiration_formatted os_install_date alert_dates_formatted purchase_price salvage depreciation status po vendor},

    phone: %w{ name type_name asset_tag connection_interface serial_number product_number mac_addresses ip_address manufacturer model operating_system
               tags description used_by_email managed_by_email location_name department acquisition_date_formatted warranty_expiration_formatted os_install_date alert_dates_formatted purchase_price salvage depreciation status po vendor},
    
    mobile: %w{ name type_name asset_tag imei serial_number product_number mac_addresses ip_address manufacturer model operating_system
                tags description used_by_email managed_by_email location_name department acquisition_date_formatted warranty_expiration_formatted os_install_date alert_dates_formatted purchase_price salvage depreciation status po vendor},

    networking_device: %w{ name type_name asset_tag ports serial_number product_number mac_addresses ip_address manufacturer model operating_system
                           tags description used_by_email managed_by_email location_name department acquisition_date_formatted warranty_expiration_formatted os_install_date alert_dates_formatted purchase_price salvage depreciation status po vendor},

    printer: %w{ name type_name asset_tag serial_number product_number mac_addresses ip_address manufacturer model operating_system
                 tags description used_by_email managed_by_email location_name department acquisition_date_formatted warranty_expiration_formatted os_install_date alert_dates_formatted purchase_price salvage depreciation status po vendor},

    other: %w{ name type_name asset_tag serial_number product_number mac_addresses ip_address manufacturer model operating_system
               tags description used_by_email managed_by_email location_name department acquisition_date_formatted warranty_expiration_formatted os_install_date alert_dates_formatted purchase_price salvage depreciation status po vendor},

    virtual_machine: %w{ name type_name asset_tag processor memory hard_drive serial_number product_number mac_addresses ip_address manufacturer model operating_system
                         tags description used_by_email managed_by_email location_name department os_product_key acquisition_date_formatted warranty_expiration_formatted os_install_date alert_dates_formatted purchase_price salvage depreciation status po vendor},
  }

  REQUIRED = %w{ name asset_type }

  def import_class(my_type)
    my_class = my_type.to_s.titleize.gsub(/\s+/, "")
    "ImportExport::Assets::#{my_class}".constantize
  end

  def sample_values
    []
  end
end
