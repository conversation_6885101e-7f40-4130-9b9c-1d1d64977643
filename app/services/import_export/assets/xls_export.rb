# Using this service to export data for managed assets in Xlsx format
require 'rubyXL'

class ImportExport::Assets::XlsExport < ImportExport::XlsExportBase
  include ::ImportExport::Assets::Shared
  include ::ImportExport::ConstantHelper
  include ::ImportExport::PeopleAssetsHelper
  include MemoryStorageClause
  include ReadReplicaDb

  def resources
    @resources ||= load_base_resources

    return @resources unless is_assets_analytics

    @resources = apply_analytics_filters unless @resources.is_a?(Array)

    @resources
  end

  def people_resources(type)
    @type = type
    @resources = load_people_resources
    @resources
  end

  def load_people_resources
    people_assets.includes(
      :location,
      :assignment_information,
      :asset_user_accounts,
      :custom_status
    )
  end

  def load_base_resources
    company_assets.includes(
      :asset_type,
      :location,
      :vendor,
      :asset_softwares,
      :hardware_detail,
      :assignment_information,
      :custom_status
    )
  end

  def apply_analytics_filters
    @resources = @resources.where(archived: false)
    apply_manufacturer_filter
    apply_location_filter
    apply_asset_type_filter
    apply_asset_status_filter
    apply_integration_source_filter
    apply_warranty_filter
    apply_device_encryption_filter
    apply_memory_filter
    apply_storage_filter
    apply_installed_software_filter
    apply_not_installed_software_filter
    apply_operating_system_filter
    @resources
  end

  def apply_manufacturer_filter
    return unless should_apply_filter('fetch_all_manufacturer_data', 'analytics_asset_manufacturers')

    if apply_all_data_filter('fetch_all_manufacturer_data')
      @resources = @resources.where("manufacturer IN (?)", company_assets_manufacturers)
    else
      @resources = @resources.where("manufacturer IN (?)", applied_analytics_filter['analytics_asset_manufacturers'])
    end
  end

  def apply_location_filter
    return unless should_apply_filter('fetch_all_locations_data', 'analytics_location_filter')

    if apply_all_data_filter('fetch_all_locations_data')
      @resources = @resources.where("managed_assets.location_id IN (?)", company_location_ids)
    else
      @resources = @resources.where("managed_assets.location_id IN (?)", applied_analytics_filter['analytics_location_filter'])
    end
  end

  def apply_asset_type_filter
    return unless should_apply_filter('fetch_all_asset_type_data', 'analytics_asset_types')

    if apply_all_data_filter('fetch_all_asset_type_data')
      @resources = @resources.where("company_asset_type_id IN (?)", company_asset_types_ids)
    else
      @resources = @resources.where("company_asset_type_id IN (?)", applied_analytics_filter['analytics_asset_types'])
    end
  end

  def apply_asset_status_filter
    return unless should_apply_filter('fetch_all_status_data', 'analytics_availability_status')

    if apply_all_data_filter('fetch_all_status_data')
      @resources = @resources.where("company_asset_status_id IN (?)", company_asset_statuses_ids)
    else
      @resources = @resources.where("company_asset_status_id IN (?)", applied_analytics_filter['analytics_availability_status'])
    end
  end

  def apply_integration_source_filter
    return unless should_apply_filter('fetch_all_sources_data', 'analytics_integration_source')

    @resources = @resources.joins(ManagedAssets::AssetsQuery::QueryConstants::ASSET_SOURCES_JOIN)

    if apply_all_data_filter('fetch_all_sources_data')
      integration_sources = AssetSource.sources.reject { |source, value| %w[manually_added uploaded].include?(source) }
      source_clause = integration_sources.map { |source, value| "asset_sources.source = #{value}" }
    else
      source_clause = applied_analytics_filter['analytics_integration_source'].map do |source|
        "asset_sources.source = #{AssetSource.sources[source]}"
      end
    end

    @source_clause = source_clause.join(" OR ") if source_clause&.any?
    @resources = @resources.where(@source_clause)
  end

  def apply_warranty_filter
    return unless should_apply_filter('fetch_all_warranty_data', 'analytics_asset_warranty')

    if apply_all_data_filter('fetch_all_warranty_data')
      warranty_filters = ['expired_warranty', 'expiring_warranty', 'in_warranty', 'no_warranty']
      warranty_clauses = warranty_filters.map { |warranty| generate_warranty_clause(warranty) }
    else
      warranty_clauses = applied_analytics_filter['analytics_asset_warranty'].map { |status| generate_warranty_clause(status) }
    end

    @warranty_clause = warranty_clauses.join(" OR ")
    @resources = @resources.where(@warranty_clause)
  end

  def apply_device_encryption_filter
    return unless should_apply_filter('fetch_all_device_encryption_data', 'analytics_device_encryption')

    @resources = @resources.joins(ManagedAssets::AssetsQuery::QueryConstants::COMPUTER_DETAILS_JOIN)

    if apply_all_data_filter('fetch_all_device_encryption_data')
      @resources = @resources.where("computer_details.disk_encryption IN (?)", ['On', 'Off'])
    else
      encryption_status = applied_analytics_filter['analytics_device_encryption'][0] # Always has one value, either On or Off
      @resources = @resources.where("computer_details.disk_encryption = ?", encryption_status)
    end
  end

  def apply_memory_filter
    return unless should_apply_filter('fetch_all_memory_data', 'analytics_memory_filter')

    @resources = @resources.joins(ManagedAssets::AssetsQuery::QueryConstants::COMPUTER_DETAILS_JOIN)

    memory_filters = if apply_all_data_filter('fetch_all_memory_data')
                        ["lessThan16", "16GB_32GB", "32GB_64GB", "greaterThan64"]
                     else
                        applied_analytics_filter['analytics_memory_filter']
                     end

    memory_clauses = memory_filters.map { |memory| generate_memory_clause(memory) }
    @memory_clause = "#{memory_clauses.join(" OR ")}" if memory_clauses&.any?
    @resources = @resources.where(@memory_clause)
  end

  def apply_storage_filter
    return unless should_apply_filter('fetch_all_storage_data', 'analytics_storage_filter')

    @resources = @resources.joins(ManagedAssets::AssetsQuery::QueryConstants::COMPUTER_DETAILS_JOIN)

    storage_filters = if apply_all_data_filter('fetch_all_storage_data')
                        ["lessThan256", "256GB_512GB", "512GB_1024GB", "greaterThan1024"]
                      else
                        applied_analytics_filter['analytics_storage_filter']
                      end

    storage_condition = "computer_details.hard_drive != '' AND "
    storage_clauses = storage_filters.map { |storage| generate_storage_clause(storage) }
    @storage_clause = "#{storage_condition}#{storage_clauses.join(" OR ")}" if storage_clauses&.any?
    @resources = @resources.where(@storage_clause)
  end

  def apply_installed_software_filter
    return unless should_apply_filter('fetch_all_installed_software_data', 'analytics_installed_software')

    if apply_all_data_filter('fetch_all_installed_software_data')
      @resources = @resources.joins(ManagedAssets::AssetsQuery::QueryConstants::ASSET_SOFTWARE_JOIN)

      asset_software_names = set_read_replica_db do
        company_assets.joins(:asset_softwares)
                      .where(asset_softwares: { archived_at: nil })
                      .pluck('asset_softwares.name').uniq.compact_blank
      end
      @resources = @resources.where("asset_softwares.name IN (?)", asset_software_names).uniq
    else
      @resources = @resources.select do |asset|
        asset_software_names = set_read_replica_db { asset.asset_softwares.pluck(:name) }
        (applied_analytics_filter['analytics_installed_software'] & asset_software_names).present?
      end
    end
  end

  def apply_not_installed_software_filter
    return unless should_apply_filter('fetch_all_not_installed_software_data', 'analytics_not_installed_software')

    if apply_all_data_filter('fetch_all_not_installed_software_data')
      @resources = @resources.joins(ManagedAssets::AssetsQuery::QueryConstants::ASSET_SOFTWARE_JOIN)

      asset_software_names =  set_read_replica_db do
        company_assets.joins(:asset_softwares)
                      .where(asset_softwares: { archived_at: nil })
                      .pluck('asset_softwares.name').uniq.compact_blank
      end
      @resources = @resources.where("asset_softwares.name NOT IN (?) OR asset_softwares.id IS NULL", asset_software_names).uniq
    else
      @resources = @resources.reject do |asset|
        asset_software_names = set_read_replica_db { asset.asset_softwares.pluck(:name) }
        (applied_analytics_filter['analytics_not_installed_software'] & asset_software_names).present?
      end
    end
  end

  def apply_operating_system_filter
    return unless should_apply_filter('fetch_all_os_data', 'analytics_operating_system')

    @resources = @resources.joins(ManagedAssets::AssetsQuery::QueryConstants::OPERATING_SYSTEM_JOIN)

    if apply_all_data_filter('fetch_all_os_data')
      all_os_software_names = set_read_replica_db do
        company_assets.joins(:asset_softwares)
                      .where(asset_softwares: { archived_at: nil, software_type: 'Operating System' })
                      .pluck('asset_softwares.name').uniq.compact_blank
      end
      @resources = @resources.where("aso.name IN (?)", all_os_software_names).uniq
    else
      applied_analytics_filter&.dig('analytics_operating_system').present?
      @resources = @resources.where("aso.name IN (?)", applied_analytics_filter['analytics_operating_system']).uniq
    end
  end

  def type_index(asset)
    asset.asset_type&.xls_friendly_name
  end

  def people_assignment_index(asset)
    if asset.archived?
      'Archived'
    elsif asset.assignment_information&.used_by_contributor_id.present?
      'Assigned'
    else
      'Unassigned'
    end
  end

  def company_assets
    @company_assets ||= @company.managed_assets
  end

  def people_assets
    @scope ||= @company.managed_assets
    @company_assets = case @type
                      when 'Assigned'
                           @scope.joins(:assignment_information)
                              .where(archived: false)
                              .where.not(assignment_informations: { used_by_contributor_id: nil })
                      when 'Unassigned'
                           @scope.left_joins(:assignment_information)
                              .where(archived: false)
                              .where('assignment_informations.used_by_contributor_id IS NULL OR assignment_informations.id IS NULL')
                      when 'Archived'
                           @scope.where(archived: true)
                      else
                           @scope.where(archived: false)
                      end
  end

  def company_assets_manufacturers
    @company_assets_manufacturers ||= company_assets.pluck(:manufacturer).uniq.compact_blank
  end

  def company_location_ids
    @company_location_ids ||= @company.locations.pluck(:id)
  end

  def company_asset_types_ids
    @company_asset_types_ids ||= @company.asset_types.pluck(:id)
  end

  def company_asset_statuses_ids
    @company_asset_statuses_ids ||= @company.asset_statuses.pluck(:id)
  end

  def applied_analytics_filter
    parsed_analytics_params['analytics_filters'] if parsed_analytics_params
  end

  def is_assets_analytics
    parsed_analytics_params['analytics_export'] if parsed_analytics_params
  end

  def parsed_analytics_params
    return unless @filters_data.present?

    JSON.parse(@filters_data)
  end

  def generate_warranty_clause(status="no_warranty")
    warranty_status_lookup = {
      "expired_warranty" => "warranty_expiration <= '#{Date.today}'",
      "expiring_warranty" => "warranty_expiration > '#{Date.today}' AND warranty_expiration <= '#{3.months.from_now}'",
      "in_warranty" => "warranty_expiration > '#{3.months.from_now}'",
      "no_warranty" => "warranty_expiration IS NULL"
    }

    warranty_status_lookup[status]
  end

  def should_apply_filter(all_data, specific_filter)
    apply_all_data_filter(all_data) || applied_analytics_filter&.dig(specific_filter).present?
  end

  def apply_all_data_filter(all_data)
    applied_analytics_filter&.dig(all_data).present?
  end
end
