class ImportExport::Assets::Base
  include ::ImportExport::CommonBase
  include ReadReplicaDb
  include DiscoveredManagedAssetFinder

  attr_accessor :asset, :errors, :new_tags, :xlsx_params, :is_duplicate

  def initialize(asset)
    self.asset = asset
    self.new_tags = []
    @xlsx_params = {}
    @errors = ""
    @is_duplicate = false
  end

  def self.detail_class
    class_name = self.name.dup
    class_name["ImportExport::Assets::"] = ''
    "#{class_name}Detail".constantize
  end

  def self.import(company, obj)
    obj_mac_addresses = obj["mac_addresses"]
    obj_serial_number = obj["serial_number"]
    obj_name = obj["name"]

    Rails.logger.info "Serial No: #{obj_serial_number}"
    Rails.logger.info "Mac address: #{obj_mac_addresses}"
    Rails.logger.info "name: #{obj_name}"
    Rails.logger.info "name: #{company.subdomain}"

    display_name = obj_name.to_s.strip if obj_name.present?
    serial_number = obj_serial_number.to_s.strip if obj_serial_number.present?
    instance = self.new(nil)
    asset = instance.find_managed_asset(company, serial_number, [obj_mac_addresses], display_name, nil)

    if asset && asset.hardware_detail.class.name != detail_class.name
      asset.hardware_detail = detail_class.new unless detail_class.name === 'OtherDetail'
      asset.source ||= 'uploaded'
    end

    Rails.logger.info "Asset found with ID: #{asset.id}" if asset.present?
    asset ||= create_asset(company)
    check_import_source(asset)
    self.new(asset)
  end

  def self.check_import_source(asset)
    if !asset.sources.include?("uploaded")
      asset.source = "uploaded"
    end
  end

  def save_tags
    destroy_tags
    tags_to_add = new_tags - asset.tags

    tags_to_add.each do |tag|
      asset_tag = init_tag_with_company_asset_tag(tag)
      if !asset_tag.save
        errors = asset_tag.errors&.full_messages
      end
    end
  end

  def save_tags_without_audit
    destroy_tags
    tags_to_add = new_tags - asset.tags

    tags_to_add.each do |tag|
      asset_tag = init_tag_with_company_asset_tag(tag)
      if !asset_tag.save_without_auditing
        @errors += asset_tag.errors&.full_messages
      end
    end
  end

  def init_tag_with_company_asset_tag(tag)
    company_asset_tag = asset.company.asset_tags.find_or_create_by(name: tag&.downcase)
    ManagedAssetTag.new(
      company_asset_tag_id: company_asset_tag.id,
      managed_asset_id: asset.id
    )
  end

  def destroy_tags
    tags_to_destroy = asset.tags - new_tags
    asset.managed_asset_tags.joins(:company_asset_tag).where(company_asset_tag: { name: tags_to_destroy }).destroy_all if tags_to_destroy.present?
  end

  def save_everything
    asset_saved = asset.save
    if asset_saved
      add_source
      detail_saved = set_read_replica_db { asset.hardware_detail }&.save || true
      os = set_read_replica_db do
        asset.asset_softwares.find_by(software_type: 'Operating System')
      end
      os_saved = os.present? ? os.save : true
      assignment_saved = asset.assignment_information.present? ? asset.assignment_information.save : true

      if detail_saved &&
        os_saved &&
        assignment_saved
      else
        @errors += asset.hardware_detail&.errors&.full_messages
        @errors += first_asset_software&.errors&.full_messages
        @errors += asset.assignment_information&.errors&.full_messages
      end
    else
      @errors += asset.errors&.full_messages.join(",")
    end
  end

  def save
    return false unless valid?
    if asset.new_record?
      Rails.logger.info "Asset is a new record"
      asset.without_auditing do
        save_everything
        save_tags_without_audit
      end
    else
      Rails.logger.info "Asset is a duplicate record"
      self.is_duplicate = true
      save_everything
      save_tags
    end
    @errors.blank?
  end

  def save!
    asset.hardware_detail.save! if asset.hardware_detail.present?
    asset.asset_softwares.first.save!
    asset.assignment_information&.save!
    asset.save!
    add_source
  end

  def self.create_asset(company)
    my_asset = company.managed_assets.new
    my_asset.hardware_detail = detail_class.new unless detail_class.name === 'OtherDetail'
    my_asset.assignment_information = AssignmentInformation.new
    my_asset.source = 'uploaded'
    my_asset
  end

  def type_name
    asset.asset_type.name
  end

  def asset_type=(value)
    if value.present?
      xlsx_params[:typeName] = value
      asset_type = value.strip
      asset_type = 'WAP' if asset_type == 'Wap'
      asset.asset_type = company.asset_types.find_by(name: asset_type)
    end
    errors << "Asset type #{asset_type} not found" if asset.asset_type.blank?
  end

  def type_name=(value)
    if value.present?
      xlsx_params[:typeName] = value
      asset_type = value
      asset_type = 'WAP' if asset_type == 'Wap'
      asset.asset_type = company.asset_types.select{ |a| a.name == asset_type.strip }.first
    end
    errors << "Asset type #{asset_type} not found" if asset_type.present? && asset.asset_type.blank?
  end

  def os_product_key
    first_asset_software&.product_key rescue ''
  end

  def os_product_key=(value)
    xlsx_params[:osProductKey] = value
    if value.present?
      asset.asset_softwares << AssetSoftware.new(software_type: 'Operating System') if asset.asset_softwares&.blank?
      first_asset_software.product_key = value if value.present?
    end
  end

  def os_install_date
    asset.asset_softwares.select { |a| a.software_type == 'Operating System' }.first&.install_date&.to_date&.strftime("%m/%d/%Y") rescue ''
  end

  def os_install_date=(value)
    xlsx_params[:osInstallDate] = value
    if value.present?
      software = set_read_replica_db do
        asset.asset_softwares.operating_systems.first
      end
      if software.blank?
        software = AssetSoftware.new(software_type: 'Operating System')
        asset.asset_softwares << software
      end

      software.install_date = date_value(value)
      bad_format = software && software.install_date.nil? && value.present?
      errors << 'OS Install date must have format of month/day/year.' if bad_format
    end
  end

  def acquisition_date_formatted
    asset.acquisition_date.try(:strftime, "%m/%d/%Y")
  end

  def acquisition_date_formatted=(value)
    xlsx_params[:acquisitionDate] = value
    asset.acquisition_date = date_value(value.to_s)
    errors << "Acquisition date is incorrect" if asset.acquisition_date.nil? && value.present?
  end

  def warranty_expiration_formatted
    asset.warranty_expiration.try(:strftime, "%Y/%m/%d")
  end

  def warranty_expiration_formatted=(value)
    xlsx_params[:warrantyExpiration] = value
    asset.warranty_expiration = date_value(value.to_s)
    if asset.warranty_expiration.nil? && value.present?
      errors << "Warranty expiration date is incorrect. Please follow format of (mm/dd/yyyy) OR (yyyy-mm-dd)"
    end
  end

  def operating_system
    operating_systems = asset.asset_softwares.select { |a| a.software_type == 'Operating System' }
    operating_systems.pluck(:name).join(';')
  end

  def operating_system=(value)
    xlsx_params[:os] = value
    if value.present?
      asset.asset_softwares << AssetSoftware.new(software_type: 'Operating System') if asset.asset_softwares&.blank?
      first_asset_software.name = string_value(value)
    end
  end

  def applications
    application = set_read_replica_db do
      asset.asset_softwares.where(software_type: 'Application')&.pluck(:name)
    end
    application.join(';')
  end

  def department
    dep_id = asset.assignment_information&.department_id
    company_departments.find_by(id: dep_id)&.name if dep_id.present?
  end

  def vendor
    asset.vendor&.name
  end

  def vendor=(value)
    if value.present?
      vendor = asset.company.vendors.find_by('lower(name) = ?', value.to_s.downcase)

      if vendor.blank?
        vendor = asset.company.vendors.new(name: value.to_s)
        vendor.save!
      end

      xlsx_params[:vendor] = vendor.name
      asset.vendor = vendor
    end
  end

  def department=(value)
    department_id = company_departments.find_by("lower(name) = ?", value&.downcase)&.id
    xlsx_params[:department_id] = department_id
    asset.assignment_information ||= AssignmentInformation.new
    asset.assignment_information&.department_id = department_id if department_id.present?
  end

  def managed_by_email
    asset.assignment_information&.managed_by_email
  end

  def managed_by_email=(value)
    xlsx_params[:managedBy] = value
    if value.present?
      if (value =~ URI::MailTo::EMAIL_REGEXP) == 0
        user = User.find_by_cache(email: value)
        if user
          company_user = CompanyUser.find_by_cache(user_id: user.id, company_id: company.id)
          if company_user
            asset.assignment_information&.manager = company_user&.contributor
          else
            errors << "Managed by user for #{value} does not exist"
          end
        else
          errors << "Managed by user for #{value} not found"
        end
      elsif
        group = company.groups.find_by(name: value)
        if group
          asset.assignment_information&.manager = group.contributor
        end
      else
        errors << "Managed by user for #{value} does not exist"
      end
      value
    end
  end

  def last_check_in
    asset&.asset_user_accounts&.pluck('last_login')&.compact&.max
  end

  def location_name
    asset.location&.name&.to_s&.strip
  end

  def status
    asset.custom_status&.name
  end

  def status=(value)
    if value.present?
      asset.custom_status = company.asset_statuses.find { |s| s.name == value }
      if asset.custom_status.nil?
        errors << "Status \"#{value}\" not found." if value.present? && asset.custom_status.blank?
      end
    else
      asset.custom_status = company.asset_statuses.find_by_name('In Use')
    end
  end

  def name
    asset.name
  end

  def name=(value)
    xlsx_params[:displayName] = string_value(value)
    if value.present?
      asset.name = value
    else
      errors << "name is required"
    end
  end

  def location_name=(value)
    if value.present?
      location = company.locations.includes(:custom_form_values)
                                  .joins(:custom_form_fields)
                                  .find_by(custom_form_fields: { name: 'name' },
                                           custom_form_values: { value_str: value.to_s.strip })
      if location
        xlsx_params[:location] = location.name
        asset.location = location
      else
        errors << "Location #{value} not found"
      end
    end
  end

  def description
    asset.description
  end

  def description=(value)
    xlsx_params[:description] = string_value(value)
    asset.description = string_value(value)
  end

  def used_by_email=(value)
    xlsx_params[:usedBy] = value
    if value.present?
      value = value.downcase
      if (value =~ URI::MailTo::EMAIL_REGEXP) == 0
        user = User.find_by_cache(email: value)
        if user
          company_user = CompanyUser.find_by_cache(user_id: user.id, company_id: company.id)
          if company_user
            asset.assignment_information&.user = company_user&.contributor
          else
            errors << "Used by user for #{value} not found"
          end
        else
          errors << "Used by user for #{value} not found"
        end
      elsif
        group = company.groups.find_by(name: value)
        if group
          asset.assignment_information&.user = group.contributor
        end
      else
        errors << "Used by user for #{value} not found"
      end
      value
    end
  end

  def used_by_email
    asset.assignment_information&.used_by_email
  end

  def serial_number
    asset.machine_serial_number
  end

  def serial_number=(value)
    xlsx_params[:serialNumber] = string_value(value)
    asset.machine_serial_number = string_value(value)
  end

  def asset_tag=(value)
    xlsx_params[:assetTag] = string_value(value)
    asset.asset_tag = string_value(value)
  end

  def tags
    if new_tags.blank? && asset.tags.present?
      CSV.generate_line(asset.tags).chomp
    else
      CSV.generate_line(new_tags).chomp
    end
  end

  def tags=(value)
    if value.blank?
      self.new_tags = []
    else
      tags_array = csv_array(value.to_s).compact_blank
      xlsx_params[:tags] = tags_array
      self.new_tags = tags_array
    end
  end

  def mac_addresses
    asset.mac_addresses&.join(" ")
  end

  def mac_addresses=(value)
    xlsx_params[:macAddresses] = space_array(value)
    asset.mac_addresses = space_array(value) || []
  end

  def ip_address
    asset.ip_address
  end

  def ip_address=(value)
    ip_address_regex = /(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])|(\/)([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])/i
    if value.present?
      if value =~ ip_address_regex
        xlsx_params[:ipAddress] = value
        asset.ip_address = string_value(value)
      else
        errors << "#{value} is not a valid ip address."
      end
    end
  end

  def purchase_price
    asset.cost&.purchase_price
  end

  def purchase_price=(value)
    xlsx_params[:purchase_price] = money_value(value)
    if asset.cost.blank?
      asset.build_cost(purchase_price: money_value(value))
    else
      asset.cost.purchase_price = money_value(value)
    end
  end

  def po
    asset.cost&.po
  end

  def po=(value)
    xlsx_params[:po] = value
    if asset.cost.blank?
      asset.build_cost(po: value)
    else
      asset.cost.po = value
    end
  end

  def salvage
    asset.cost&.salvage
  end

  def salvage=(value)
    xlsx_params[:salvage] = money_value(value)
    if asset.cost.blank?
      asset.build_cost(salvage: money_value(value))
    else
      asset.cost.salvage = money_value(value)
    end
  end

  def depreciation
    asset.depreciation&.name
  end

  def depreciation=(value)
    xlsx_params[:depreciation] = string_value(value)
    if value.present?
      dep = company.depreciations.find_by('lower(name) = ?', string_value(value).downcase)
      if dep.present?
        asset.cost ||= Cost.new
      else
        errors << "#{value} is not a valid depreciation."
      end
    end
  end

  def alert_dates_formatted
    alert_dates = asset.alert_dates.map do |alert|
      alert.date.to_date.strftime("%m/%d/%Y") rescue ''
    end
    alert_dates.compact_blank.join(";")
  end

  def first_asset_software
    @first_asset_software ||= set_read_replica_db do
      asset.asset_softwares&.first
    end
  end

  def alert_dates_formatted=(values)
    xlsx_params[:alertDates] = []
    invalid_alert_dates = []
    old_alert_dates = []
    passed_alert_dates = []
    if values.present?
      alert_dates = values.to_s.split(";")
      old_alert_dates = asset.alert_dates.pluck(:date) if !asset.new_record?
      alert_dates.each do |date|
        if !(xlsx_params[:alertDates].include?(date) || old_alert_dates.length > 0)
          xlsx_params[:alertDates] << date
          alert_date = date_value(date)
          if alert_date.nil?
            invalid_alert_dates << date
          elsif alert_date <= DateTime.now.in_time_zone(company.timezone)
            date = date.include?('-') ? Time.parse(date).strftime("%m/%d/%Y") : date
            passed_alert_dates << date
          else
            asset.alert_dates << AlertDate.new(date: alert_date)
          end
        end
      end
      errors << "Alert dates (#{invalid_alert_dates.join(", ")}) must have format of month/day/year." if invalid_alert_dates.length > 0
      errors << "Alert dates (#{passed_alert_dates.join(", ")}) can not be in the past." if passed_alert_dates.length > 0
    end
  end

  def method_missing(name, *args, &block)
    asset.send(name, *args, &block)
  end

  def add_source
    xlsx_params[:memory] = asset.hardware_detail.try(:memory)
    xlsx_params[:harddisk] = asset.hardware_detail.try(:hard_drive)
    xlsx_params[:processor] = asset.hardware_detail.try(:processor)
    xlsx_params[:imei] = asset.hardware_detail.try(:imei)
    xlsx_params[:ports] = asset.hardware_detail.try(:ports)
    xlsx_params[:connection_interface] = asset.hardware_detail.try(:connection_interface)

    asset_source = AssetSource.find_or_initialize_by(managed_asset_id: asset.id, source: asset.source)
    asset_source.asset_data = xlsx_params
    asset_source.managed_asset_id = asset.id
    asset_source.save!
  end

  def sample_asset_tags=(value)
    tags_array = value.split(";")
    if tags_array.present?
      tags_array.each do |tag|
        company_asset_tag = CompanyAssetTag.find_or_create_by(name: tag.to_s.downcase, company_id: asset.company_id)
        asset.managed_asset_tags << ManagedAssetTag.new(company_asset_tag_id: company_asset_tag.id)
      end
    end
  end

  def sample_operating_system=(value)
    xlsx_params[:operatingSystem] = value
    asset.asset_softwares << AssetSoftware.new(software_type: 'Operating System') if asset.asset_softwares&.blank?
    first_asset_software.name = value
    asset.operating_system = value
  end

  def sample_used_by=(value)
    company_user = User.new(email: value).company_users.new
    company_user.contributor = Contributor.new
    asset.assignment_information&.user = company_user.contributor
  end

  def sample_managed_by=(value)
    company_user = User.new(email: value).company_users.new
    company_user.contributor = Contributor.new
    asset.assignment_information&.manager = company_user.contributor
  end

  def sample_depreciation=(value)
    asset.depreciation = value
  end

  def company_departments
    @company_departments ||= company.departments
  end
end
