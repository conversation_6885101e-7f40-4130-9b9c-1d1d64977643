module AssetAutomatedTasks
  module Concerns
    module Activity
      def create_recepients_activity(label, recipients)
        return unless @event_data.present?

        event_type = @event_data['event_type']
        assets =  case event_type
                  when "{an asset's} disk space is low"
                    @event_data['low_disk_space_assets']
                  when "{a software} wasn't detected"
                    @event_data['software_not_detected_assets']
                  when "{an agent} didn't resync"
                    find_assets_for_not_resynced_agents(@event_data['not_resynced_agents'])
                  else
                    []
                  end

        return if assets.blank?

        assets.each do |asset|
          data = {
            activity_label: label,
            recipients_data: recipients.join(', '),
            managed_asset_id: asset['id'].to_s,
            source: :auto_generated,
            task_id: @action.automated_task_id.to_s,
            task_serial_number: @action.automated_task.serial_number.to_s
          }

          if @event_data['event_type'] == "{a software} wasn't detected"
            data[:softwares] = @event_data['softwares'].join(', ')
          end

          ManagedAssetActivity.create!(
            managed_asset_id: asset['id'],
            activity_type: :notification,
            data: data
          )
        end
      end

      def recipients_target_name
        {
          contributors: "Specific People or Groups",
          specified: "Specific People or Groups",
          admins: "All Admins"
        }
      end

      def find_assets_for_not_resynced_agents(not_resynced_agents)
        return [] if not_resynced_agents.blank?

        not_resynced_agents.map do |agent_info|
          managed_asset = ManagedAsset.find_by(agent_location_id: agent_info['id'])
          next unless managed_asset

          { "id" => managed_asset.id, "name" => managed_asset.name }
        end.compact
      end
    end
  end
end
