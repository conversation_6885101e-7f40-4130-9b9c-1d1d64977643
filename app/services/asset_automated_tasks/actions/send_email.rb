module AssetAutomatedTasks
  module Actions
    class SendEmail
      include ContributorOptionsHelper
      include Utilities::Domains
      include AssetAutomatedTasks::Concerns::Activity
    
      def initialize(action, object, event_data)
        @action = action
        @object = object
        @event_data = event_data
      end

      def call
        TaskActionMailer.action_email(@action, @object, recipients, email_data).deliver_now!
        create_recepients_activity("Email", @recipient_names.uniq)
      end

      def email_data
        base_body = action_data['body']
        event_type = @event_data["event_type"]
      
        email_body_with_data = 
          case event_type
          when "{an integration} failed continuously"
            integrations = @event_data["failed_integrations"].map { |i| "<p>#{i}</p>" }.join(", ")
            "#{base_body}<strong>Failed Integration(s):</strong>#{integrations}"
        
          when "{a software} wasn't detected"
            softwares = @event_data["softwares"].map { |s| "<p>#{s}</p>" }.join(", ")
            assets = @event_data["software_not_detected_assets"].map do |asset|
              "<a href='#{build_company_url(@action.company)}managed_assets/#{asset["id"]}'>#{asset["name"]}</a>"
            end.join("<br>")
            "#{base_body}<strong>Software(s):</strong>#{softwares}<strong>Asset(s):</strong><br>#{assets}"
        
          when "{an agent} didn't resync"
            agents = @event_data["not_resynced_agents"].map do |agent|
              "<p>#{agent["computer_name"]} (#{agent["os"]})</p>"
            end.join("<br>")
            "#{base_body}<strong>Agent(s) that didn't resync:</strong>#{agents}"
        
          when "{an asset's} disk space is low"
            assets = @event_data["low_disk_space_assets"].map do |asset|
              "<a href='#{build_company_url(@action.company)}managed_assets/#{asset["id"]}'>#{asset["name"]}</a>"
            end.join("<br>")
            "#{base_body}<strong>Asset(s) with low disk space:</strong><br>#{assets}"
      
          else
            base_body
          end
      
        {
          email_body: email_body_with_data,
          subject: action_data["subject"],
          from_email: "<EMAIL>",
          sanitize_email_body: email_body_with_data,
          helpdesk_custom_emails: []
        }.as_json
      end      

      def recipients
        targets = action_data['target'].split(',').map(&:strip)
        emails = []
        names = []
        
        targets.each do |target|
          case target
          when 'admins'
            emails += admin_emails
            names << recipients_target_name[:admins]
          when 'contributors'
            ids = action_data['contributors'].map { |contributor| contributor['id'] }
            emails += member_emails(ids)
            names << recipients_target_name[:contributors]
          end
        end
        @recipient_names = names
        return emails.uniq
      end

      def action_data
        @action_data ||= JSON.parse(@action.value)
      end

      def admin_emails
        @object.company.admin_company_users.where(out_of_office: false).joins(:user).pluck(:email)
      end
    end
  end
end
