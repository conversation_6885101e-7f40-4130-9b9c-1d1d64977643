module AssetAutomatedTasks
  module Actions
    class AddAlert
      include Utilities::Domains
      include AssetAutomatedTasks::Concerns::Activity
      
      def initialize(action, object, event_data)
        @action = action
        @object = object
        @event_data = event_data
      end

      def call
        recipients.each do |contributor_id|
          ContributorActionableAlert.create!(contributor_id: contributor_id, actionable_alert: alert)
        end
        create_recepients_activity("Alert", @recipient_names.uniq)
      end

      def alert
        @alert ||= ActionableAlert.create!(company_id: @object.company_id, message: message_body, link: "", workspace_id: nil, module: :asset)
      end

      def message_body
        base_message = action_data['message']
        event_type = @event_data["event_type"]
      
        case event_type
        when "{an integration} failed continuously"
          integrations = @event_data["failed_integrations"].map { |integration| "<p>#{integration}</p>" }.join(", ")
          "#{base_message}<strong>Failed Integration(s):</strong>#{integrations}"
      
        when "{a software} wasn't detected"
          softwares = @event_data["softwares"].map { |s| "<p>#{s}</p>" }.join(", ")
          assets = @event_data["software_not_detected_assets"].map do |asset|
            "<a href='#{build_company_url(@action.company)}managed_assets/#{asset["id"]}'>#{asset["name"]}</a>"
          end.join("<br>")
          "#{base_message}<strong>Software(s):</strong>#{softwares}<strong>Asset(s):</strong><br>#{assets}"
      
        when "{an agent} didn't resync"
          agents = @event_data["not_resynced_agents"].map do |agent|
            "<p>#{agent["computer_name"]} (#{agent["os"]})</p>"
          end.join("<br>")
          "#{base_message}<strong>Agent(s) that didn't resync:</strong>#{agents}"
      
        when "{an asset's} disk space is low"
          assets = @event_data["low_disk_space_assets"].map do |asset|
            "<a href='#{build_company_url(@action.company)}managed_assets/#{asset["id"]}'>#{asset["name"]}</a>"
          end.join("<br>")
          "#{base_message}<strong>Asset(s) with low disk space:</strong><br>#{assets}"
      
        else
          base_message
        end
      end

      def recipients
        targets = action_data['target'].split(',').map(&:strip)
        recipients_ids = []
        names = []

        targets.each do |target|
          case target
          when 'admins'
            recipients_ids += admin_ids
            names << recipients_target_name[:admins]
          when 'specified'
            ids = action_data['contributors'].map { |contributor| contributor['id'] }
            recipients_ids += specified_ids(ids)
            names << recipients_target_name[:specified]
          end
        end
        @recipient_names = names
        return recipients_ids.flatten.uniq
      end
      
      def action_data
        @action_data ||= JSON.parse(@action.value)
      end

      def admin_ids
        @object.company.admins.contributor.contributor_ids_only_users
      end

      def specified_ids(value_ints)
        Contributor.where(id: value_ints).flat_map { |con| con.contributor_ids_only_users unless con.guest }.compact
      end
    end
  end
end
