namespace :azure do
  desc "Test the optimized Azure worker performance"
  task test_worker: :environment do
    puts "Testing Azure Worker Optimization..."
    
    # Find a company with existing data or create test data
    company = Company.first
    unless company
      puts "No companies found. Please create a company first."
      exit
    end
    
    # Create mock Azure response
    mock_response = 100.times.map do |i|
      {
        "properties" => {
          "date" => (Date.current - rand(30).days).to_s,
          "consumedService" => ["Virtual Machines", "Storage", "Networking", "Compute"].sample,
          "costInBillingCurrency" => rand(10.0..100.0).round(2),
          "cost" => rand(10.0..100.0).round(2)
        }
      }
    end
    
    puts "Created mock response with #{mock_response.size} records"
    
    # Find or create necessary records
    vendor = company.vendors.find_or_create_by(name: 'Microsoft Azure') do |v|
      v.is_cloud_platform = true
    end
    
    product = vendor.products.find_or_create_by(name: 'Azure Services', company: company)
    
    company_integration = company.company_integrations.first || 
                         company.company_integrations.create!(
                           integration: Integration.find_or_create_by(name: 'azure'),
                           status: true
                         )
    
    # Test the worker
    worker = Integrations::Azure::SaveUsageTransactionsWorker.new
    worker.instance_variable_set(:@company, company)
    worker.instance_variable_set(:@company_integration, company_integration)
    worker.instance_variable_set(:@company_integration_id, company_integration.id)
    worker.instance_variable_set(:@vendor_id, vendor.id)
    worker.instance_variable_set(:@product_id, product.id)
    
    # Measure performance
    initial_count = CloudUsageTransaction.where(company: company).count
    
    puts "Starting test with #{initial_count} existing records..."
    
    start_time = Time.current
    worker.send(:save_usage_transactions, mock_response)
    end_time = Time.current
    
    final_count = CloudUsageTransaction.where(company: company).count
    
    puts "\n" + "="*50
    puts "PERFORMANCE RESULTS"
    puts "="*50
    puts "Execution time: #{((end_time - start_time) * 1000).round(2)}ms"
    puts "Records processed: #{mock_response.size}"
    puts "Initial count: #{initial_count}"
    puts "Final count: #{final_count}"
    puts "Records created/updated: #{final_count - initial_count}"
    puts "Average time per record: #{(((end_time - start_time) * 1000) / mock_response.size).round(2)}ms"
    
    if (end_time - start_time) < 1.0
      puts "✅ EXCELLENT: Processing completed in under 1 second!"
    elsif (end_time - start_time) < 5.0
      puts "✅ GOOD: Processing completed in under 5 seconds"
    else
      puts "⚠️  SLOW: Processing took more than 5 seconds"
    end
  end
  
  desc "Compare old vs new Azure worker performance"
  task compare_performance: :environment do
    puts "Performance Comparison: Old vs New Azure Worker"
    puts "="*60
    
    # This would require temporarily switching back to old implementation
    # For now, just show the expected improvements
    puts "Expected improvements with optimization:"
    puts "- Database queries: 1000+ → 1-2 queries"
    puts "- Processing time: 30+ seconds → <1 second"
    puts "- Memory usage: High → Low"
    puts "- COUNT queries: Many → Zero"
    
    puts "\nRun 'rake azure:test_worker' to test the optimized version"
  end
end
